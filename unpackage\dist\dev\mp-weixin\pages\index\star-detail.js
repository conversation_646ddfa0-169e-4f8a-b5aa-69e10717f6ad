"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      starId: "",
      starDetail: {},
      loading: true,
      // 处理后的富文本内容
      processedDetailContent: ""
      // 处理后的详细介绍内容
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/index/star-detail.vue:99", "科技之星详情页面加载参数:", options);
    if (options.starData) {
      try {
        this.starDetail = JSON.parse(decodeURIComponent(options.starData));
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:106", "⭐ 使用传递的科技之星数据:", this.starDetail);
        this.starId = this.starDetail.starId;
        common_vendor.index.setNavigationBarTitle({
          title: this.starDetail.name || "科技之星详情"
        });
        this.processRichTextContent();
        this.incrementViewCount();
        this.loading = false;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/star-detail.vue:124", "解析科技之星数据失败:", error);
        if (options.id) {
          this.starId = options.id;
          this.loadStarDetail();
        } else {
          this.showErrorAndGoBack("数据解析失败");
        }
      }
    } else if (options.id) {
      this.starId = options.id;
      this.loadStarDetail();
    } else {
      this.showErrorAndGoBack("缺少科技之星参数");
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return `天大海棠 - ${this.starDetail.name || "科技之星详情"}`;
    },
    // 自定义分享路径
    getSharePath() {
      return `/pages/index/star-detail?id=${this.starId}`;
    },
    // 自定义分享图片（使用科技之星的图片）
    getShareImageUrl() {
      return utils_imageUtils.processServerImageUrl(
        this.starDetail.middleImageUrl || this.starDetail.topImageUrl,
        utils_imageUtils.getImagePath("share-logo.png")
      );
    },
    // 自定义分享内容
    getShareContent() {
      return `科技之星详情分享 - ${this.starDetail.name || "科技之星"}`;
    },
    // 显示错误并返回
    showErrorAndGoBack(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 获取科技之星详情
    async loadStarDetail() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:183", "⭐ 开始获取科技之星详情，ID:", this.starId);
        const response = await utils_request.request.get(`/miniapp/techstar/detail/${this.starId}`);
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:186", "⭐ 科技之星详情响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.starDetail = response.data.data || {};
          common_vendor.index.__f__("log", "at pages/index/star-detail.vue:190", "⭐ 科技之星详情数据:", this.starDetail);
          common_vendor.index.setNavigationBarTitle({
            title: this.starDetail.name || "科技之星详情"
          });
          this.processRichTextContent();
          this.incrementViewCount();
        } else {
          common_vendor.index.__f__("log", "at pages/index/star-detail.vue:204", "⭐ ❌ 科技之星详情获取失败");
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "获取科技之星详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/star-detail.vue:211", "⭐ 获取科技之星详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 顶图加载错误处理
    onTopImageError() {
      common_vendor.index.__f__("log", "at pages/index/star-detail.vue:223", "顶图加载失败");
    },
    // 头像加载错误处理
    onAvatarError() {
      common_vendor.index.__f__("log", "at pages/index/star-detail.vue:228", "头像加载失败");
    },
    // 增加科技之星浏览次数
    async incrementViewCount() {
      var _a;
      if (!this.starId) {
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:234", "⭐ 没有starId，跳过增加浏览次数");
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:239", "⭐ 开始增加浏览次数，starId:", this.starId);
        const response = await utils_request.request.post(`/miniapp/techstar/incrementViewCount/${this.starId}`);
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:242", "⭐ 增加浏览次数响应:", response);
        if (response && response.data && response.data.code === 200) {
          common_vendor.index.__f__("log", "at pages/index/star-detail.vue:245", "⭐ ✅ 浏览次数增加成功");
          const currentViewCount = this.starDetail.viewCount || 0;
          this.starDetail.viewCount = currentViewCount + 1;
          common_vendor.index.__f__("log", "at pages/index/star-detail.vue:250", "⭐ 本地浏览量已更新为:", this.starDetail.viewCount);
        } else {
          common_vendor.index.__f__("log", "at pages/index/star-detail.vue:253", "⭐ ❌ 浏览次数增加失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/star-detail.vue:256", "⭐ 增加浏览次数失败:", error);
      }
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "暂无";
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/star-detail.vue:272", "日期格式化失败:", error);
        return dateString;
      }
    },
    // 处理富文本内容，确保图片样式正确
    processRichTextContent() {
      if (this.starDetail.detailIntroduction && this.starDetail.detailIntroduction.trim()) {
        this.processedDetailContent = this.processHtmlContent(this.starDetail.detailIntroduction);
        common_vendor.index.__f__("log", "at pages/index/star-detail.vue:282", "🖼️ 处理后的详细介绍内容:", this.processedDetailContent);
      }
    },
    // 格式化浏览量显示
    formatViewCount(count) {
      if (!count || count === 0)
        return "0";
      if (count >= 1e4) {
        return Math.floor(count / 1e3) / 10 + "万";
      }
      return count.toString();
    },
    // 处理HTML内容，为图片添加样式限制
    processHtmlContent(htmlContent) {
      if (!htmlContent) {
        return "";
      }
      common_vendor.index.__f__("log", "at pages/index/star-detail.vue:301", "🖼️ 原始HTML内容:", htmlContent);
      const processedContent = utils_imageUtils.processHtmlImageUrls(htmlContent);
      common_vendor.index.__f__("log", "at pages/index/star-detail.vue:306", "🖼️ 处理后的HTML内容:", processedContent);
      return processedContent;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.starDetail.topImageUrl
  }, $data.starDetail.topImageUrl ? {
    c: $options.processServerImageUrl($data.starDetail.topImageUrl),
    d: common_vendor.o((...args) => $options.onTopImageError && $options.onTopImageError(...args))
  } : {}, {
    e: $options.processServerImageUrl($data.starDetail.middleImageUrl, $options.getImagePath("avatar.png")),
    f: common_vendor.o((...args) => $options.onAvatarError && $options.onAvatarError(...args)),
    g: common_vendor.t($data.starDetail.middleName || $data.starDetail.name || "暂无名称"),
    h: common_vendor.t($options.formatViewCount($data.starDetail.viewCount || 0)),
    i: $data.starDetail.detailIntroduction && $data.starDetail.detailIntroduction.trim()
  }, $data.starDetail.detailIntroduction && $data.starDetail.detailIntroduction.trim() ? {
    j: $data.processedDetailContent
  } : {}, {
    k: !$data.loading && ($data.starDetail.address && $data.starDetail.address.trim() || $data.starDetail.email && $data.starDetail.email.trim())
  }, !$data.loading && ($data.starDetail.address && $data.starDetail.address.trim() || $data.starDetail.email && $data.starDetail.email.trim()) ? common_vendor.e({
    l: $data.starDetail.address && $data.starDetail.address.trim()
  }, $data.starDetail.address && $data.starDetail.address.trim() ? {
    m: $options.getImagePath("icon-b1.png"),
    n: common_vendor.t($data.starDetail.address)
  } : {}, {
    o: $data.starDetail.email && $data.starDetail.email.trim()
  }, $data.starDetail.email && $data.starDetail.email.trim() ? {
    p: $options.getImagePath("icon-b2.png"),
    q: common_vendor.t($data.starDetail.email)
  } : {}) : {}));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/star-detail.js.map
