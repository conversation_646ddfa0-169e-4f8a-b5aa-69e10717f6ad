
.container.data-v-68bf51f7 {
	min-height: 100vh;
	background: #c9ddfe;
}

/* 加载和空状态样式 */
.loading-container.data-v-68bf51f7, .empty-container.data-v-68bf51f7 {
	padding: 100rpx 40rpx;
	text-align: center;
}
.loading-text.data-v-68bf51f7, .empty-text.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #666;
}

/* 导航栏样式 */
.navbar.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 32rpx;
	background: #1E4BA6;
	color: white;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
}
.nav-left.data-v-68bf51f7 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.nav-back.data-v-68bf51f7 {
	font-size: 36rpx;
	font-weight: bold;
}
.nav-title.data-v-68bf51f7 {
	font-size: 36rpx;
	font-weight: 500;
}
.nav-right.data-v-68bf51f7 {
	width: 60rpx;
}

/* 步骤栏样式 */
.steps-container.data-v-68bf51f7 {
	margin-bottom: 40rpx;
}
.steps-wrapper.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}
.step-item.data-v-68bf51f7 {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
	z-index: 2;
}
.step-circle.data-v-68bf51f7 {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
	background: #E8E8E8;
	border: 3rpx solid #E8E8E8;
	transition: all 0.3s ease;
}
.step-item.completed .step-circle.data-v-68bf51f7 {
	background: #1E4BA6;
	border-color: #1E4BA6;
}
.step-item.active .step-circle.data-v-68bf51f7 {
	background: #4A90E2;
	border-color: #4A90E2;
	box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.2);
}
.step-number.data-v-68bf51f7 {
	font-size: 24rpx;
	font-weight: bold;
	color: #999;
}
.step-item.completed .step-number.data-v-68bf51f7,
.step-item.active .step-number.data-v-68bf51f7 {
	color: white;
}
.step-text.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	white-space: nowrap;
}
.step-item.completed .step-text.data-v-68bf51f7 {
	color: #1E4BA6;
	font-weight: 500;
}
.step-item.active .step-text.data-v-68bf51f7 {
	color: #4A90E2;
	font-weight: bold;
}
.step-lines.data-v-68bf51f7 {
	position: absolute;
	top: 30rpx;
	left: 0;
	right: 0;
	height: 4rpx;
	z-index: 1;
	display: flex;
}
.step-line.data-v-68bf51f7 {
	flex: 1;
	height: 4rpx;
	background: #E8E8E8;
	margin: 0 30rpx;
}
.step-line.completed.data-v-68bf51f7 {
	background: linear-gradient(90deg, #1E4BA6, #4A90E2);
}

/* 表单容器 */
.form-container.data-v-68bf51f7 {
	padding: 40rpx 32rpx;
}

/* 页面标题 */
.page-title.data-v-68bf51f7 {
	text-align: center;
	margin-bottom: 60rpx;
	position: relative;
}
.page-text.data-v-68bf51f7 {
	font-size: 48rpx;
	font-weight: bold;
	color: #1E4BA6;
	text-shadow: 0 2rpx 4rpx rgba(30, 75, 166, 0.1);
}


/* 区域样式 */
.section.data-v-68bf51f7 {
	background:linear-gradient(180deg, #e8f1fc, #f5f9fc);
	border-radius: 20rpx;
	padding: 40rpx 32rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(209, 223, 245, 1);
	border: 1px solid #fff;
}
.section-title.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 40rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #fff;
	position: relative;
}
.title-icon.data-v-68bf51f7 {
	width: 40rpx;
	height: 40rpx;
	margin-right: 16rpx;
}
.title-text.data-v-68bf51f7 {
	flex: 1;
}

/* 表单项样式 */
.form-item.data-v-68bf51f7 {
	margin-bottom: 40rpx;
}
.form-item.data-v-68bf51f7:last-child {
	margin-bottom: 0;
}
.label-row.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}
.label.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	flex: 1;
}
.label .required-star.data-v-68bf51f7 {
	color: #ff4757 !important;
	margin-right: 4rpx;
	font-weight: bold;
}
.hidden-tip.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #ff6b6b;
	background: #fff5f5;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid #ffebee;
	margin-left: 16rpx;
}
.required-tag.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #ff0000;
	margin-left: 8rpx;
}
.optional-tag.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #999;
	margin-left: 8rpx;
}
.char-count.data-v-68bf51f7 {
	font-size: 22rpx;
	color: #999;
	margin-left: auto;
}

/* 字段提示样式 */
.field-hint.data-v-68bf51f7 {
	margin-top: 8rpx;
}
.hint-text.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}
.input.data-v-68bf51f7 {
	width: 100%;
	height: 64rpx;
	background: #F8F9FA;
	border: 2rpx solid #E8E8E8;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333;
	box-sizing: border-box;
}
.input.data-v-68bf51f7:focus {
	border-color: #1E4BA6;
	background: #FFF;
}
.textarea.data-v-68bf51f7 {
	width: 100%;
	min-height: 120rpx;
	background: #F8F9FA;
	border: 2rpx solid #E8E8E8;
	border-radius: 12rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	color: #333;
	box-sizing: border-box;
	line-height: 1.5;
}
.textarea.data-v-68bf51f7:focus {
	border-color: #1E4BA6;
	background: #FFF;
}

/* 选择器样式 */
.picker-content.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 64rpx;
	background: #F8F9FA;
	border: 2rpx solid #E8E8E8;
	border-radius: 12rpx;
	padding: 0 24rpx;
	box-sizing: border-box;
	transition: all 0.3s ease;
}
.picker-content.data-v-68bf51f7:active {
	background: #FFF;
	border-color: #1E4BA6;
	transform: scale(0.98);
}
.picker-text.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}
.picker-arrow.data-v-68bf51f7 {
	font-size: 24rpx;
	color: #999;
	transform: rotate(90deg);
	transition: transform 0.3s ease;
}
.picker-content:active .picker-arrow.data-v-68bf51f7 {
	transform: rotate(90deg) scale(1.2);
}

/* 上传文件样式 */
.image-upload-container.data-v-68bf51f7 {
	width: 100%;
	margin-top: 0;
	position: relative;
}
.image-upload-area.data-v-68bf51f7 {
	width: 100%;
	height: 64rpx;
	background: #f8f9fa;
	border: 2rpx solid #E9ECEF;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
	position: relative;
	box-sizing: border-box;
}
.upload-btn.data-v-68bf51f7 {
	background: #E9ECEF;
	border: none;
	border-radius: 8rpx;
	padding: 8rpx 20rpx;
	font-size: 28rpx;
	color: #666;
	height: 48rpx;
	line-height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.upload-hint.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #999;
	flex: 1;
	margin-left: 16rpx;
}
.upload-status.data-v-68bf51f7 {
	font-size: 32rpx;
	color: #28a745;
	font-weight: bold;
}

/* 单选框样式 */
.radio-group.data-v-68bf51f7 {
	width: 100%;
}
.radio-container.data-v-68bf51f7 {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx 12rpx;
	align-items: flex-start;
}
.radio-item.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	min-width: 0;
	flex: 0 0 auto;
	max-width: 100%;
	padding: 12rpx 16rpx;
	border-radius: 12rpx;
	cursor: pointer;
	transition: background-color 0.3s ease;
	background-color: #f8f9fa;
	border: 2rpx solid #e9ecef;
}
.radio-item.data-v-68bf51f7:active {
	background-color: #e9ecef;
	transform: scale(0.98);
}
.radio-circle.data-v-68bf51f7 {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #D0D0D0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12rpx;
	transition: all 0.3s ease;
	flex-shrink: 0;
}
.radio-circle.checked.data-v-68bf51f7 {
	border-color: #03328c;
	background-color: #03328c;
}
.radio-dot.data-v-68bf51f7 {
	width: 16rpx;
	height: 16rpx;
	background-color: white;
	border-radius: 50%;
}
.radio-label.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4;
	word-wrap: break-word;
	word-break: break-all;
	flex: 1;
	white-space: normal;
}

/* 选中状态的单选框项目样式 */
.radio-item.data-v-68bf51f7:has(.radio-circle.checked) {
	background-color: #e3f2fd !important;
	border-color: #03328c !important;
}
.radio-item:has(.radio-circle.checked) .radio-label.data-v-68bf51f7 {
	color: #03328c !important;
	font-weight: 500;
}

/* 多选框样式 */
.checkbox-group.data-v-68bf51f7 {
	width: 100%;
}
.checkbox-container.data-v-68bf51f7 {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx 12rpx; /* 垂直间距16rpx，水平间距12rpx */
	align-items: flex-start;
}
.checkbox-row.data-v-68bf51f7 {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 24rpx;
	gap: 16rpx; /* 添加间距 */
}
.checkbox-row.data-v-68bf51f7:last-child {
	margin-bottom: 0;
}
.checkbox-item.data-v-68bf51f7 {
	display: flex;
	align-items: flex-start; /* 改为顶部对齐 */
	min-width: 0; /* 允许收缩 */
	flex: 0 0 auto; /* 不拉伸，根据内容自适应 */
	max-width: 100%; /* 最大宽度 */
	cursor: pointer;
	padding: 12rpx 16rpx; /* 增加内边距 */
	border-radius: 12rpx; /* 添加圆角 */
	transition: background-color 0.3s ease;
	background-color: #f8f9fa; /* 默认背景色 */
	border: 2rpx solid #e9ecef; /* 添加边框 */
}
.checkbox-item.data-v-68bf51f7:active {
	background-color: #e9ecef;
	transform: scale(0.98);
}
.checkbox.data-v-68bf51f7 {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #D0D0D0;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12rpx;
	background: #fff;
	transition: all 0.3s ease;
	flex-shrink: 0; /* 防止收缩 */
	margin-top: 4rpx; /* 与文字顶部对齐 */
}
.checkbox.checked.data-v-68bf51f7 {
	border-color: #03328c;
	background-color: #03328c;
}

/* 选中状态的整个项目样式 */
.checkbox-item-checked.data-v-68bf51f7 {
	background-color: #e3f2fd !important;
	border-color: #03328c !important;
}
.checkbox-icon.data-v-68bf51f7 {
	color: white; /* 改为白色 */
	font-size: 20rpx; /* 调整大小 */
	font-weight: bold;
}
.checkbox-label.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4; /* 增加行高 */
	word-wrap: break-word; /* 允许换行 */
	word-break: break-all; /* 强制换行 */
	flex: 1; /* 占据剩余空间 */
	white-space: normal; /* 允许换行 */
}

/* 选中状态的标签文字样式 */
.checkbox-item-checked .checkbox-label.data-v-68bf51f7 {
	color: #03328c !important;
	font-weight: 500;
}

/* 其他选项特殊样式 */
.other-row.data-v-68bf51f7 {
	width: 100%;
}
.checkbox-item-other.data-v-68bf51f7 {
	display: flex;
	align-items: center;
	width: 100%;
	margin-bottom: 16rpx;
	cursor: pointer;
}
.other-inline-input.data-v-68bf51f7 {
	flex: 1;
	height: 40rpx;
	background: transparent;
	border: none;
	border-bottom: 2rpx solid #D0D0D0;
	margin-left: 16rpx;
	padding: 0 8rpx 4rpx 8rpx;
	font-size: 28rpx;
	color: #333;
	box-sizing: border-box;
	transition: border-bottom-color 0.3s ease;
}
.other-inline-input.data-v-68bf51f7:focus {
	border-bottom-color: #1E4BA6;
	outline: none;
}
.other-inline-input.data-v-68bf51f7::-webkit-input-placeholder {
	color: #999;
}
.other-inline-input.data-v-68bf51f7::placeholder {
	color: #999;
}

/* 邮件提交样式 */
.email-submit-container.data-v-68bf51f7 {
	width: 100%;
	padding: 24rpx;
	background: #F8F9FA;
	border: 2rpx solid #E8E8E8;
	border-radius: 12rpx;
	box-sizing: border-box;
}
.email-address.data-v-68bf51f7 {
	margin-bottom: 16rpx;
}
.email-text.data-v-68bf51f7 {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}
.email-format.data-v-68bf51f7 {
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}
.format-label.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #666;
}
.format-example.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}
.email-notice.data-v-68bf51f7 {
	padding: 16rpx 20rpx;
	background: #E3F2FD;
	border: 1rpx solid #BBDEFB;
	border-radius: 8rpx;
}
.notice-text.data-v-68bf51f7 {
	font-size: 28rpx;
	color: #1976D2;
	line-height: 1.5;
}

/* 提交按钮区域 */
.submit-section.data-v-68bf51f7 {
	margin-top: 60rpx;
	padding: 0 32rpx;
}
.submit-btn.data-v-68bf51f7 {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(180deg, #003eae 0%, #285cb9 100%);
	border-radius: 44rpx;
	border: none;
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(30, 75, 166, 0.3);
}
.submit-btn.data-v-68bf51f7:disabled {
	background: #CCCCCC;
	box-shadow: none;
}
.submit-btn.data-v-68bf51f7:active {
	transform: translateY(2rpx);
}

/* 页面动画 */
.container.data-v-68bf51f7 {
	animation: slideInUp-68bf51f7 0.4s ease-out;
}
@keyframes slideInUp-68bf51f7 {
from {
		opacity: 0;
		transform: translateY(100rpx);
}
to {
		opacity: 1;
		transform: translateY(0);
}
}
.section.data-v-68bf51f7 {
	animation: fadeInUp-68bf51f7 0.6s ease-out;
	animation-fill-mode: both;
}
.section.data-v-68bf51f7:nth-child(1) { animation-delay: 0.1s;
}
.section.data-v-68bf51f7:nth-child(2) { animation-delay: 0.2s;
}
.section.data-v-68bf51f7:nth-child(3) { animation-delay: 0.3s;
}
.section.data-v-68bf51f7:nth-child(4) { animation-delay: 0.4s;
}
@keyframes fadeInUp-68bf51f7 {
from {
		opacity: 0;
		transform: translateY(40rpx);
}
to {
		opacity: 1;
		transform: translateY(0);
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.form-container.data-v-68bf51f7 {
		padding: 32rpx 24rpx;
}
.section.data-v-68bf51f7 {
		padding: 32rpx 24rpx;
}
.submit-section.data-v-68bf51f7 {
		padding: 0 24rpx;
}
.title-text.data-v-68bf51f7 {
		font-size: 40rpx;
}
.navbar.data-v-68bf51f7 {
		padding: 0 24rpx;
}
.steps-container.data-v-68bf51f7 {
		margin: 0 24rpx 32rpx;
		padding: 32rpx 24rpx;
}
.step-circle.data-v-68bf51f7 {
		width: 50rpx;
		height: 50rpx;
}
.step-number.data-v-68bf51f7 {
		font-size: 22rpx;
}
.step-text.data-v-68bf51f7 {
		font-size: 22rpx;
}
.step-lines.data-v-68bf51f7 {
		top: 25rpx;
}
.step-line.data-v-68bf51f7 {
		margin: 0 25rpx;
}
.upload-btn.data-v-68bf51f7 {
		padding: 6rpx 16rpx;
		font-size: 26rpx;
		height: 44rpx;
		line-height: 32rpx;
}
.upload-hint.data-v-68bf51f7 {
		font-size: 26rpx;
}
.checkbox-container.data-v-68bf51f7 {
		gap: 12rpx 8rpx;
}
.checkbox-item.data-v-68bf51f7 {
		padding: 10rpx 12rpx;
		min-width: 0;
		flex: 1 1 45%; /* 小屏幕下每行显示2个 */
}
.checkbox-label.data-v-68bf51f7 {
		font-size: 26rpx;
}
.checkbox.data-v-68bf51f7 {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
}
.tip-text.data-v-68bf51f7 {
		font-size: 24rpx;
}
.other-inline-input.data-v-68bf51f7 {
		font-size: 26rpx;
		height: 36rpx;
}
.email-text.data-v-68bf51f7 {
		font-size: 30rpx;
}
.format-label.data-v-68bf51f7,
	.format-example.data-v-68bf51f7 {
		font-size: 26rpx;
}
.notice-text.data-v-68bf51f7 {
		font-size: 26rpx;
}
.email-submit-container.data-v-68bf51f7 {
		padding: 20rpx;
}

	/* 输入框错误状态样式 */
.input-error.data-v-68bf51f7 {
		border-color: #ff4757 !important;
		background-color: #fff5f5 !important;
}

	/* textarea错误状态样式 */
.textarea.input-error.data-v-68bf51f7 {
		border-color: #ff4757 !important;
		background-color: #fff5f5 !important;
}
}
