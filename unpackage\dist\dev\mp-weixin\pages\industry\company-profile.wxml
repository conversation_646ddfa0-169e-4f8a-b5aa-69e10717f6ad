<view class="company-profile-container"><view wx:if="{{a}}" class="{{['status-bar', f]}}"><view class="status-content"><text class="status-text">{{b}}</text><text wx:if="{{c}}" class="status-action" bindtap="{{e}}">{{d}}</text></view></view><view class="form-container"><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{g}}" class="required-star">*</text>企业名称</text></view><input class="{{['form-input', h && 'input-error']}}" placeholder="请输入企业工商注册全称，如：天津海河教育园区投资有限公司" bindinput="{{i}}" bindblur="{{j}}" value="{{k}}"/><text wx:if="{{l}}" class="error-text">{{m}}</text></view><view class="form-item"><view class="form-label-row"><text class="form-label">企业logo</text></view><view class="logo-upload-section"><view class="logo-upload-btn" bindtap="{{p}}"><image wx:if="{{n}}" src="{{o}}" class="uploaded-logo" mode="aspectFit"></image><view wx:else class="upload-placeholder"><text class="upload-plus">+</text></view></view></view></view><view class="form-item"><view class="form-label-row"><text class="form-label">公司简介</text></view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="字数300字以内（公司简介及主营业务）" maxlength="300" bindinput="{{q}}" value="{{r}}"></textarea></block><view class="char-count">{{s}}/300</view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{t}}" class="required-star">*</text>所在地区</text></view><picker mode="selector" range="{{y}}" range-key="{{'name'}}" bindchange="{{z}}" class="selector-picker"><view class="{{['picker-input', x && 'input-error']}}"><text wx:if="{{v}}" class="picker-text">{{w}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{A}}" class="required-star">*</text>创始人/核心团队所在院校</text></view><input class="{{['form-input', B && 'input-error']}}" placeholder="请输入" bindinput="{{C}}" bindblur="{{D}}" value="{{E}}"/></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{F}}" class="required-star">*</text>所处行业</text></view><view class="industry-selector" bindtap="{{J}}"><view class="{{['picker-input', I && 'input-error']}}"><text wx:if="{{G}}" class="picker-text">{{H}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择行业</text><text class="picker-arrow">▼</text></view></view></view><view wx:if="{{K}}" class="industry-picker-modal" bindtap="{{P}}"><view class="industry-picker-content" catchtap="{{O}}"><view class="industry-header"><text class="industry-title">请选择行业</text><view class="industry-actions"><text class="cancel-btn" bindtap="{{L}}">取消</text><text class="confirm-btn" bindtap="{{M}}">确定</text></view></view><scroll-view class="industry-tree-container" scroll-y="true"><view wx:for="{{N}}" wx:for-item="level1" wx:key="l" class="tree-node level1-node"><view class="{{['tree-item', 'level1-item', level1.f && 'disabled']}}" bindtap="{{level1.g}}"><view class="tree-content"><view wx:if="{{level1.a}}" class="{{['expand-icon', level1.c && 'expanded']}}">{{level1.b}}</view><text class="tree-label">{{level1.d}}</text><text wx:if="{{level1.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level1.h}}" class="tree-children level2-children"><block wx:if="{{level1.i}}"><view wx:for="{{level1.j}}" wx:for-item="categoryName" wx:key="d"><view wx:if="{{categoryName.a}}" class="stream-category"><view class="stream-title">{{categoryName.b}}</view><view wx:for="{{categoryName.c}}" wx:for-item="level2" wx:key="j" class="tree-node level2-node"><view class="{{['tree-item', 'level2-item', level2.f && 'disabled']}}" bindtap="{{level2.g}}"><view class="tree-content"><view wx:if="{{level2.a}}" class="{{['expand-icon', level2.c && 'expanded']}}">{{level2.b}}</view><text class="tree-label">{{level2.d}}</text><text wx:if="{{level2.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level2.h}}" class="tree-children level3-children"><view wx:for="{{level2.i}}" wx:for-item="level3" wx:key="f" class="tree-node level3-node"><view class="tree-item level3-item" bindtap="{{level3.e}}"><view class="tree-content"><text class="tree-label">{{level3.a}}</text></view><view class="{{['tree-checkbox', level3.c && 'checked']}}" catchtap="{{level3.d}}"><view wx:if="{{level3.b}}" class="checkbox-inner"></view></view></view></view></view></view></view></view></block><block wx:else><view wx:for="{{level1.k}}" wx:for-item="level2" wx:key="j" class="tree-node level2-node"><view class="{{['tree-item', 'level2-item', level2.f && 'disabled']}}" bindtap="{{level2.g}}"><view class="tree-content"><view wx:if="{{level2.a}}" class="{{['expand-icon', level2.c && 'expanded']}}">{{level2.b}}</view><text class="tree-label">{{level2.d}}</text><text wx:if="{{level2.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level2.h}}" class="tree-children level3-children"><view wx:for="{{level2.i}}" wx:for-item="level3" wx:key="f" class="tree-node level3-node"><view class="tree-item level3-item" bindtap="{{level3.e}}"><view class="tree-content"><text class="tree-label">{{level3.a}}</text></view><view class="{{['tree-checkbox', level3.c && 'checked']}}" catchtap="{{level3.d}}"><view wx:if="{{level3.b}}" class="checkbox-inner"></view></view></view></view></view></view></block></view></view></scroll-view></view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{Q}}" class="required-star">*</text>年营业收入</text></view><input class="{{['form-input', R && 'input-error']}}" placeholder="请输入" bindinput="{{S}}" bindblur="{{T}}" value="{{U}}"/></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{V}}" class="required-star">*</text>企业规模</text></view><picker mode="selector" range="{{Z}}" range-key="{{'name'}}" bindchange="{{aa}}" class="selector-picker"><view class="{{['picker-input', Y && 'input-error']}}"><text wx:if="{{W}}" class="picker-text">{{X}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{ab}}" class="required-star">*</text>企业类型</text></view><picker mode="selector" range="{{af}}" range-key="{{'name'}}" bindchange="{{ag}}" class="selector-picker"><view class="{{['picker-input', ae && 'input-error']}}"><text wx:if="{{ac}}" class="picker-text">{{ad}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label">企业资质</text><text class="form-label-note">（可多选）</text></view><view class="checkbox-grid"><view wx:for="{{ah}}" wx:for-item="item" wx:key="d" class="checkbox-item" bindtap="{{item.e}}"><view class="{{['checkbox', item.b && 'checked']}}"><text wx:if="{{item.a}}" class="check-mark">✓</text></view><text class="checkbox-label">{{item.c}}</text></view></view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{ai}}" class="required-star">*</text>融资轮次</text></view><picker mode="selector" range="{{am}}" range-key="{{'name'}}" bindchange="{{an}}" class="selector-picker"><view class="{{['picker-input', al && 'input-error']}}"><text wx:if="{{aj}}" class="picker-text">{{ak}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label">主营产品/服务清单</text><text class="form-label-note">（可多选）</text></view><view class="checkbox-grid"><view wx:for="{{ao}}" wx:for-item="item" wx:key="d" class="checkbox-item" bindtap="{{item.e}}"><view class="{{['checkbox', item.b && 'checked']}}"><text wx:if="{{item.a}}" class="check-mark">✓</text></view><text class="checkbox-label">{{item.c}}</text></view></view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="产品描述，字数300字以内" maxlength="300" bindinput="{{ap}}" value="{{aq}}"></textarea></block><view class="char-count">{{ar}}/300</view></view></view><view class="submit-section"><button class="{{['submit-btn', at && 'disabled']}}" disabled="{{av}}" bindtap="{{aw}}">{{as}}</button></view></view>