.roadshow-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}
.content-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: visible;
}
.rich-text-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: visible;
}
/* 富文本内容样式优化 */
.rich-text-content {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}
/* 富文本内部图片样式限制 */
.rich-text-content img {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  box-sizing: border-box !important;
  margin: 10rpx 0 !important;
}
/* 针对微信小程序的特殊处理 */
.content-section {
  overflow: hidden;
  max-width: 100%;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 底部按钮 */
.bottom-buttons {
  display: flex;
  height: 120rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}
.bottom-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.apply-btn {
  background: #d4af37;
}
.consult-btn {
  background: #5dade2;
}
.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}
/* 联系人弹窗样式 */
.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
