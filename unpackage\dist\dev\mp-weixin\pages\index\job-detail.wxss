.job-detail-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.job-detail-content {
  background: #ffffff;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
/* 职位标题区域 */
.job-title-section {
  margin-bottom: 20rpx;
}
.job-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #000;
  line-height: 1.3;
}
/* 基本信息区域 */
.job-basic-info {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.company-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.company-scale {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  margin-left: 10rpx;
}
.job-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}
.job-tag {
  font-size: 24rpx;
  color: #666;
  background-color: #d7e6ff;
  padding: 8rpx 16rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
  border-radius: 6rpx;
}
/* 内容区块 */
.section-block {
  margin-bottom: 50rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 10rpx;
}
.section-content {
  line-height: 1.8;
}
.section-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  white-space: pre-wrap;
}
/* 联系信息区域 */
.contact-section {
  margin-top: 60rpx;
  padding-top: 40rpx;
  border-top: 1px solid #f0f0f0;
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.contact-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.contact-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}
