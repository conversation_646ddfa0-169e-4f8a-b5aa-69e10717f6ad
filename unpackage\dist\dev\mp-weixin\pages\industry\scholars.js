"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      loading: true,
      searchKeyword: "",
      scholarsList: [],
      // 专家学者列表
      // 联系人信息弹窗
      showContactPopup: false,
      contactInfo: {
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  onLoad() {
    this.getScholarsList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 专家学者";
    },
    // 自定义分享内容
    getShareContent() {
      return "专家学者分享";
    },
    // 获取专家学者列表
    async getScholarsList(keyword = "") {
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:161", "获取专家学者列表，关键词:", keyword);
        const params = {};
        if (keyword.trim()) {
          params.keyword = keyword.trim();
        }
        const response = await utils_request.request.get("/miniapp/expertscholar/miniapp/search", params);
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:170", "专家学者API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const scholarsData = response.data.rows || [];
          this.scholarsList = scholarsData.map((item) => ({
            id: item.id || item.expertId,
            name: item.name || item.expertName || "未知专家",
            unit: item.university || "未知大学",
            tags: this.parseTags(item.industry),
            description: item.researchDirection || "暂无简介",
            originalData: item
          }));
          common_vendor.index.__f__("log", "at pages/industry/scholars.vue:185", "专家学者列表获取成功:", this.scholarsList);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/scholars.vue:187", "获取专家学者列表失败:", response);
          this.scholarsList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/scholars.vue:192", "获取专家学者列表失败:", error);
        this.scholarsList = [];
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.loading = false;
      }
    },
    // 解析标签
    parseTags(tagsString) {
      if (!tagsString)
        return [];
      if (typeof tagsString === "string") {
        return tagsString.split(",").filter((tag) => tag.trim()).slice(0, 3);
      }
      if (Array.isArray(tagsString)) {
        return tagsString.slice(0, 3);
      }
      return [];
    },
    // 搜索处理
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      await this.getScholarsList(this.searchKeyword);
    },
    // 我有需求 - 跳转到资源场景需求发布页面（与主页面一致）
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/industry/scholars.vue:233", "🎯 我有需求 - 跳转到资源场景需求发布页面");
      try {
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:237", "🎯 获取分类列表，查找资源场景分类ID...");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:240", "🎯 分类列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const categories = response.data.data || [];
          const scenarioCategory = categories.find(
            (cat) => cat.categoryCode === "scenario" || cat.categoryName === "资源场景" || cat.name === "资源场景"
          );
          if (scenarioCategory) {
            const categoryId = scenarioCategory.categoryId || scenarioCategory.id;
            const categoryName = scenarioCategory.categoryName || scenarioCategory.name || "资源场景";
            common_vendor.index.__f__("log", "at pages/industry/scholars.vue:256", "🎯 找到资源场景分类:", { categoryId, categoryName });
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}&categoryCode=scenario`
            });
          } else {
            common_vendor.index.__f__("log", "at pages/industry/scholars.vue:263", "🎯 ❌ 未找到资源场景分类，使用默认方式");
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/scholars.vue:270", "🎯 获取分类列表失败:", response);
          common_vendor.index.navigateTo({
            url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/scholars.vue:277", "🎯 获取分类列表异常:", error);
        common_vendor.index.navigateTo({
          url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
        });
      }
    },
    // 我有疑问 - 与需求广场功能一致
    async haveQuestion() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/industry/scholars.vue:287", "📋 问题咨询");
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:296", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/industry/scholars.vue:313", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/industry/scholars.vue:319", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "学者信息咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/scholars.vue:332", "📋 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/scholars.vue:367", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: $data.loading
  }, $data.loading ? {} : $data.scholarsList.length > 0 ? {
    g: common_vendor.f($data.scholarsList, (scholar, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(scholar.name),
        b: common_vendor.t(scholar.unit),
        c: scholar.tags && scholar.tags.length > 0
      }, scholar.tags && scholar.tags.length > 0 ? {
        d: common_vendor.f(scholar.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        e: common_vendor.t(scholar.description),
        f: scholar.id || index
      });
    })
  } : {
    h: $options.getImagePath("empty-expert.png")
  }, {
    f: $data.scholarsList.length > 0,
    i: common_vendor.o((...args) => $options.haveQuestion && $options.haveQuestion(...args)),
    j: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    k: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    l: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    m: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    n: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    o: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    p: $data.contactInfo.qrCodeUrl
  } : {}, {
    q: common_vendor.o(() => {
    }),
    r: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-10fbadec"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/scholars.js.map
