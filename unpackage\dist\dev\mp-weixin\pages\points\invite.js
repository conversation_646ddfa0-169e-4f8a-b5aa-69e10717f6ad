"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      topImage: "",
      // 顶部图片
      inviteCount: 0,
      // 当前邀请数量
      inviteStats: {
        totalInvites: 0,
        // 累计有效邀请
        totalRewards: 0
        // 累计奖励积分
      },
      inviteRecords: [],
      // 邀请记录
      loading: false,
      isFirstLoad: true,
      // 标记是否首次加载
      // 海报相关
      showPosterModal: false,
      // 海报弹窗显示状态
      posterImage: "",
      // 生成的海报图片路径
      posterLoading: false,
      // 海报生成加载状态
      shareTopImage: "",
      // 分享海报顶图
      inviteQRCode: ""
      // 邀请二维码base64
    };
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/points/invite.vue:176", "🎁 邀请页面加载");
    common_vendor.index.setNavigationBarTitle({
      title: "积分商城"
    });
    this.initPage();
  },
  // 页面显示时刷新数据（避免首次加载重复调用）
  onShow() {
    common_vendor.index.__f__("log", "at pages/points/invite.vue:188", "🎁 邀请页面显示");
    if (!this.isFirstLoad) {
      common_vendor.index.__f__("log", "at pages/points/invite.vue:192", "🎁 非首次显示，刷新邀请记录");
      this.getInviteRecords();
    } else {
      common_vendor.index.__f__("log", "at pages/points/invite.vue:195", "🎁 首次显示，跳过刷新（已在onLoad中加载）");
      this.isFirstLoad = false;
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 邀请新人好友获取超值1000积分";
    },
    // 自定义分享路径
    getSharePath() {
      return "/pages/login/index?inviteCode=" + this.getInviteCode();
    },
    // 自定义分享内容
    getShareContent() {
      return "邀请新人好友获取积分分享";
    },
    // 初始化页面
    async initPage() {
      await this.getTopImage();
      await this.getInviteRecords();
    },
    // 获取顶部图片
    async getTopImage() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/invite.vue:229", "🎁 获取邀请页面顶部图片...");
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "PointGoodsInviteTopImage");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:233", "🎁 顶部图片响应:", response);
        if (response && response.data && response.data.code === 200) {
          const imageData = response.data.data[0];
          if (imageData && imageData.imageUrl) {
            this.topImage = utils_imageUtils.processServerImageUrl(imageData.imageUrl);
            common_vendor.index.__f__("log", "at pages/points/invite.vue:239", "🎁 设置顶部图片:", this.topImage);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:242", "🎁 获取顶部图片失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:246", "🎁 获取顶部图片失败:", error);
      }
    },
    // 获取邀请统计信息
    async getInviteStats() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/invite.vue:253", "🎁 获取邀请统计信息...");
        const response = await utils_request.request.get("/miniapp/invite/app/getStats");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:256", "🎁 邀请统计响应:", response);
        if (response && response.data && response.data.code === 200) {
          const stats = response.data.data;
          this.inviteCount = stats.currentInvites || 0;
          this.inviteStats = {
            totalInvites: stats.totalInvites || 0,
            totalRewards: stats.totalRewards || 0
          };
          common_vendor.index.__f__("log", "at pages/points/invite.vue:265", "🎁 邀请统计信息:", this.inviteStats);
        } else {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:267", "🎁 获取邀请统计失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:271", "🎁 获取邀请统计失败:", error);
      }
    },
    // 获取邀请记录
    async getInviteRecords() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/invite.vue:278", "🎁 获取邀请记录...");
        const response = await utils_request.request.get("/miniapp/invitation/friends");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:282", "🎁 邀请记录响应:", response);
        if (response && response.data && response.data.code === 0) {
          const records = response.data.rows || [];
          this.inviteRecords = records.map((record) => ({
            nickname: record.nickname || record.userName || record.name || "新用户",
            avatar: record.avatar || record.headImg || record.userAvatar || "",
            inviteTime: this.formatTime(record.createTime || record.inviteTime || record.registerTime),
            rewardPoints: record.rewardPoints || record.points || 100
            // 默认100积分
          }));
          common_vendor.index.__f__("log", "at pages/points/invite.vue:296", "🎁 格式化后的邀请记录:", this.inviteRecords);
          const totalCount = response.data.total || this.inviteRecords.length;
          this.inviteStats.totalInvites = totalCount;
          this.inviteCount = Math.min(totalCount, 10);
          const totalRewards = this.inviteRecords.reduce((sum, record) => sum + record.rewardPoints, 0);
          this.inviteStats.totalRewards = totalRewards;
          common_vendor.index.__f__("log", "at pages/points/invite.vue:307", "🎁 更新后的统计数据:", {
            totalInvites: this.inviteStats.totalInvites,
            totalRewards: this.inviteStats.totalRewards,
            currentInvites: this.inviteCount
          });
        } else {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:314", "🎁 获取邀请记录失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          this.inviteRecords = [];
          this.inviteStats = { totalInvites: 0, totalRewards: 0 };
          this.inviteCount = 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:321", "🎁 获取邀请记录失败:", error);
        this.inviteRecords = [];
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "刚刚";
      try {
        const date = new Date(timeStr);
        const now = /* @__PURE__ */ new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / (1e3 * 60));
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
        if (minutes < 1)
          return "刚刚";
        if (minutes < 60)
          return `${minutes}分钟前`;
        if (hours < 24)
          return `${hours}小时前`;
        if (days < 7)
          return `${days}天前`;
        return `${date.getMonth() + 1}-${date.getDate()}`;
      } catch (error) {
        return "刚刚";
      }
    },
    // 开始邀请
    startInvite() {
      common_vendor.index.__f__("log", "at pages/points/invite.vue:354", "🎁 开始邀请好友");
      common_vendor.index.showActionSheet({
        itemList: ["分享给微信好友", "生成邀请海报"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.shareToFriend();
          } else if (res.tapIndex === 1) {
            this.generateInvitePoster();
          }
        }
      });
    },
    // 分享给好友
    shareToFriend() {
      common_vendor.index.__f__("log", "at pages/points/invite.vue:373", "🎁 分享给微信好友");
      common_vendor.index.showToast({
        title: "请点击右上角分享",
        icon: "none"
      });
    },
    // 生成邀请海报
    async generateInvitePoster() {
      common_vendor.index.__f__("log", "at pages/points/invite.vue:382", "🎁 生成邀请海报");
      try {
        this.posterLoading = true;
        this.showPosterModal = true;
        common_vendor.index.__f__("log", "at pages/points/invite.vue:388", "🎁 步骤1: 获取分享海报顶图...");
        await this.getShareTopImage();
        common_vendor.index.__f__("log", "at pages/points/invite.vue:391", "🎁 步骤2: 获取邀请二维码...");
        await this.getInviteQRCode();
        common_vendor.index.__f__("log", "at pages/points/invite.vue:394", "🎁 步骤3: 生成海报...");
        await this.createPosterImage();
        common_vendor.index.__f__("log", "at pages/points/invite.vue:397", "🎁 海报生成完成!");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:400", "🎁 生成邀请海报失败:", error);
        let errorMessage = "海报生成失败";
        if (error.message) {
          errorMessage = error.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }
        common_vendor.index.showModal({
          title: "海报生成失败",
          content: errorMessage,
          showCancel: false,
          confirmText: "我知道了"
        });
        this.closePosterModal();
      } finally {
        this.posterLoading = false;
      }
    },
    // 获取邀请码
    getInviteCode() {
      return "INVITE123";
    },
    // 获取分享海报顶图
    async getShareTopImage() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/invite.vue:433", "🎁 获取分享海报顶图...");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:434", "🎁 API地址: POST /miniapp/topimage/app/getEnabledListByPage");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:435", "🎁 请求参数: pageCode = SharePoster");
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "SharePoster");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:438", "🎁 分享海报顶图响应:", response);
        if (response && response.data && response.data.code === 200) {
          const imageList = response.data.data || [];
          if (imageList.length > 0) {
            this.shareTopImage = utils_imageUtils.processServerImageUrl(imageList[0].imageUrl);
            common_vendor.index.__f__("log", "at pages/points/invite.vue:444", "🎁 分享海报顶图获取成功:", this.shareTopImage);
          } else {
            common_vendor.index.__f__("log", "at pages/points/invite.vue:446", "🎁 没有找到分享海报顶图，将跳过顶图绘制");
          }
        } else {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:450", "🎁 获取分享海报顶图失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:454", "🎁 获取分享海报顶图失败:", error);
      }
    },
    // 获取邀请二维码
    async getInviteQRCode() {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/points/invite.vue:462", "🎁 获取邀请二维码...");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:463", "🎁 API地址: POST /miniapp/invitation/generateQRCode");
        const response = await utils_request.request.post("/miniapp/invitation/generateQRCode");
        common_vendor.index.__f__("log", "at pages/points/invite.vue:466", "🎁 邀请二维码响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.inviteQRCode = response.data.data.qrcode;
          common_vendor.index.__f__("log", "at pages/points/invite.vue:471", "🎁 邀请二维码获取成功:", this.inviteQRCode ? "已获取" : "数据为空");
        } else {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:473", "🎁 获取邀请二维码失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          throw new Error(((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.msg) || "获取二维码失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:477", "🎁 获取邀请二维码失败:", error);
        throw error;
      }
    },
    // 创建海报图片
    async createPosterImage() {
      return new Promise(async (resolve, reject) => {
        try {
          common_vendor.index.__f__("log", "at pages/points/invite.vue:486", "🎁 开始生成海报图片...");
          common_vendor.index.__f__("log", "at pages/points/invite.vue:487", "🎁 顶图URL:", this.shareTopImage);
          common_vendor.index.__f__("log", "at pages/points/invite.vue:488", "🎁 二维码数据:", this.inviteQRCode ? "已获取" : "未获取");
          let localTopImagePath = null;
          if (this.shareTopImage) {
            try {
              const downloadResult = await common_vendor.index.downloadFile({
                url: this.shareTopImage
              });
              localTopImagePath = downloadResult.tempFilePath;
              common_vendor.index.__f__("log", "at pages/points/invite.vue:498", "🎁 顶图下载成功:", localTopImagePath);
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/points/invite.vue:500", "🎁 顶图下载失败:", error);
            }
          }
          const ctx = common_vendor.index.createCanvasContext("posterCanvas", this);
          const canvasWidth = 375;
          const canvasHeight = 540;
          let currentY = 0;
          const topImageHeight = 260;
          if (localTopImagePath) {
            ctx.drawImage(localTopImagePath, 0, currentY, canvasWidth, topImageHeight);
            common_vendor.index.__f__("log", "at pages/points/invite.vue:518", "🎁 顶图绘制完成");
          } else {
            common_vendor.index.__f__("log", "at pages/points/invite.vue:520", "🎁 跳过顶图绘制");
          }
          const gradientStartY = topImageHeight;
          const gradientHeight = canvasHeight - topImageHeight;
          const gradient = ctx.createLinearGradient(0, gradientStartY, 0, gradientStartY + gradientHeight);
          gradient.addColorStop(0, "#f9ebbd");
          gradient.addColorStop(1, "#ffffff");
          ctx.setFillStyle(gradient);
          ctx.fillRect(0, gradientStartY, canvasWidth, gradientHeight);
          common_vendor.index.__f__("log", "at pages/points/invite.vue:534", "🎁 渐变背景绘制完成");
          const circleRadius = 40;
          const circleX = canvasWidth / 2;
          const circleY = topImageHeight;
          ctx.setShadow(0, 2, 8, "rgba(0, 0, 0, 0.15)");
          ctx.setFillStyle("#ffffff");
          ctx.beginPath();
          ctx.arc(circleX, circleY, circleRadius, 0, 2 * Math.PI);
          ctx.fill();
          ctx.setShadow(0, 0, 0, "transparent");
          common_vendor.index.__f__("log", "at pages/points/invite.vue:552", "🎁 白色圆圈绘制完成");
          const contentTopMargin = 35;
          currentY = circleY + circleRadius + contentTopMargin;
          const titleColor = "#f24547";
          const titleFontSize = 18;
          const titleLineHeight = 32;
          ctx.setFillStyle(titleColor);
          ctx.setFontSize(titleFontSize);
          ctx.setTextAlign("center");
          ctx.font = `bold ${titleFontSize}px sans-serif`;
          ctx.fillText("天大海棠创新创业生态", canvasWidth / 2, currentY);
          currentY += titleLineHeight;
          ctx.fillText("欢迎您的加入", canvasWidth / 2, currentY);
          common_vendor.index.__f__("log", "at pages/points/invite.vue:574", "🎁 红色文字绘制完成");
          const qrTopMargin = 25;
          const qrSize = 120;
          currentY += qrTopMargin;
          if (this.inviteQRCode) {
            const qrX = (canvasWidth - qrSize) / 2;
            const qrY = currentY;
            const qrRadius = qrSize * 0.5;
            try {
              ctx.save();
              ctx.beginPath();
              ctx.moveTo(qrX + qrRadius, qrY);
              ctx.arcTo(qrX + qrSize, qrY, qrX + qrSize, qrY + qrRadius, qrRadius);
              ctx.arcTo(qrX + qrSize, qrY + qrSize, qrX + qrSize - qrRadius, qrY + qrSize, qrRadius);
              ctx.arcTo(qrX, qrY + qrSize, qrX, qrY + qrSize - qrRadius, qrRadius);
              ctx.arcTo(qrX, qrY, qrX + qrRadius, qrY, qrRadius);
              ctx.closePath();
              ctx.clip();
              ctx.drawImage(this.inviteQRCode, qrX, qrY, qrSize, qrSize);
              ctx.restore();
              common_vendor.index.__f__("log", "at pages/points/invite.vue:610", "🎁 圆角二维码绘制完成");
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/points/invite.vue:612", "🎁 二维码绘制失败:", error);
            }
          } else {
            common_vendor.index.__f__("log", "at pages/points/invite.vue:615", "🎁 跳过二维码绘制");
          }
          ctx.draw(false, () => {
            common_vendor.index.__f__("log", "at pages/points/invite.vue:620", "🎁 Canvas绘制完成，开始转换为图片...");
            setTimeout(() => {
              common_vendor.index.canvasToTempFilePath({
                canvasId: "posterCanvas",
                success: (res) => {
                  common_vendor.index.__f__("log", "at pages/points/invite.vue:625", "🎁 海报生成成功:", res.tempFilePath);
                  this.posterImage = res.tempFilePath;
                  resolve(res.tempFilePath);
                },
                fail: (error) => {
                  common_vendor.index.__f__("error", "at pages/points/invite.vue:630", "🎁 Canvas转图片失败:", error);
                  reject(new Error("Canvas转图片失败: " + JSON.stringify(error)));
                }
              }, this);
            }, 1500);
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/points/invite.vue:638", "🎁 创建海报图片失败:", error);
          reject(error);
        }
      });
    },
    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false;
      this.posterImage = "";
      this.posterLoading = false;
    },
    // 保存海报到相册
    async savePosterToAlbum() {
      if (!this.posterImage) {
        common_vendor.index.showToast({
          title: "海报未生成",
          icon: "none"
        });
        return;
      }
      try {
        await common_vendor.index.saveImageToPhotosAlbum({
          filePath: this.posterImage
        });
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/invite.vue:672", "🎁 保存海报失败:", error);
        if (error.errMsg && error.errMsg.includes("auth deny")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "需要授权保存图片到相册",
            confirmText: "去设置",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.topImage,
    b: $options.getImagePath("mall_button.png"),
    c: common_vendor.t($data.inviteCount),
    d: common_vendor.o((...args) => $options.startInvite && $options.startInvite(...args)),
    e: $options.getImagePath("mall_icon1.png"),
    f: $options.getImagePath("mall_icon2.png"),
    g: $options.getImagePath("mall_icon3.png"),
    h: common_vendor.t($data.inviteStats.totalInvites),
    i: common_vendor.t($data.inviteStats.totalRewards),
    j: $data.inviteRecords.length > 0
  }, $data.inviteRecords.length > 0 ? {
    k: common_vendor.f($data.inviteRecords, (record, index, i0) => {
      return {
        a: record.avatar || $options.getImagePath("avatar.png"),
        b: common_vendor.t(record.nickname || "新用户"),
        c: common_vendor.t(record.inviteTime),
        d: common_vendor.t(record.rewardPoints),
        e: index
      };
    })
  } : {
    l: $options.getImagePath("mall_nodata.png")
  }, {
    m: $data.showPosterModal
  }, $data.showPosterModal ? common_vendor.e({
    n: $data.posterLoading
  }, $data.posterLoading ? {} : $data.posterImage ? {
    p: $data.posterImage,
    q: common_vendor.o((...args) => $options.savePosterToAlbum && $options.savePosterToAlbum(...args))
  } : {}, {
    o: $data.posterImage,
    r: $options.getImagePath("cha.png"),
    s: common_vendor.o((...args) => $options.closePosterModal && $options.closePosterModal(...args)),
    t: common_vendor.o(() => {
    }),
    v: common_vendor.o((...args) => $options.closePosterModal && $options.closePosterModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/invite.js.map
