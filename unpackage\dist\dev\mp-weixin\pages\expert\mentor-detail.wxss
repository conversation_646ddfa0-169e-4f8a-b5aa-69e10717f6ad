.mentor-detail-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.mentor-detail-content {
  position: relative;
}
/* 导师信息卡片 */
.mentor-card {
  background: #fff;
  margin-bottom: 30rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
  display: flex;
  align-items: center;
}
.mentor-avatar-container {
  margin-right: 30rpx;
}
.mentor-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}
.mentor-basic-info {
  flex: 1;
}
.mentor-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #003399;
  display: block;
  margin-bottom: 10rpx;
}
.mentor-title {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}
/* 内容区域 */
.content-section {
  background: #fff;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.section-content {
  padding: 40rpx;
}
.rich-text-content {
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}
.empty-content {
  padding: 80rpx 40rpx;
  text-align: center;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 富文本样式优化 */
.rich-text-content p {
  margin: 20rpx 0;
  line-height: 1.6;
}
.rich-text-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 20rpx 0;
  border-radius: 10rpx;
}
.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3 {
  color: #003399;
  margin: 30rpx 0 20rpx;
  font-weight: bold;
}
.rich-text-content h1 {
  font-size: 36rpx;
}
.rich-text-content h2 {
  font-size: 32rpx;
}
.rich-text-content h3 {
  font-size: 30rpx;
}
