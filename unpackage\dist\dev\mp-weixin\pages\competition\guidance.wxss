.guidance-container {
  width: 100%;
  min-height: 100vh;
  background: #f5f7fa;
}
.header-section {
  background: linear-gradient(270deg, #013fb0 0%, #002566 100%);
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  overflow: hidden;
}
.header-section image {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}
.header-section .search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
  z-index: 10;
}
.header-section .search-container .search-input {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  border: 1px solid #fff;
}
.header-section .search-container .search-input::-webkit-input-placeholder {
  color: #999;
}
.header-section .search-container .search-input::placeholder {
  color: #999;
}
.header-section .search-container .search-btn {
  width: 120rpx;
  height: 80rpx;
  background: #72a5ff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-section .search-container .search-btn .search-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
.tab-navigation {
  background: #fff;
  display: flex;
  padding: 0 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.tab-navigation .tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.tab-navigation .tab-item .tab-text {
  font-size: 28rpx;
  color: #666;
}
.tab-navigation .tab-item.active .tab-text {
  color: #2a5298;
  font-weight: bold;
}
.tab-navigation .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #2a5298;
  border-radius: 2rpx;
}
.guidance-list {
  padding: 20rpx 20rpx 0rpx 20rpx;
}
.guidance-list .guidance-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.guidance-list .guidance-item .item-content {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
  padding-bottom: 32rpx;
  border-bottom: 1px solid #ececec;
}
.guidance-list .guidance-item .item-content .item-image {
  width: 250rpx;
  height: 150rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}
.guidance-list .guidance-item .item-content .item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.guidance-list .guidance-item .item-content .item-info .item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4rpx;
}
.guidance-list .guidance-item .item-content .item-info .item-date {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 2rpx;
}
.guidance-list .guidance-item .item-content .item-info .item-location,
.guidance-list .guidance-item .item-content .item-info .item-organizer {
  font-size: 22rpx;
  color: #999;
  line-height: 1.3;
}
.guidance-list .guidance-item .item-action {
  display: flex;
  justify-content: flex-end;
}
.guidance-list .guidance-item .item-action .action-btn {
  padding: 0rpx 45rpx 8rpx;
  border-radius: 20rpx;
}
.guidance-list .guidance-item .item-action .action-btn .action-text {
  font-size: 24rpx;
  font-weight: 500;
}
.guidance-list .guidance-item .item-action .action-btn.pending {
  background: #8e44ad;
}
.guidance-list .guidance-item .item-action .action-btn.pending .action-text {
  color: #fff;
}
.guidance-list .guidance-item .item-action .action-btn.ongoing,
.guidance-list .guidance-item .item-action .action-btn.active {
  background: #eab45a;
}
.guidance-list .guidance-item .item-action .action-btn.ongoing .action-text,
.guidance-list .guidance-item .item-action .action-btn.active .action-text {
  color: #fff;
}
.guidance-list .guidance-item .item-action .action-btn.ended {
  background: #b01415;
}
.guidance-list .guidance-item .item-action .action-btn.ended .action-text {
  color: #fff;
}
.no-more {
  padding: 0 40rpx 40rpx;
  text-align: center;
}
.no-more .no-more-text {
  font-size: 24rpx;
  color: #999;
}
/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 描述文本样式 */
.item-description {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
