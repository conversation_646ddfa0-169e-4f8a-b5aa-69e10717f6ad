"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      showFilter: false,
      // 控制筛选面板显示
      activeCategory: 0,
      // 当前激活的筛选分类
      scrollTop: 0,
      // 滚动位置
      scrollIntoView: "",
      // 滚动到指定元素
      isManualScroll: false,
      // 标记是否为手动点击触发的滚动
      // 热点产业和城市地区弹窗控制
      showHotIndustryModal: false,
      // 控制热点产业弹窗显示
      showCityAreaModal: false,
      // 控制城市地区弹窗显示
      // 热点产业数据 - 从API获取
      hotIndustries: [],
      // 城市地区数据 - 从API获取
      cityAreas: [],
      // 筛选选项
      selectedRegion: "",
      // 选中的地区
      selectedIndustry: "",
      // 选中的行业
      selectedScale: "",
      // 选中的企业规模
      selectedType: "",
      // 选中的企业类型
      selectedQualification: "",
      // 选中的企业资质
      selectedRound: "",
      // 选中的融资轮次
      industryList: [],
      // 从接口获取的一级行业数据（前五个）
      allIndustryList: [],
      // 所有一级行业数据
      // 筛选分类
      filterCategories: [
        { name: "所在地区" },
        { name: "所在行业" },
        { name: "企业规模" },
        { name: "企业类型" },
        { name: "企业资质" },
        { name: "融资轮次" }
      ],
      // 地区选项 - 全国省级行政区
      regionOptions: [
        { label: "全 部", value: "" },
        // 直辖市
        { label: "北京", value: "北京" },
        { label: "天津", value: "天津" },
        { label: "上海", value: "上海" },
        { label: "重庆", value: "重庆" },
        // 省份（按拼音排序）
        { label: "安徽", value: "安徽" },
        { label: "福建", value: "福建" },
        { label: "甘肃", value: "甘肃" },
        { label: "广东", value: "广东" },
        { label: "贵州", value: "贵州" },
        { label: "海南", value: "海南" },
        { label: "河北", value: "河北" },
        { label: "河南", value: "河南" },
        { label: "黑龙江", value: "黑龙江" },
        { label: "湖北", value: "湖北" },
        { label: "湖南", value: "湖南" },
        { label: "吉林", value: "吉林" },
        { label: "江苏", value: "江苏" },
        { label: "江西", value: "江西" },
        { label: "辽宁", value: "辽宁" },
        { label: "青海", value: "青海" },
        { label: "山东", value: "山东" },
        { label: "山西", value: "山西" },
        { label: "陕西", value: "陕西" },
        { label: "四川", value: "四川" },
        { label: "云南", value: "云南" },
        { label: "浙江", value: "浙江" },
        // 自治区
        { label: "广西", value: "广西" },
        { label: "内蒙古", value: "内蒙古" },
        { label: "宁夏", value: "宁夏" },
        { label: "西藏", value: "西藏" },
        { label: "新疆", value: "新疆" },
        // 特别行政区
        { label: "香港", value: "香港" },
        { label: "澳门", value: "澳门" },
        { label: "台湾", value: "台湾" }
      ],
      // 行业选项 - 从API获取
      industryOptions: [
        { label: "全 部", value: "", percentage: "" }
      ],
      // 企业规模选项
      scaleOptions: [
        { label: "全 部", value: "" },
        { label: "初创企业", value: "初创企业" },
        { label: "成长企业", value: "成长企业" },
        { label: "成熟企业", value: "成熟企业" },
        { label: "大型企业", value: "大型企业" }
      ],
      // 企业类型选项 - 从API获取
      typeOptions: [
        { label: "全 部", value: "" }
      ],
      // 企业资质选项
      qualificationOptions: [
        { label: "全 部", value: "" },
        { label: "高新技术企业", value: "高新技术企业" },
        { label: "专精特新", value: "专精特新" },
        { label: "独角兽企业", value: "独角兽企业" },
        { label: "上市企业", value: "上市企业" }
      ],
      // 融资轮次选项
      roundOptions: [
        { label: "全 部", value: "" },
        { label: "A轮", value: "A轮" },
        { label: "B轮", value: "B轮" },
        { label: "C轮", value: "C轮" },
        { label: "上市", value: "上市" }
      ]
    };
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 检查功能权限（只检查个人资料）
    async checkProfilePermission() {
      return await utils_profileCheck.checkFunctionPermission(false);
    },
    // 检查功能权限（检查个人资料 + 企业信息）
    async checkFullPermission() {
      return await utils_profileCheck.checkFunctionPermission(true);
    },
    // 我有需求 - 跳转到资源场景类型的需求发布页面
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:453", "🎯 我有需求 - 跳转到资源场景需求发布页面");
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:463", "🎯 获取分类列表，查找资源场景分类ID...");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/industry/index.vue:466", "🎯 分类列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const categories = response.data.data || [];
          const scenarioCategory = categories.find(
            (cat) => cat.categoryCode === "scenario" || cat.categoryName === "资源场景" || cat.name === "资源场景"
          );
          if (scenarioCategory) {
            const categoryId = scenarioCategory.categoryId || scenarioCategory.id;
            const categoryName = scenarioCategory.categoryName || scenarioCategory.name || "资源场景";
            common_vendor.index.__f__("log", "at pages/industry/index.vue:482", "🎯 找到资源场景分类:", { categoryId, categoryName });
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}&categoryCode=scenario`
            });
          } else {
            common_vendor.index.__f__("log", "at pages/industry/index.vue:489", "🎯 ❌ 未找到资源场景分类，使用默认方式");
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/index.vue:496", "🎯 获取分类列表失败:", response);
          common_vendor.index.navigateTo({
            url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:503", "🎯 获取分类列表异常:", error);
        common_vendor.index.navigateTo({
          url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
        });
      }
    },
    // 完善信息 - 跳转到完善企业信息页面
    completeInfo() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:513", "🎯 完善信息 - 跳转到企业信息页面");
      common_vendor.index.navigateTo({
        url: "/pages/industry/company-profile"
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 显示筛选弹窗
    showFilterModal() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:526", "显示筛选弹窗");
      this.showFilter = true;
    },
    // 隐藏筛选弹窗
    hideFilterModal() {
      this.showFilter = false;
    },
    // 搜索处理
    async handleSearch() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:537", "搜索关键词:", this.searchKeyword);
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      await this.searchEnterprises();
    },
    // 滚动到指定分类
    scrollToSection(index) {
      this.isManualScroll = true;
      this.activeCategory = index;
      if (index === 5) {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select(".filter-options").scrollOffset();
        query.select(".filter-options").boundingClientRect();
        query.exec((res) => {
          if (res && res[0] && res[1]) {
            const scrollOffset = res[0];
            const scrollView = res[1];
            const maxScrollTop = scrollOffset.scrollHeight - scrollView.height;
            this.scrollTop = 0;
            setTimeout(() => {
              const targetScrollTop = maxScrollTop > 0 ? maxScrollTop : 9999;
              this.scrollTop = targetScrollTop;
              setTimeout(() => {
                this.isManualScroll = false;
              }, 300);
            }, 10);
          }
        });
      } else {
        this.scrollIntoView = "";
        this.$nextTick(() => {
          this.scrollIntoView = `section-${index}`;
          setTimeout(() => {
            this.isManualScroll = false;
          }, 300);
        });
      }
    },
    // 滚动事件处理
    onScroll(e) {
      if (this.isManualScroll) {
        return;
      }
      const scrollTop = e.detail.scrollTop;
      this.updateActiveCategory(scrollTop);
    },
    // 更新当前选中的分类
    updateActiveCategory(scrollTop) {
      if (this.isManualScroll) {
        return;
      }
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".filter-options").scrollOffset();
      query.select(".filter-options").boundingClientRect();
      query.selectAll(".section-title").boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1] && res[2]) {
          const scrollOffset = res[0];
          const scrollView = res[1];
          const titles = res[2];
          const maxScrollTop = scrollOffset.scrollHeight - scrollView.height;
          if (scrollTop >= maxScrollTop - 10) {
            this.activeCategory = 5;
            return;
          }
          for (let i = 0; i < titles.length && i < 6; i++) {
            const title = titles[i];
            if (title) {
              const titleTop = title.top - scrollView.top;
              if (titleTop >= 0 && titleTop <= 50) {
                this.activeCategory = i;
                break;
              }
            }
          }
        }
      });
    },
    // 选择地区
    selectRegion(value) {
      this.selectedRegion = value;
      this.applyFilters();
    },
    // 选择行业
    selectIndustry(value) {
      this.selectedIndustry = value;
      this.applyFilters();
    },
    // 选择企业规模
    selectScale(value) {
      this.selectedScale = value;
      this.applyFilters();
    },
    // 选择企业类型
    selectType(value) {
      this.selectedType = value;
      this.applyFilters();
    },
    // 选择企业资质
    selectQualification(value) {
      this.selectedQualification = value;
      this.applyFilters();
    },
    // 选择融资轮次
    selectRound(value) {
      this.selectedRound = value;
      this.applyFilters();
    },
    // 应用筛选
    async applyFilters() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:691", "应用筛选条件:", {
        region: this.selectedRegion,
        industry: this.selectedIndustry,
        scale: this.selectedScale,
        type: this.selectedType,
        qualification: this.selectedQualification,
        round: this.selectedRound
      });
      await this.searchEnterprises();
    },
    // 企业搜索
    async searchEnterprises() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:707", "开始企业搜索...");
        const hasPermission = await this.checkFullPermission();
        if (!hasPermission) {
          return;
        }
        const searchParams = this.buildSearchParams();
        common_vendor.index.__f__("log", "at pages/industry/index.vue:717", "搜索参数:", searchParams);
        const response = await utils_request.request.post("/miniapp/enterprise/search", searchParams);
        common_vendor.index.__f__("log", "at pages/industry/index.vue:721", "企业搜索API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const searchResults = response.data.rows || [];
          const total = response.data.total || 0;
          common_vendor.index.__f__("log", "at pages/industry/index.vue:727", "搜索结果:", searchResults, "总数:", total);
          this.navigateToEnterpriseList(searchResults, total);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:732", "企业搜索失败:", response);
          common_vendor.index.showToast({
            title: "搜索失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:740", "企业搜索异常:", error);
        common_vendor.index.showToast({
          title: "搜索异常，请重试",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 构建搜索参数
    buildSearchParams() {
      const params = {
        keyword: this.searchKeyword.trim(),
        pageNum: 1,
        pageSize: 20,
        regions: [],
        industryIds: [],
        companyScales: [],
        companyTypes: [],
        qualifications: [],
        fundingRounds: []
      };
      if (this.selectedRegion) {
        params.regions.push(this.selectedRegion);
      }
      if (this.selectedIndustry) {
        const industryId = this.getIndustryIdByName(this.selectedIndustry);
        if (industryId) {
          params.industryIds.push(industryId);
        }
      }
      if (this.selectedScale) {
        params.companyScales.push(this.selectedScale);
      }
      if (this.selectedType) {
        params.companyTypes.push(this.selectedType);
      }
      if (this.selectedQualification) {
        params.qualifications.push(this.selectedQualification);
      }
      if (this.selectedRound) {
        params.fundingRounds.push(this.selectedRound);
      }
      return params;
    },
    // 根据行业名称获取行业ID
    getIndustryIdByName(industryName) {
      const industry = this.allIndustryList.find(
        (item) => item.nodeInfo.nodeName === industryName
      );
      return industry ? industry.nodeInfo.nodeId : null;
    },
    // 跳转到企业列表页面
    navigateToEnterpriseList(searchResults, total) {
      const searchData = {
        results: searchResults,
        total,
        searchParams: {
          keyword: this.searchKeyword,
          region: this.selectedRegion,
          industry: this.selectedIndustry,
          scale: this.selectedScale,
          type: this.selectedType,
          qualification: this.selectedQualification,
          round: this.selectedRound
        }
      };
      const encodedData = encodeURIComponent(JSON.stringify(searchData));
      common_vendor.index.navigateTo({
        url: `/pages/industry/list?searchData=${encodedData}&regionName=${encodeURIComponent("搜索结果")}&subsectionName=${encodeURIComponent("企业搜索")}`
      });
      this.hideFilterModal();
    },
    // 查看更多产业
    async viewMoreIndustries() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:836", "查看更多产业");
      const hasPermission = await this.checkFullPermission();
      if (!hasPermission) {
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/industry/industry-list"
      });
    },
    // 打开热点产业弹窗
    async openHotIndustryModal() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:852", "点击热点产业卡片");
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      this.showHotIndustryModal = true;
    },
    // 打开城市地区弹窗
    async openCityAreaModal() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:865", "点击城市地区卡片");
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      this.showCityAreaModal = true;
    },
    // 选择热点产业
    async selectHotIndustry(industry) {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:878", "选择热点产业:", industry);
      this.showHotIndustryModal = false;
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      switch (industry.uniqueCode) {
        case "hot_ZJTX":
          this.searchSpecializedEnterprises();
          break;
        case "hot_ZJXZ":
          this.navigateToScholars();
          break;
        default:
          this.showDevelopingToast(industry.name);
          break;
      }
    },
    // 搜索专精特新企业
    async searchSpecializedEnterprises() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:907", "搜索专精特新企业...");
        const searchParams = {
          keyword: "",
          pageNum: 1,
          pageSize: 20,
          regions: [],
          industryIds: [],
          companyScales: [],
          companyTypes: [],
          qualifications: ["专精特新企业", '专精特新"小巨人"企业'],
          // 专精特新相关资质
          fundingRounds: []
        };
        common_vendor.index.__f__("log", "at pages/industry/index.vue:922", "专精特新搜索参数:", searchParams);
        const response = await utils_request.request.post("/miniapp/enterprise/search", searchParams);
        common_vendor.index.__f__("log", "at pages/industry/index.vue:926", "专精特新企业搜索API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const searchResults = response.data.rows || [];
          const total = response.data.total || 0;
          common_vendor.index.__f__("log", "at pages/industry/index.vue:932", "专精特新企业搜索结果:", searchResults, "总数:", total);
          this.navigateToEnterpriseListWithResults(searchResults, total, "专精特新企业");
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:937", "专精特新企业搜索失败:", response);
          common_vendor.index.showToast({
            title: "搜索失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:945", "搜索专精特新企业异常:", error);
        common_vendor.index.showToast({
          title: "搜索异常，请重试",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 跳转到专家学者页面
    navigateToScholars() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:956", "跳转到专家学者页面");
      common_vendor.index.navigateTo({
        url: "/pages/industry/scholars"
      });
    },
    // 显示开发中提示
    showDevelopingToast(name) {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:964", "显示开发中提示:", name);
      common_vendor.index.showToast({
        title: `${name}开发中`,
        icon: "none",
        duration: 2e3
      });
    },
    // 跳转到企业列表页面（带搜索结果）
    navigateToEnterpriseListWithResults(searchResults, total, searchType) {
      const searchData = {
        results: searchResults,
        total,
        searchParams: {
          keyword: "",
          region: "",
          industry: "",
          scale: "",
          type: "",
          qualification: searchType,
          // 搜索类型作为资质显示
          round: ""
        }
      };
      const encodedData = encodeURIComponent(JSON.stringify(searchData));
      common_vendor.index.navigateTo({
        url: `/pages/industry/list?searchData=${encodedData}&regionName=${encodeURIComponent("搜索结果")}&subsectionName=${encodeURIComponent(searchType)}`
      });
    },
    // 选择城市地区
    async selectCityArea(city) {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:998", "选择城市地区:", city);
      this.showCityAreaModal = false;
      const hasPermission = await this.checkProfilePermission();
      if (!hasPermission) {
        return;
      }
      this.selectedRegion = city.name;
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1009", "设置地区筛选条件:", this.selectedRegion);
      this.searchEnterprises();
    },
    // 进入产业详情
    async goToIndustryDetail(industry) {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1017", "进入产业详情:", industry);
      const hasPermission = await this.checkFullPermission();
      if (!hasPermission) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/industry/map?industryName=${encodeURIComponent(industry.name)}&industryId=${industry.id}`
      });
    },
    // 导航到首页
    navigateToHome() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1033", "点击首页");
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    // 导航到人脉资源
    navigateToContacts() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1041", "点击人脉资源");
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts-new"
      });
    },
    // 导航到需求广场
    navigateToDemandSquare() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1049", "点击需求广场");
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    },
    // 导航到我的
    navigateToMine() {
      common_vendor.index.__f__("log", "at pages/industry/index.vue:1057", "点击我的");
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 获取热点产业数据
    async getHotIndustriesData() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1066", "获取热点产业数据...");
        const response = await utils_request.request.get("/miniapp/industry/region/app/hotIndustry");
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1069", "热点产业API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const hotIndustriesData = response.data.data || [];
          this.hotIndustries = hotIndustriesData.map((item) => ({
            id: item.id,
            name: item.name || "未知产业",
            uniqueCode: item.uniqueCode,
            remark: item.remark,
            image: utils_imageUtils.processServerImageUrl(item.coverImage)
          }));
          common_vendor.index.__f__("log", "at pages/industry/index.vue:1080", "处理后的热点产业数据:", this.hotIndustries);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:1082", "获取热点产业数据失败");
          this.hotIndustries = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:1086", "获取热点产业数据失败:", error);
        this.hotIndustries = [];
      }
    },
    // 获取城市地区数据
    async getCityAreasData() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1094", "获取城市地区数据...");
        const response = await utils_request.request.get("/miniapp/industry/region/app/list/2");
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1097", "城市地区API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const cityAreasData = response.data.data || [];
          this.cityAreas = cityAreasData.map((item) => ({
            id: item.id,
            name: item.name || item.regionName || "未知地区",
            image: utils_imageUtils.processServerImageUrl(item.coverImage)
          }));
          common_vendor.index.__f__("log", "at pages/industry/index.vue:1106", "处理后的城市地区数据:", this.cityAreas);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:1108", "获取城市地区数据失败");
          this.cityAreas = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:1112", "获取城市地区数据失败:", error);
        this.cityAreas = [];
      }
    },
    // 获取筛选行业选项数据
    async getIndustryFilterOptions() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1120", "获取筛选行业选项数据...");
        if (this.allIndustryList.length === 0) {
          await this.getIndustryTreeData();
        }
        const industryFilterOptions = this.allIndustryList.map((item) => ({
          label: item.nodeInfo.nodeName,
          value: item.nodeInfo.nodeName,
          percentage: ""
          // 可以根据需要添加百分比显示
        }));
        this.industryOptions = [
          { label: "全 部", value: "", percentage: "" },
          ...industryFilterOptions
        ];
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1140", "处理后的行业筛选选项:", this.industryOptions);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:1142", "获取行业筛选选项失败:", error);
      }
    },
    // 获取企业类型选项数据
    async getEnterpriseTypeOptions() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1149", "获取企业类型选项数据...");
        const response = await utils_request.request.get("/miniapp/resourceCatalog/listByType/enterprise_type");
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1152", "企业类型API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const typeData = response.data.data || [];
          const typeFilterOptions = typeData.map((item) => ({
            label: item.name || item.resourceName || "未知类型",
            value: item.name || item.resourceName || ""
          }));
          this.typeOptions = [
            { label: "全 部", value: "" },
            ...typeFilterOptions
          ];
          common_vendor.index.__f__("log", "at pages/industry/index.vue:1167", "处理后的企业类型筛选选项:", this.typeOptions);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:1169", "获取企业类型选项失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:1172", "获取企业类型选项失败:", error);
      }
    },
    // 获取行业树数据
    async getIndustryTreeData() {
      try {
        const response = await utils_request.request.get("/miniapp/industry/tree/enterprises", {
          level: 1
          // 获取一级行业数据
        });
        common_vendor.index.__f__("log", "at pages/industry/index.vue:1183", "行业数据响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          this.allIndustryList = response.data.data || [];
          this.industryList = this.allIndustryList.slice(0, 5).map((item) => ({
            id: item.nodeInfo.nodeId,
            name: item.nodeInfo.nodeName,
            count: item.statistics.totalCount || 0,
            icon: utils_imageUtils.processServerImageUrl(item.nodeInfo.coverImage),
            coverImage: item.nodeInfo.coverImage
          }));
          common_vendor.index.__f__("log", "at pages/industry/index.vue:1196", "处理后的行业列表:", this.industryList);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/index.vue:1198", "获取行业数据失败，显示空状态");
          this.industryList = [];
          this.allIndustryList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/index.vue:1203", "获取行业数据失败:", error);
        this.industryList = [];
        this.allIndustryList = [];
        common_vendor.index.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2e3
        });
      }
    }
  },
  onLoad() {
    this.getIndustryTreeData();
    this.getHotIndustriesData();
    this.getCityAreasData();
    this.getIndustryFilterOptions();
    this.getEnterpriseTypeOptions();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("filter.png"),
    b: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    f: $options.getImagePath("right.png"),
    g: common_vendor.o((...args) => $options.openHotIndustryModal && $options.openHotIndustryModal(...args)),
    h: $options.getImagePath("right.png"),
    i: common_vendor.o((...args) => $options.openCityAreaModal && $options.openCityAreaModal(...args)),
    j: $options.getImagePath("list_bg.png"),
    k: common_vendor.o((...args) => $options.viewMoreIndustries && $options.viewMoreIndustries(...args)),
    l: $data.industryList.length > 0
  }, $data.industryList.length > 0 ? {
    m: common_vendor.f($data.industryList, (industry, index, i0) => {
      return {
        a: industry.icon,
        b: common_vendor.t(industry.name),
        c: common_vendor.t(industry.count),
        d: index,
        e: common_vendor.o(($event) => $options.goToIndustryDetail(industry), index)
      };
    }),
    n: $options.getImagePath("right.png")
  } : {}, {
    o: $data.showFilter
  }, $data.showFilter ? {
    p: common_vendor.o((...args) => $options.hideFilterModal && $options.hideFilterModal(...args))
  } : {}, {
    q: $data.showFilter
  }, $data.showFilter ? common_vendor.e({
    r: $data.searchKeyword
  }, $data.searchKeyword ? {
    s: common_vendor.t($data.searchKeyword)
  } : {}, {
    t: common_vendor.f($data.filterCategories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: index,
        c: $data.activeCategory === index ? 1 : "",
        d: common_vendor.o(($event) => $options.scrollToSection(index), index)
      };
    }),
    v: common_vendor.f($data.regionOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedRegion === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectRegion(option.value), index)
      };
    }),
    w: common_vendor.f($data.industryOptions, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(option.label),
        b: option.percentage
      }, option.percentage ? {
        c: common_vendor.t(option.percentage)
      } : {}, {
        d: index,
        e: $data.selectedIndustry === option.value ? 1 : "",
        f: common_vendor.o(($event) => $options.selectIndustry(option.value), index)
      });
    }),
    x: common_vendor.f($data.scaleOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedScale === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectScale(option.value), index)
      };
    }),
    y: common_vendor.f($data.typeOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedType === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectType(option.value), index)
      };
    }),
    z: common_vendor.f($data.qualificationOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedQualification === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectQualification(option.value), index)
      };
    }),
    A: common_vendor.f($data.roundOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedRound === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectRound(option.value), index)
      };
    }),
    B: $data.scrollTop,
    C: $data.scrollIntoView,
    D: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args))
  }) : {}, {
    E: $data.showHotIndustryModal
  }, $data.showHotIndustryModal ? {
    F: common_vendor.o(($event) => $data.showHotIndustryModal = false)
  } : {}, {
    G: $data.showHotIndustryModal
  }, $data.showHotIndustryModal ? common_vendor.e({
    H: $data.hotIndustries.length > 0
  }, $data.hotIndustries.length > 0 ? {
    I: common_vendor.f($data.hotIndustries, (industry, k0, i0) => {
      return {
        a: industry.image,
        b: common_vendor.t(industry.name),
        c: industry.id,
        d: common_vendor.o(($event) => $options.selectHotIndustry(industry), industry.id)
      };
    })
  } : {}) : {}, {
    J: $data.showCityAreaModal
  }, $data.showCityAreaModal ? {
    K: common_vendor.o(($event) => $data.showCityAreaModal = false)
  } : {}, {
    L: $data.showCityAreaModal
  }, $data.showCityAreaModal ? common_vendor.e({
    M: $data.cityAreas.length > 0
  }, $data.cityAreas.length > 0 ? {
    N: common_vendor.f($data.cityAreas, (city, k0, i0) => {
      return {
        a: city.image,
        b: common_vendor.t(city.name),
        c: city.id,
        d: common_vendor.o(($event) => $options.selectCityArea(city), city.id)
      };
    })
  } : {}) : {}, {
    O: common_vendor.o((...args) => $options.completeInfo && $options.completeInfo(...args)),
    P: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    Q: $options.getImagePath("bottom_order_icon1.png"),
    R: common_vendor.o((...args) => $options.navigateToHome && $options.navigateToHome(...args)),
    S: $options.getImagePath("bottom_order_icon2.png"),
    T: common_vendor.o((...args) => $options.navigateToContacts && $options.navigateToContacts(...args)),
    U: $options.getImagePath("bottom_order_icon.png"),
    V: common_vendor.o((...args) => $options.navigateToDemandSquare && $options.navigateToDemandSquare(...args)),
    W: $options.getImagePath("bottom_order_icon3_active.png"),
    X: $options.getImagePath("bottom_order_icon4.png"),
    Y: common_vendor.o((...args) => $options.navigateToMine && $options.navigateToMine(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-71433351"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/index.js.map
