.invite-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #fee4ab 20%, #fbede2 100%);
  padding-bottom: 1rpx;
}
.header-section {
  position: relative;
  width: 100%;
  margin-bottom: 120rpx;
}
.top-image {
  width: 100%;
  display: block;
}
.invite-button-wrapper {
  position: absolute;
  bottom: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 500rpx;
}
.invite-button {
  position: relative;
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.button-bg {
  width: 100%;
  height: 100%;
}
.button-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  font-size: 32rpx;
  font-weight: bold;
  color: #f9e3ae;
  line-height: 1;
  /* 重要：消除行高影响 */
}
.process-section,
.earnings-section,
.records-section {
  margin: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 30rpx 50rpx;
  box-shadow: 0 4rpx 8rpx rgba(243, 202, 119, 0.8);
  border: 1px solid #f3c465;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  background: #f1a162;
  padding: 8rpx 40rpx;
  border-radius: 40rpx;
  color: #ffffff;
}
/* 上方：logo + 虚线区域 */
.process-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.step-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f1a162;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 154, 86, 0.3);
}
.step-icon {
  width: 70rpx;
  height: 70rpx;
}
.process-line {
  width: 120rpx;
  height: 2rpx;
  background: transparent;
  border-top: 2rpx dashed #ff9a56;
}
/* 下方：文字区域 */
.process-texts {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 20rpx;
}
.step-text-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.step-text {
  font-size: 26rpx;
  color: #f1a162;
  font-weight: 600;
  text-align: center;
  line-height: 1.4;
}
.earnings-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.earnings-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.earnings-number {
  font-size: 60rpx;
  font-weight: bold;
  color: #f1a162;
  line-height: 1;
}
.earnings-unit {
  font-size: 24rpx;
  color: #f1a162;
  margin-top: 8rpx;
}
.earnings-label {
  font-size: 24rpx;
  color: #c89269;
  margin-top: 10rpx;
}
.earnings-divider {
  width: 2rpx;
  height: 80rpx;
  background: #e5e5e5;
}
.records-list {
  display: flex;
  flex-direction: column;
}
.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.record-item:last-child {
  border-bottom: none;
}
.record-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
}
.avatar-img {
  width: 100%;
  height: 100%;
}
.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.record-name {
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.record-time {
  font-size: 24rpx;
  color: #999999;
}
.record-reward {
  display: flex;
  align-items: center;
}
.reward-text {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 500;
}
.records-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
}
.empty-text {
  font-size: 24rpx;
  color: #bc7b50;
  text-align: center;
}
/* 分享海报弹窗样式 */
.poster-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.poster-modal-container {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.poster-content {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
.poster-loading {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-text {
  font-size: 32rpx;
  color: #666666;
}
.poster-image {
  width: 100%;
  display: block;
}
.poster-tip {
  margin-top: 30rpx;
}
.tip-text {
  font-size: 28rpx;
  color: #fff;
  text-align: center;
}
.poster-close {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-circle {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
}
.close-icon {
  width: 35rpx;
  height: 35rpx;
}
/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
