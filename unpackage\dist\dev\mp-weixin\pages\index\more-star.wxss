.more-star-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #d3e2ff, #fff);
  padding-bottom: 120rpx;
}
.header-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
}
.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #003399;
}
/* 内容区域 - 纯色背景 */
.content-area {
  position: relative;
  z-index: 0;
}
/* 科技之星区域 */
.section-block {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.7);
  padding-top: 30rpx;
}
.list_bg {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.tech-star-block {
  margin-bottom: 30rpx;
}
/* 标题区域 */
.section-header {
  position: relative;
  z-index: 20;
  padding: 30rpx 30rpx 20rpx 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #003399;
}
.star-content {
  position: relative;
  z-index: 20;
}
.star-item {
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  align-items: flex-start;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f2f2f5;
}
.star-item:last-child {
  margin-bottom: 0;
}
.star-avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 50rpx;
  object-fit: cover;
  flex-shrink: 0;
}
.star-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.star-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #000000;
}
.star-position {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.star-desc {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/* 空状态样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 30rpx;
  position: relative;
  z-index: 20;
}
.star-empty {
  padding: 100rpx 30rpx;
}
.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  text-align: center;
}
/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
  padding-bottom: 20rpx;
}
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.tab-center-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  position: absolute;
  top: -20rpx;
}
.tab-center-image {
  width: 48rpx;
  height: 48rpx;
}
.tab-text {
  font-size: 20rpx;
  color: #666;
}
