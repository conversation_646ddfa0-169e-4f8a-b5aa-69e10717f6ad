/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mentor-matrix-container.data-v-78fd55f5 {
  min-height: 100vh;
  background: #f3f8fe;
}
.header-bg-img.data-v-78fd55f5 {
  width: 100%;
  height: 100%;
  z-index: 1;
}
.header-content.data-v-78fd55f5 {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}
.header-title.data-v-78fd55f5 {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.header-subtitle.data-v-78fd55f5 {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}
.header-desc.data-v-78fd55f5 {
  font-size: 24rpx;
  opacity: 0.9;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}
.mentor-content.data-v-78fd55f5 {
  width: 94%;
  margin-left: 3%;
  background-color: #fff;
  margin-top: -30rpx;
  border-radius: 16rpx;
  position: relative;
  z-index: 20;
  box-shadow: 0 4rpx 16rpx #c5cfe8;
}

/* 导师列表容器 */
.mentor-list-container.data-v-78fd55f5 {
  padding: 30rpx 20rpx 50rpx;
}

/* 加载状态 */
.loading-container.data-v-78fd55f5 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-text.data-v-78fd55f5 {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-container.data-v-78fd55f5 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.empty-text.data-v-78fd55f5 {
  font-size: 28rpx;
  color: #999;
}

/* 导师网格布局 */
.mentor-grid.data-v-78fd55f5 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 10rpx;
}

/* 导师卡片 */
.mentor-card.data-v-78fd55f5 {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx 40rpx;
  /* 增加底部padding */
  box-shadow: 0 4rpx 12rpx #c5cfe8;
  /* 减少阴影扩散 */
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 320rpx;
  /* 增加最小高度 */
  margin-bottom: 20rpx;
  /* 添加底部外边距为阴影留空间 */
}
.mentor-card.data-v-78fd55f5:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

/* 导师头像 */
.mentor-avatar.data-v-78fd55f5 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
}
.avatar-img.data-v-78fd55f5 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
}

/* 导师信息 */
.mentor-info.data-v-78fd55f5 {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.mentor-name.data-v-78fd55f5 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.mentor-titles.data-v-78fd55f5 {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  flex: 1;
}
.mentor-title.data-v-78fd55f5 {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
  display: block;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.mentor-list-container.data-v-78fd55f5 {
    padding: 20rpx 15rpx 40rpx;
}
.mentor-grid.data-v-78fd55f5 {
    gap: 15rpx;
    padding: 0 8rpx;
}
.mentor-card.data-v-78fd55f5 {
    padding: 25rpx 15rpx 35rpx;
    /* 增加底部padding */
    min-height: 300rpx;
    /* 增加最小高度 */
    margin-bottom: 15rpx;
    /* 响应式下的底部边距 */
}
.mentor-avatar.data-v-78fd55f5 {
    width: 80rpx;
    height: 80rpx;
}
.mentor-name.data-v-78fd55f5 {
    font-size: 26rpx;
}
.mentor-title.data-v-78fd55f5 {
    font-size: 20rpx;
}
}