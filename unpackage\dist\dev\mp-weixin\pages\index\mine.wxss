.mine-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #c3dbff 0%, #f3f8fe 100%);
  padding-bottom: 120rpx;
  position: relative;
  overflow: hidden;
}
/* 个人资料完善提示条 */
.profile-tip-bar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  box-shadow: 0 4rpx 8rpx #6a8dd6;
}
.tip-content {
  flex: 1;
  cursor: pointer;
}
.tip-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #5f3901;
  display: block;
  margin-bottom: 8rpx;
}
.tip-desc {
  font-size: 26rpx;
  color: #5f3602;
  line-height: 1.4;
}
.tip-link {
  color: #c09a50;
  font-weight: 500;
}
.tip-action {
  margin-left: 20rpx;
  cursor: pointer;
}
.action-btn {
  background: #ebb368;
  color: #633c02;
  font-size: 28rpx;
  font-weight: 500;
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.tip-action:active .action-btn {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.background-img {
  width: 100%;
  position: absolute;
  top: 100rpx;
  left: 0;
  z-index: 1;
}
.header-section {
  padding: 60rpx 40rpx 30rpx;
  position: relative;
  z-index: 2;
}
.user-card {
  position: relative;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.user-basic-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.user-info {
  flex: 1;
  margin-right: 0rpx;
}
.user-name-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 6rpx;
}
.username {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}
.location {
  font-size: 22rpx;
  color: #333;
}
.user-school {
  margin-bottom: 6rpx;
}
.school-name,
.major {
  font-size: 22rpx;
  color: #333;
  margin-right: 8rpx;
}
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}
.left-text {
  font-size: 24rpx;
  color: #333;
}
.right-text {
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  text-align: right;
}
.points-section {
  flex-shrink: 0;
}
.points-badge {
  position: absolute;
  right: 32rpx;
  background: #dcb955;
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  display: flex;
  align-items: center;
}
.points-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.points-text {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}
.user-tags {
  display: flex;
  margin-bottom: 20rpx;
}
.tag-item {
  background: #aacaf9;
  border-radius: 20rpx;
  padding: 0rpx 15rpx 5rpx;
  margin-right: 16rpx;
}
.tag-text {
  font-size: 26rpx;
  color: #fff;
}
.divider-line {
  width: 100%;
  height: 1rpx;
  background: #dde0e9;
  margin: 30rpx 0 20rpx;
}
.user-intro {
  margin-bottom: 20rpx;
  display: flex;
}
.intro-label {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  white-space: nowrap;
  padding-top: 5rpx;
}
.intro-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
.more-button {
  display: flex;
  justify-content: flex-end;
}
.more-text {
  font-size: 28rpx;
  color: #fff;
  padding: 10rpx 25rpx;
  background: #00389f;
  border-radius: 16rpx;
}
.points-mall-section {
  padding: 0rpx 40rpx 30rpx;
  position: relative;
  z-index: 2;
}
.points-mall-card {
  background: linear-gradient(0deg, #6b91e9 0%, #4978da 100%);
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  position: relative;
  overflow: hidden;
}
.mall-title {
  font-size: 34rpx;
  color: white;
  font-weight: bold;
}
.mall-subtitle {
  font-size: 26rpx;
  color: white;
  opacity: 0.8;
}
.mall-right {
  position: absolute;
  bottom: -18rpx;
  right: 16rpx;
}
.mall-icon {
  width: 100rpx;
  height: 100rpx;
}
.menu-section {
  background: white;
  margin: 0 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 32rpx 32rpx 52rpx;
  z-index: 2;
}
.menu-item {
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-item:active {
  background: #f8f9fa;
}
.menu-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 28rpx;
}
.menu-content {
  flex: 1;
}
.menu-text {
  font-size: 34rpx;
  color: #333;
}
.menu-status {
  font-size: 26rpx;
  color: #00389f;
  margin-left: 8rpx;
}
.menu-arrow {
  font-size: 34rpx;
  color: #333;
}
/* 底部导航栏样式 - 与首页保持一致 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -6rpx 16rpx #c8d5f2;
  z-index: 100;
  padding-bottom: 20rpx;
}
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  position: relative;
}
.tab-icon {
  width: 44rpx;
  height: 44rpx;
}
.tab-text {
  font-size: 22rpx;
  color: #444;
}
.tab-item.active .tab-text {
  color: #003399;
}
.tab-center-icon {
  width: 88rpx;
  height: 88rpx;
  background-color: #003399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: relative;
  top: -48rpx;
}
.tab-center-image {
  width: 60rpx;
  height: 60rpx;
}
.tab-center-text {
  font-size: 22rpx;
  color: #444;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
