.publish-container {
  width: 100%;
  min-height: 100vh;
  background: #e8f0ff;
  padding-top: 30rpx;
}
.header-section {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  padding: 60rpx 0 40rpx 0;
  color: white;
}
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
}
.back-btn {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  width: 60rpx;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  flex: 1;
  text-align: center;
}
.header-placeholder {
  width: 60rpx;
}
.publish-list {
  padding: 0 30rpx;
}
/* 加载和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 需求列表样式 - 与需求广场一致 */
.demand-item {
  position: relative;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 30rpx 30rpx 120rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx #c2d1f1;
}
.demand-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20rpx;
}
.demand-category {
  padding: 26rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: white;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #333;
}
/* 根据categoryCode的分类样式 - 与需求广场完全一致 */
.financing-category {
  background: linear-gradient(180deg, #4A90E2 0%, #357ABD 100%);
  /* 蓝色 - 融资对接 */
}
.technology-category {
  background: linear-gradient(180deg, #7ED321 0%, #5CB85C 100%);
  /* 绿色 - 技术合作 */
}
.exposure-category {
  background: linear-gradient(180deg, #50E3C2 0%, #4ECDC4 100%);
  /* 青色 - 曝光 */
}
.scenario-category {
  background: linear-gradient(180deg, #BD10E0 0%, #9013FE 100%);
  /* 紫色 - 资源场景 */
}
.qualification-category {
  background: linear-gradient(180deg, #F5A623 0%, #D68910 100%);
  /* 橙色 - 政策资质 */
}
.office-category {
  background: linear-gradient(180deg, #FF6B9D 0%, #E91E63 100%);
  /* 粉色 - 办公载体 */
}
.factory-category {
  background: linear-gradient(180deg, #8B4513 0%, #A0522D 100%);
  /* 棕色 - 厂房载体 */
}
.consult-category {
  background: linear-gradient(180deg, #95A5A6 0%, #7F8C8D 100%);
  /* 灰色 - 管理咨询 */
}
.default-category {
  background: linear-gradient(180deg, #A8A8A8 0%, #808080 100%);
  /* 默认灰色 */
}
.top-badge {
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}
.demand-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.demand-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 显示 2 行 */
  overflow: hidden;
}
.demand-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.demand-date {
  font-size: 24rpx;
  color: #999;
}
.demand-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.view-count {
  font-size: 24rpx;
  color: #999;
}
.demand-status {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}
/* 状态颜色 - 与详情页面保持一致 */
.demand-status.status-pending {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
  /* 橙色 - 待审核 */
}
.demand-status.status-published {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  /* 绿色 - 已发布 */
}
.demand-status.status-docked {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  /* 蓝色 - 已对接 */
}
.demand-status.status-offline {
  background: linear-gradient(135deg, #8c8c8c, #595959);
  /* 灰色 - 已下架 */
}
.demand-status.status-rejected {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
  /* 红色 - 审核拒绝 */
}
.demand-status.status-unknown {
  background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
  /* 浅灰色 - 未知状态 */
}
.status-text {
  font-size: 22rpx;
  color: white;
}
