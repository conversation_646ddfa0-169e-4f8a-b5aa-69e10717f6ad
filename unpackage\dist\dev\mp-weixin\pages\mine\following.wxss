.following-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f7fc;
}
.tab-selector {
  display: flex;
  background: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.tab-selector .tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}
.tab-selector .tab-item .tab-text {
  font-size: 32rpx;
  color: #666666;
  font-weight: 500;
}
.tab-selector .tab-item.active .tab-text {
  color: #003399;
  font-weight: 600;
}
.tab-selector .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #003399;
  border-radius: 2rpx;
}
.following-list {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.user-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  position: relative;
}
.avatar-container {
  margin-right: 24rpx;
}
.avatar-container .avatar-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: #f0f0f0;
}
.user-info {
  flex: 1;
  min-width: 0;
}
.user-info .name-location-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}
.user-info .name-location-row .user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}
.user-info .name-location-row .location {
  font-size: 22rpx;
  color: #333;
  font-weight: 900;
}
.user-info .education-row {
  margin-bottom: 8rpx;
}
.user-info .education-row .education {
  font-size: 24rpx;
  color: #333;
  font-weight: 900;
}
.user-info .work-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.user-info .work-row .company {
  max-width: 300rpx;
  font-size: 24rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-info .work-row .position {
  font-size: 24rpx;
  color: #666666;
  margin-left: 16rpx;
  flex-shrink: 0;
}
.user-info .industry-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}
.user-info .industry-tags .tag-item {
  background: #aacaf9;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
}
.user-info .industry-tags .tag-item .tag-text {
  font-size: 20rpx;
  color: #ffffff;
}
.action-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  margin-left: 16rpx;
}
.action-area .follow-back-btn {
  background: #41c8dc;
  border-radius: 100rpx;
  padding: 12rpx 0;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-area .follow-back-btn .follow-back-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}
.action-area .chat-btn {
  background: #2196F3;
  border-radius: 100rpx;
  padding: 12rpx 0;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-area .chat-btn .chat-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}
.action-area .detail-btn {
  background: #dc4174;
  border-radius: 100rpx;
  padding: 12rpx 0;
  width: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-area .detail-btn .detail-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}
.user-card:active {
  transform: scale(0.98);
  opacity: 0.8;
}
@media screen and (max-width: 750rpx) {
.user-card {
    padding: 24rpx;
}
.avatar-container .avatar-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
}
.user-info .name-location-row .user-name {
    font-size: 30rpx;
}
}
/* 聊天解锁弹框样式 */
.unlock-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.unlock-dialog {
  position: relative;
  width: 540rpx;
  overflow: visible;
}
.dialog-close {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 54rpx;
  height: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ed4545;
  border-radius: 50%;
  z-index: 10000;
  border: 3rpx solid #fff;
}
.close-icon {
  width: 30rpx;
  height: 30rpx;
}
.dialog-content {
  width: 100%;
  position: relative;
}
.dialog-background {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20rpx;
}
.unlock-button {
  position: absolute;
  bottom: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #ed4545;
  border-radius: 50rpx;
  padding: 12rpx 80rpx;
  border: 3rpx solid #fff;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.unlock-button-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
}
