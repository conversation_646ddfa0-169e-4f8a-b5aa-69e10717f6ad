.haitang-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.header-section {
  position: relative;
}
/* 顶图容器样式 */
.header-image-container {
  width: 100%;
  position: relative;
}
.header-image {
  width: 100%;
  display: block;
  min-height: 300rpx;
  object-fit: cover;
}
/* 默认内容样式（无顶图时显示） */
.header-content {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  padding: 60rpx 40rpx 80rpx 40rpx;
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  letter-spacing: 2rpx;
}
.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}
.function-cards {
  padding: 40rpx 30rpx 0;
}
.card-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.function-card {
  width: 39%;
  background: white;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx #cedcf4;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  position: relative;
  height: 160rpx;
  overflow: hidden;
  border-radius: 20rpx;
}
.function-card image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.card-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
  position: relative;
  z-index: 10;
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}
.card-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  text-align: left;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  width: 80%;
}
/* 不同卡片的背景色 */
.intro-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}
.register-card {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}
.guidance-card {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}
.consult-card {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}
/* 区块通用样式 */
.section-block {
  margin: 50rpx 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  position: relative;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.list_bg {
  width: 100%;
  position: absolute;
  top: -4rpx;
  left: 0;
  z-index: 16;
  border-radius: 20rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  position: relative;
}
.section-title {
  font-size: 32rpx;
  font-weight: 900;
  color: #003399;
  margin-left: 20rpx;
  z-index: 100;
}
.more-link {
  font-size: 28rpx;
  color: #003399;
  margin-right: 10rpx;
  z-index: 100;
}
.news-list {
  padding: 30rpx;
  position: relative;
  z-index: 20;
}
.news-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.news-item:last-child {
  border-bottom: none;
}
.news-avatar {
  width: 200rpx;
  height: 130rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}
.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.news-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.news-time {
  font-size: 22rpx;
  color: #999;
}
.video-container {
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 30rpx;
  position: relative;
  z-index: 20;
}
.video-swiper {
  height: 100%;
}
.video-item {
  height: 100%;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
}
.video-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.video-poster {
  width: 100%;
  height: 100%;
}
.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}
.video-play-btn {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}
.video-play-icon {
  font-size: 48rpx;
  color: #003399;
  margin-left: 8rpx;
}
.play-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.play-icon {
  color: #013ba7;
  font-size: 46rpx;
  margin-left: 8rpx;
}
.video-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx 30rpx;
  color: white;
}
.video-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.video-desc {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}
/* 空状态样式 */
.video-empty,
.empty-state {
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-content {
  text-align: center;
}
.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 点击效果 */
.function-card:active,
.news-item:active {
  transform: scale(0.98);
  transition: transform 0.2s ease;
}
