"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      regionName: "天津地区",
      total: 0,
      loading: false,
      loadingMore: false,
      // 加载更多状态
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      // 是否还有更多数据
      // 企业列表
      companiesList: [],
      // 用于区分数据来源
      isFromSearchData: false,
      // 是否来自搜索数据
      industryId: null,
      // 产业ID
      searchParams: null,
      // 搜索参数（用于分页时继续使用相同的搜索条件）
      // 联系人信息弹窗
      showContactPopup: false,
      contactInfo: {
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  async onLoad(options) {
    const hasPermission = await this.checkAccessPermission();
    if (!hasPermission) {
      common_vendor.index.__f__("log", "at pages/industry/list.vue:159", "🔐 企业列表 - 权限检查失败，停止加载页面内容");
      return;
    }
    if (options.regionName) {
      this.regionName = decodeURIComponent(options.regionName);
    }
    if (options.industryId) {
      this.industryId = options.industryId;
    }
    if (options.searchData) {
      try {
        const searchData = JSON.parse(decodeURIComponent(options.searchData));
        common_vendor.index.__f__("log", "at pages/industry/list.vue:175", "接收到搜索数据:", searchData);
        this.loadCompaniesFromSearchData(searchData);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:178", "解析搜索数据失败:", error);
        this.loadCompaniesList();
      }
    } else if (options.enterprisesData) {
      try {
        const enterprisesData = JSON.parse(decodeURIComponent(options.enterprisesData));
        this.loadCompaniesFromData(enterprisesData);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:188", "解析企业数据失败:", error);
        this.loadCompaniesList();
      }
    } else {
      this.loadCompaniesList();
    }
    this.setNavigationTitle();
  },
  // 页面触底事件
  onReachBottom() {
    common_vendor.index.__f__("log", "at pages/industry/list.vue:202", "触底加载更多，当前状态:", {
      isFromSearchData: this.isFromSearchData,
      loading: this.loading,
      loadingMore: this.loadingMore,
      hasMore: this.hasMore,
      currentPage: this.currentPage,
      companiesLength: this.companiesList.length,
      total: this.total
    });
    this.loadMore();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 检查用户是否可以访问产业资源
    async checkAccessPermission() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:222", "🔐 企业列表 - 检查用户产业资源访问权限...");
        common_vendor.index.__f__("log", "at pages/industry/list.vue:223", "🔐 API地址: GET /miniapp/unified-enterprise/user/status");
        const response = await utils_request.request.get("/miniapp/unified-enterprise/user/status");
        common_vendor.index.__f__("log", "at pages/industry/list.vue:226", "🔐 企业列表 - 访问权限检查响应:", response);
        if (response && response.data && response.data.code === 200) {
          const statusData = response.data;
          const status = statusData.status;
          const needCompleteInfo = statusData.needCompleteInfo;
          common_vendor.index.__f__("log", "at pages/industry/list.vue:233", "🔐 企业列表 - 用户审核状态:", {
            status,
            needCompleteInfo
          });
          if (status === "approved") {
            common_vendor.index.__f__("log", "at pages/industry/list.vue:241", "🔐 企业列表 - 审核通过，允许访问");
            return true;
          } else if (status === "pending") {
            common_vendor.index.showModal({
              title: "提示",
              content: "您的企业信息正在审核中，请耐心等待审核结果",
              showCancel: false,
              confirmText: "我知道了",
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
            return false;
          } else if (status === "rejected") {
            common_vendor.index.showModal({
              title: "提示",
              content: "您的企业信息审核未通过，请重新完善企业信息",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/industry/company-profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          } else if (status === "info_incomplete") {
            common_vendor.index.showModal({
              title: "提示",
              content: "请先完善个人信息，才能访问企业列表",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/index/profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          } else if (status === "not_submitted") {
            common_vendor.index.showModal({
              title: "提示",
              content: "请先完善企业信息，才能访问企业列表",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/industry/company-profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/list.vue:312", "🔐 企业列表 - 检查访问权限失败:", response);
          return true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:317", "🔐 企业列表 - 检查访问权限异常:", error);
        return true;
      }
      return true;
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 设置导航栏标题
    setNavigationTitle() {
      const title = this.total > 0 ? `企业列表（共${this.total}家企业）` : "企业列表";
      common_vendor.index.setNavigationBarTitle({
        title
      });
      common_vendor.index.__f__("log", "at pages/industry/list.vue:337", "设置导航栏标题:", title);
    },
    // 加载企业列表
    async loadCompaniesList() {
      try {
        if (this.currentPage === 1) {
          this.loading = true;
        } else {
          this.loadingMore = true;
        }
        common_vendor.index.__f__("log", "at pages/industry/list.vue:352", "加载企业列表，页数:", this.currentPage, "页大小:", this.pageSize);
        let searchParams;
        if (this.searchParams) {
          searchParams = {
            ...this.searchParams
            // pageNum: this.currentPage,
            // pageSize: this.pageSize
          };
          common_vendor.index.__f__("log", "at pages/industry/list.vue:364", "使用保存的搜索参数进行分页查询");
        } else {
          searchParams = {
            keyword: "",
            // pageNum: this.currentPage,
            // pageSize: this.pageSize,
            regions: [],
            industryIds: this.industryId ? [this.industryId] : [],
            // 如果有产业ID则传递
            companyScales: [],
            companyTypes: [],
            qualifications: [],
            fundingRounds: []
          };
        }
        common_vendor.index.__f__("log", "at pages/industry/list.vue:380", "企业列表搜索参数:", searchParams);
        const response = await utils_request.request.post(`/miniapp/enterprise/search?pageNum=${this.currentPage}&pageSize=${this.pageSize}`, searchParams);
        common_vendor.index.__f__("log", "at pages/industry/list.vue:383", "企业列表API响应:", response);
        if (response.success && response.data && response.data.code === 200) {
          const enterprisesData = response.data.rows || [];
          const total = response.data.total || 0;
          common_vendor.index.__f__("log", "at pages/industry/list.vue:389", "企业数据:", enterprisesData, "总数:", total);
          this.total = total;
          this.setNavigationTitle();
          const newCompanies = enterprisesData.map((company) => ({
            id: company.enterpriseId,
            name: company.enterpriseName || "未知企业",
            industry: "",
            // API中没有industryName字段
            stage: company.financingRound || "",
            region: company.location || "",
            logo: utils_imageUtils.processServerImageUrl(company.logoUrl, utils_imageUtils.getImagePath("avatar1.png")),
            tags: "",
            // API中没有tags字段
            parsedTags: [],
            parsedIndustryTags: this.parseIndustryTags(company.industryNames),
            parsedQualifications: this.parseQualifications(company.qualifications),
            scale: company.scale || "",
            type: company.enterpriseType || "",
            description: company.businessDescription || "",
            originalData: company
          }));
          if (this.currentPage === 1) {
            this.companiesList = newCompanies;
          } else {
            this.companiesList = [...this.companiesList, ...newCompanies];
          }
          this.hasMore = this.companiesList.length < this.total;
          common_vendor.index.__f__("log", "at pages/industry/list.vue:419", "是否还有更多数据:", this.hasMore, "当前数量:", this.companiesList.length, "总数:", this.total);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/list.vue:422", "企业列表API返回失败:", response);
          if (this.currentPage === 1) {
            this.companiesList = [];
            this.total = 0;
          }
          this.hasMore = false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:431", "加载企业列表失败:", error);
        if (this.currentPage === 1) {
          this.companiesList = [];
          this.total = 0;
        }
        this.hasMore = false;
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
        this.loadingMore = false;
      }
    },
    // 直接使用传递的企业数据
    loadCompaniesFromData(enterprisesData) {
      try {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:450", "使用传递的企业数据:", enterprisesData);
        this.isFromSearchData = true;
        this.hasMore = false;
        this.total = enterprisesData.length;
        this.setNavigationTitle();
        this.companiesList = enterprisesData.map((company) => ({
          id: company.enterpriseId,
          name: company.enterpriseName || "未知企业",
          industry: "",
          // API中没有industryName字段
          stage: company.financingRound || "",
          region: company.location || "",
          logo: utils_imageUtils.processServerImageUrl(company.logoUrl, utils_imageUtils.getImagePath("avatar1.png")),
          tags: "",
          // API中没有tags字段
          parsedTags: [],
          parsedIndustryTags: this.parseIndustryTags(company.industryNames),
          parsedQualifications: this.parseQualifications(company.qualifications),
          scale: company.scale || "",
          type: company.enterpriseType || "",
          description: company.businessDescription || "",
          originalData: company
        }));
        common_vendor.index.__f__("log", "at pages/industry/list.vue:477", "处理后的企业列表:", this.companiesList);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:479", "处理企业数据失败:", error);
        this.companiesList = [];
        this.total = 0;
        common_vendor.index.showToast({
          title: "数据处理失败",
          icon: "none"
        });
      }
    },
    // 解析标签
    parseTags(tagsString) {
      if (!tagsString)
        return [];
      return tagsString.split(",").filter((tag) => tag.trim()).slice(0, 3);
    },
    // 处理搜索结果数据
    loadCompaniesFromSearchData(searchData) {
      try {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:498", "处理搜索结果数据:", searchData);
        const enterprisesData = searchData.results || [];
        const total = searchData.total || 0;
        const isCompleteResult = enterprisesData.length >= total;
        if (isCompleteResult) {
          this.isFromSearchData = true;
          this.hasMore = false;
          common_vendor.index.__f__("log", "at pages/industry/list.vue:510", "搜索结果完整，禁用分页");
        } else {
          this.isFromSearchData = false;
          this.hasMore = true;
          this.searchParams = searchData.searchParams || {};
          common_vendor.index.__f__("log", "at pages/industry/list.vue:516", "搜索结果不完整，启用分页，搜索参数:", this.searchParams);
        }
        this.total = total;
        this.setNavigationTitle();
        this.companiesList = enterprisesData.map((company) => ({
          id: company.enterpriseId,
          name: company.enterpriseName || "未知企业",
          industry: "",
          // API中没有industryName字段
          stage: company.financingRound || "",
          region: company.location || "",
          logo: utils_imageUtils.processServerImageUrl(company.logoUrl, utils_imageUtils.getImagePath("avatar1.png")),
          tags: "",
          // API中没有tags字段
          parsedTags: [],
          parsedIndustryTags: this.parseIndustryTags(company.industryNames),
          parsedQualifications: this.parseQualifications(company.qualifications),
          scale: company.scale || "",
          type: company.enterpriseType || "",
          description: company.businessDescription || "",
          originalData: company
        }));
        common_vendor.index.__f__("log", "at pages/industry/list.vue:540", "处理后的搜索结果企业列表:", this.companiesList);
        if (searchData.searchParams) {
          const params = searchData.searchParams;
          let titleParts = [];
          if (params.keyword)
            titleParts.push(`"${params.keyword}"`);
          if (params.region)
            titleParts.push(params.region);
          if (params.industry)
            titleParts.push(params.industry);
          if (params.scale)
            titleParts.push(params.scale);
          if (params.type)
            titleParts.push(params.type);
          if (params.qualification)
            titleParts.push(params.qualification);
          if (params.round)
            titleParts.push(params.round);
          if (titleParts.length > 0) {
            this.regionName = titleParts.join(" · ") + " 搜索结果";
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:561", "处理搜索结果数据失败:", error);
        this.companiesList = [];
        this.total = 0;
        common_vendor.index.showToast({
          title: "数据处理失败",
          icon: "none"
        });
      }
    },
    // 解析行业标签
    parseIndustryTags(industryTagsString) {
      if (!industryTagsString)
        return [];
      if (typeof industryTagsString === "string") {
        return industryTagsString.split(",").filter((tag) => tag.trim()).slice(0, 2);
      }
      if (Array.isArray(industryTagsString)) {
        return industryTagsString.slice(0, 2);
      }
      return [];
    },
    // 解析资质标签
    parseQualifications(qualificationsString) {
      if (!qualificationsString)
        return [];
      if (typeof qualificationsString === "string") {
        return qualificationsString.split(",").filter((tag) => tag.trim()).slice(0, 1);
      }
      if (Array.isArray(qualificationsString)) {
        return qualificationsString.slice(0, 1);
      }
      return [];
    },
    // 查看企业详情
    viewCompanyDetail(company) {
      const companyData = company.originalData || company;
      const auditStatus = companyData.auditStatus || companyData.status || companyData.approvalStatus;
      common_vendor.index.__f__("log", "at pages/industry/list.vue:605", "🏢 企业审核状态检查:", {
        enterpriseName: companyData.enterpriseName,
        auditStatus
      });
      if (auditStatus && auditStatus !== "approved" && auditStatus !== "1" && auditStatus !== 1) {
        let statusMessage = "";
        switch (auditStatus) {
          case "pending":
          case "0":
          case 0:
            statusMessage = "该企业信息正在审核中，暂时无法查看详情";
            break;
          case "rejected":
          case "2":
          case 2:
            statusMessage = "该企业信息审核未通过，暂时无法查看详情";
            break;
          default:
            statusMessage = "该企业信息暂未通过审核，无法查看详情";
            break;
        }
        common_vendor.index.showToast({
          title: statusMessage,
          icon: "none",
          duration: 3e3
        });
        return;
      }
      const companyDataStr = encodeURIComponent(JSON.stringify(companyData));
      common_vendor.index.navigateTo({
        url: `/pages/industry/detail?companyData=${companyDataStr}`
      });
    },
    // 加载更多
    loadMore() {
      if (this.isFromSearchData || this.loading || this.loadingMore || !this.hasMore) {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:648", "无法加载更多:", {
          isFromSearchData: this.isFromSearchData,
          loading: this.loading,
          loadingMore: this.loadingMore,
          hasMore: this.hasMore
        });
        return;
      }
      this.currentPage++;
      this.loadCompaniesList();
    },
    // 显示完成对话框
    showCompleteDialog() {
      common_vendor.index.showModal({
        title: "提示",
        content: "已显示全部企业列表",
        showCancel: false
      });
    },
    // 我有需求 - 跳转到资源场景需求发布页面（与主页面一致）
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/industry/list.vue:672", "🎯 我有需求 - 跳转到资源场景需求发布页面");
      try {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:676", "🎯 获取分类列表，查找资源场景分类ID...");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/industry/list.vue:679", "🎯 分类列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const categories = response.data.data || [];
          const scenarioCategory = categories.find(
            (cat) => cat.categoryCode === "scenario" || cat.categoryName === "资源场景" || cat.name === "资源场景"
          );
          if (scenarioCategory) {
            const categoryId = scenarioCategory.categoryId || scenarioCategory.id;
            const categoryName = scenarioCategory.categoryName || scenarioCategory.name || "资源场景";
            common_vendor.index.__f__("log", "at pages/industry/list.vue:695", "🎯 找到资源场景分类:", { categoryId, categoryName });
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}&categoryCode=scenario`
            });
          } else {
            common_vendor.index.__f__("log", "at pages/industry/list.vue:702", "🎯 ❌ 未找到资源场景分类，使用默认方式");
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/list.vue:709", "🎯 获取分类列表失败:", response);
          common_vendor.index.navigateTo({
            url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/list.vue:716", "🎯 获取分类列表异常:", error);
        common_vendor.index.navigateTo({
          url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
        });
      }
    },
    // 我有疑问 - 与需求广场功能一致
    async haveQuestion() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/industry/list.vue:726", "📋 问题咨询");
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/industry/list.vue:735", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/industry/list.vue:752", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/industry/list.vue:758", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "企业列表咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/list.vue:771", "📋 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/list.vue:806", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.companiesList.length === 0 ? {
    c: $options.getImagePath("icon8.png")
  } : {
    d: common_vendor.f($data.companiesList, (company, index, i0) => {
      return common_vendor.e({
        a: company.logo || $options.getImagePath("avatar1.png"),
        b: common_vendor.t(company.name),
        c: company.region
      }, company.region ? {
        d: common_vendor.t(company.region)
      } : {}, {
        e: company.parsedIndustryTags && company.parsedIndustryTags.length > 0
      }, company.parsedIndustryTags && company.parsedIndustryTags.length > 0 ? {
        f: common_vendor.f(company.parsedIndustryTags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: "industry-" + tagIndex
          };
        })
      } : {}, {
        g: company.stage
      }, company.stage ? {
        h: common_vendor.t(company.stage)
      } : {}, {
        i: company.scale
      }, company.scale ? {
        j: common_vendor.t(company.scale)
      } : {}, {
        k: company.type
      }, company.type ? {
        l: common_vendor.t(company.type)
      } : {}, {
        m: company.parsedQualifications && company.parsedQualifications.length > 0
      }, company.parsedQualifications && company.parsedQualifications.length > 0 ? {
        n: common_vendor.f(company.parsedQualifications, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: "qual-" + tagIndex
          };
        })
      } : {}, {
        o: index,
        p: common_vendor.o(($event) => $options.viewCompanyDetail(company), index)
      });
    }),
    e: $options.getImagePath("right.png")
  }, {
    b: $data.companiesList.length === 0,
    f: $data.companiesList.length > 0 && !$data.isFromSearchData
  }, $data.companiesList.length > 0 && !$data.isFromSearchData ? common_vendor.e({
    g: $data.loadingMore
  }, $data.loadingMore ? {} : $data.hasMore ? {
    i: common_vendor.t($data.companiesList.length),
    j: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {
    k: common_vendor.t($data.total)
  }, {
    h: $data.hasMore
  }) : {}, {
    l: $data.companiesList.length > 0 && $data.isFromSearchData
  }, $data.companiesList.length > 0 && $data.isFromSearchData ? {
    m: common_vendor.t($data.total)
  } : {}, {
    n: common_vendor.o((...args) => $options.haveQuestion && $options.haveQuestion(...args)),
    o: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    p: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    q: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    r: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    s: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    t: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    v: $data.contactInfo.qrCodeUrl
  } : {}, {
    w: common_vendor.o(() => {
    }),
    x: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/list.js.map
