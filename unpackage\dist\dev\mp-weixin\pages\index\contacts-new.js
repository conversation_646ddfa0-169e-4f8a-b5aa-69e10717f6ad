"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      touchStartX: 0,
      touchStartY: 0,
      touchEndX: 0,
      touchEndY: 0,
      cardOffset: 0,
      isDragging: false,
      // 卡片堆叠配置参数
      cardStackConfig: {
        displayCount: 3,
        // 显示的卡片数量
        scales: [1, 0.95, 0.9],
        // 每张卡片的缩放比例
        upwardOffsets: [0, -50, -100],
        // 每张卡片的上移距离(rpx)
        zIndexes: [10, 9, 8]
        // 每张卡片的层级
      },
      // 筛选相关数据
      showFilter: false,
      // 是否显示筛选面板
      activeCategory: 0,
      // 当前选中的分类
      selectedIndustry: "",
      // 选中的行业
      selectedRegion: "",
      // 选中的地区
      selectedGrade: "",
      // 选中的年级
      scrollTop: 0,
      // 滚动位置
      scrollIntoView: "",
      // 滚动到指定元素ID
      // 筛选分类
      filterCategories: [
        { name: "行业" },
        { name: "地区" },
        { name: "年级" }
      ],
      // 行业选项（动态获取）
      industryOptions: [
        { label: "全部", value: "" }
      ],
      // 地区选项（动态获取）
      regionOptions: [
        { label: "全部", value: "" }
      ],
      // 年级选项（动态获取）
      gradeOptions: [
        { label: "全部", value: "" }
      ],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      // 活跃用户排行榜（从API获取）
      activeUsers: [],
      // 热度用户排行榜（从API获取）
      popularUsers: [],
      // 排行榜弹窗相关
      showRankingModal: false,
      rankingModalType: "",
      // 'active' 或 'popular'
      rankingModalTitle: "",
      fullRankingList: [],
      // 完整排行榜数据
      // 用户卡片数据
      userCards: []
    };
  },
  computed: {
    visibleCards() {
      const cards = this.userCards.slice(0, this.cardStackConfig.displayCount);
      if (cards.length === 0 && !this.loading && this.userCards.length === 0) {
        this.$nextTick(() => {
          this.handleNoCards();
        });
      }
      return cards;
    }
  },
  async mounted() {
    await this.initializeData();
  },
  // 页面显示时检查状态
  onShow() {
    if (this.userCards.length === 0 && !this.loading) {
      this.currentPage = 1;
      this.loadUserCards();
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 检查功能权限
    async checkPermission() {
      return await utils_profileCheck.checkFunctionPermission(false);
    },
    // 初始化数据
    async initializeData() {
      try {
        this.initRegionOptions();
        this.initGradeOptions();
        await Promise.all([
          this.loadIndustryOptions(),
          this.loadUserCards(),
          this.loadActiveUsersRanking(),
          this.loadPopularUsersRanking()
        ]);
      } catch (error) {
      }
    },
    // 加载行业选项
    async loadIndustryOptions() {
      try {
        const response = await utils_request.request.get("/miniapp/industry/level/1");
        if (response && response.data && response.data.code === 200) {
          const industryData = response.data.data || [];
          const industryOptions = [{ label: "全部", value: "" }];
          industryData.forEach((industry) => {
            industryOptions.push({
              label: industry.nodeName,
              value: industry.id
            });
          });
          this.industryOptions = industryOptions;
        }
      } catch (error) {
      }
    },
    // 初始化地区选项（使用与老人脉资源一致的省份数据）
    initRegionOptions() {
      const provinces = [
        { name: "北京" },
        { name: "天津" },
        { name: "河北" },
        { name: "山西" },
        { name: "内蒙古" },
        { name: "辽宁" },
        { name: "吉林" },
        { name: "黑龙江" },
        { name: "上海" },
        { name: "江苏" },
        { name: "浙江" },
        { name: "安徽" },
        { name: "福建" },
        { name: "江西" },
        { name: "山东" },
        { name: "河南" },
        { name: "湖北" },
        { name: "湖南" },
        { name: "广东" },
        { name: "广西" },
        { name: "海南" },
        { name: "重庆" },
        { name: "四川" },
        { name: "贵州" },
        { name: "云南" },
        { name: "西藏" },
        { name: "陕西" },
        { name: "甘肃" },
        { name: "青海" },
        { name: "宁夏" },
        { name: "新疆" },
        { name: "台湾" },
        { name: "香港" },
        { name: "澳门" }
      ];
      const regionOptions = [{ label: "全部", value: "" }];
      provinces.forEach((province) => {
        regionOptions.push({
          label: province.name,
          value: province.name
        });
      });
      this.regionOptions = regionOptions;
    },
    // 初始化年级选项（从1980到今年）
    initGradeOptions() {
      const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
      const gradeOptions = [{ label: "全部", value: "" }];
      for (let year = currentYear; year >= 1980; year--) {
        gradeOptions.push({
          label: `${year}级`,
          value: year.toString()
        });
      }
      this.gradeOptions = gradeOptions;
    },
    // 加载用户卡片数据
    async loadUserCards() {
      try {
        this.loading = true;
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.searchValue = this.searchKeyword.trim();
        }
        if (this.selectedGrade) {
          params.graduationYear = this.selectedGrade;
        }
        if (this.selectedRegion) {
          params.region = this.selectedRegion;
        }
        if (this.selectedIndustry) {
          params.industryField = this.selectedIndustry;
        }
        const response = await utils_request.request.get("/miniapp/user/list", params);
        if (response && response.data && response.data.code === 200) {
          const userData = response.data.rows || [];
          this.total = response.data.total || 0;
          const newCards = userData.map((user) => ({
            id: user.userId,
            name: user.realName || user.nickName || "未知用户",
            location: user.region || "未知地区",
            education: `${user.graduateSchool || "未知学校"} ${user.major || "未知专业"}`,
            company: user.currentCompany || "未知公司",
            position: user.positionTitle || "未知职位",
            company2: "",
            position2: "",
            tags: user.industryNames ? user.industryNames.split(",") : [],
            points: user.totalPoints || 0,
            avatar: utils_imageUtils.processServerImageUrl(user.portraitUrl || "", utils_imageUtils.getImagePath("avatar.png")),
            isCollected: false,
            // 默认未关注，后续可以通过接口检查
            // 保存原始数据
            rawData: user
          }));
          if (this.currentPage === 1) {
            this.userCards = newCards;
          } else {
            this.userCards = [...this.userCards, ...newCards];
          }
          if (newCards.length > 0) {
            await this.checkUsersFollowStatus(newCards);
          }
        } else {
          if (response && response.data && response.data.msg) {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "获取用户列表失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 搜索处理
    async handleSearch() {
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      this.currentPage = 1;
      this.loadUserCards();
    },
    // 显示筛选面板
    showFilterModal() {
      this.showFilter = true;
    },
    // 隐藏筛选面板
    hideFilterModal() {
      this.showFilter = false;
    },
    // 滚动到指定section
    scrollToSection(index) {
      this.activeCategory = index;
      if (index === 2) {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select(".filter-options").scrollOffset();
        query.select(".filter-options").boundingClientRect();
        query.exec((res) => {
          if (res && res[0] && res[1]) {
            const scrollOffset = res[0];
            const scrollView = res[1];
            const maxScrollTop = scrollOffset.scrollHeight - scrollView.height;
            this.scrollTop = 0;
            setTimeout(() => {
              const targetScrollTop = maxScrollTop > 0 ? maxScrollTop : 9999;
              this.scrollTop = targetScrollTop;
            }, 10);
          }
        });
      } else {
        this.scrollIntoView = `section-${index}`;
      }
    },
    // 滚动事件处理
    onScroll(e) {
      const scrollTop = e.detail.scrollTop;
      this.updateActiveCategory(scrollTop);
    },
    // 更新当前选中的分类
    updateActiveCategory(scrollTop) {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".filter-options").scrollOffset();
      query.select(".filter-options").boundingClientRect();
      query.selectAll(".section-title").boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1] && res[2]) {
          const scrollOffset = res[0];
          const scrollView = res[1];
          const titles = res[2];
          const maxScrollTop = scrollOffset.scrollHeight - scrollView.height;
          if (scrollTop >= maxScrollTop - 10) {
            this.activeCategory = 2;
            return;
          }
          for (let i = 0; i < 2; i++) {
            const title = titles[i];
            const titleTop = title.top - scrollView.top;
            if (titleTop >= 0 && titleTop <= 30) {
              this.activeCategory = i;
              break;
            }
          }
        }
      });
    },
    // 选择行业
    selectIndustry(value) {
      this.selectedIndustry = value;
      this.applyFilters();
    },
    // 选择地区
    selectRegion(value) {
      this.selectedRegion = value;
      this.applyFilters();
    },
    // 选择年级
    selectGrade(value) {
      this.selectedGrade = value;
      this.applyFilters();
    },
    // 应用筛选
    applyFilters() {
      this.currentPage = 1;
      this.loadUserCards();
    },
    // 添加搜索关键词
    addSearchKeyword(keyword) {
      if (!this.searchKeywords.includes(keyword)) {
        this.searchKeywords.push(keyword);
      }
    },
    // 移除搜索关键词
    removeSearchKeyword(index) {
      this.searchKeywords.splice(index, 1);
    },
    // 触摸开始
    onTouchStart(e) {
      this.touchStartX = e.touches[0].clientX;
      this.touchStartY = e.touches[0].clientY;
      this.isDragging = true;
      e.preventDefault();
    },
    // 触摸移动
    onTouchMove(e) {
      if (!this.isDragging)
        return;
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const deltaX = currentX - this.touchStartX;
      const deltaY = currentY - this.touchStartY;
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
        e.preventDefault();
        e.stopPropagation();
        this.cardOffset = deltaX;
      } else if (Math.abs(deltaY) > Math.abs(deltaX)) {
        this.isDragging = false;
        this.cardOffset = 0;
      }
    },
    // 触摸结束
    async onTouchEnd(e) {
      if (!this.isDragging)
        return;
      this.touchEndX = e.changedTouches[0].clientX;
      this.touchEndY = e.changedTouches[0].clientY;
      const deltaX = this.touchEndX - this.touchStartX;
      const deltaY = this.touchEndY - this.touchStartY;
      const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 80;
      const isFromLeftEdge = this.touchStartX < 50;
      const isRightSwipe = deltaX > 0;
      const isIOS = common_vendor.index.getSystemInfoSync().platform === "ios";
      if (isHorizontalSwipe) {
        if (isIOS && isFromLeftEdge && isRightSwipe) {
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:830", "🍎 iOS左边缘右滑，跳过处理避免系统手势冲突");
          this.cardOffset = 0;
          this.isDragging = false;
          return;
        }
        const hasPermission = await this.checkPermission();
        if (!hasPermission) {
          this.cardOffset = 0;
          this.isDragging = false;
          return;
        }
        this.removeCurrentCard();
      }
      this.cardOffset = 0;
      this.isDragging = false;
    },
    // 获取卡片样式
    getCardStyle(index) {
      const config = this.cardStackConfig;
      if (index >= config.displayCount) {
        return { display: "none" };
      }
      const baseStyle = {
        zIndex: config.zIndexes[index]
      };
      const translateX = index === 0 ? this.cardOffset : 0;
      const scale = config.scales[index];
      const translateY = config.upwardOffsets[index];
      return {
        ...baseStyle,
        opacity: 1,
        transform: `translateX(${translateX}px) scale(${scale}) translateY(${translateY}rpx)`,
        transition: this.isDragging ? "none" : "all 0.3s ease"
      };
    },
    // 卡片点击
    async handleCardTap(user) {
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/index/business-card-detail?userData=${encodeURIComponent(JSON.stringify(user.rawData))}`
      });
    },
    // 排行榜用户点击跳转到主页
    async goToUserProfile(user) {
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      const userData = {
        userId: user.userId,
        realName: user.name,
        nickName: user.name,
        portraitUrl: user.avatar
        // 其他字段可能需要从API获取完整信息
      };
      common_vendor.index.navigateTo({
        url: `/pages/index/business-card-detail?userData=${encodeURIComponent(JSON.stringify(userData))}`
      });
    },
    // 移除当前卡片
    removeCurrentCard() {
      if (this.userCards.length > 0) {
        this.userCards.shift();
      }
      if (!this.loading) {
        if (this.userCards.length === 0) {
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:925", "🔄 没有卡片了，立即重置到第一页");
          this.resetToFirstPage();
        } else if (this.userCards.length < this.cardStackConfig.displayCount) {
          this.loadMoreCards();
        }
      }
    },
    // 加载更多卡片
    async loadMoreCards() {
      if (this.loading) {
        return;
      }
      if (this.userCards.length === 0) {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:943", "🔄 没有卡片了，重新从第一页开始");
        await this.resetToFirstPage();
        return;
      }
      const maxPage = Math.ceil(this.total / this.pageSize);
      if (this.currentPage >= maxPage && this.total > 0) {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:951", "🔄 已到达最后一页，重新从第一页开始");
        await this.resetToFirstPage();
        return;
      }
      const previousCardsLength = this.userCards.length;
      this.currentPage++;
      await this.loadUserCards();
      if (this.userCards.length === previousCardsLength) {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:964", "🔄🔄🔄 检测到空数据！卡片数量没有增加，触发重置");
        await this.resetToFirstPage();
      }
    },
    // 循环加载第一页数据
    async resetToFirstPage() {
      common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:971", "🔄🔄🔄 开始循环加载第一页数据");
      this.currentPage = 1;
      await this.loadFirstPageForLoop();
      common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:979", "🔄🔄🔄 循环完成，总卡片数量:", this.userCards.length);
    },
    // 专门用于循环的第一页加载
    async loadFirstPageForLoop() {
      try {
        this.loading = true;
        const params = {
          pageNum: 1,
          pageSize: this.pageSize
        };
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.searchValue = this.searchKeyword.trim();
        }
        if (this.selectedGrade) {
          params.graduationYear = this.selectedGrade;
        }
        if (this.selectedRegion) {
          params.region = this.selectedRegion;
        }
        if (this.selectedIndustry) {
          params.industryField = this.selectedIndustry;
        }
        const response = await utils_request.request.get("/miniapp/user/list", params);
        if (response && response.data && response.data.code === 200) {
          const userData = response.data.rows || [];
          const newCards = userData.map((user) => ({
            id: user.userId,
            name: user.realName || user.nickName || "未知用户",
            location: user.region || "未知地区",
            education: `${user.graduateSchool || "未知学校"} ${user.major || "未知专业"}`,
            company: user.currentCompany || "未知公司",
            position: user.positionTitle || "未知职位",
            company2: "",
            position2: "",
            tags: user.industryNames ? user.industryNames.split(",") : [],
            points: user.totalPoints || 0,
            avatar: utils_imageUtils.processServerImageUrl(user.portraitUrl || "", utils_imageUtils.getImagePath("avatar.png")),
            isCollected: false,
            rawData: user
          }));
          this.userCards = [...this.userCards, ...newCards];
          if (newCards.length > 0) {
            await this.checkUsersFollowStatus(newCards);
          }
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理没有卡片的情况
    async handleNoCards() {
      if (this.loading) {
        return;
      }
      if (this.currentPage === 1) {
        return;
      }
      this.currentPage = 1;
      await this.loadUserCards();
      common_vendor.index.showToast({
        title: "已重新开始",
        icon: "none",
        duration: 1500
      });
    },
    // 检查用户关注状态
    async checkUsersFollowStatus(users) {
      try {
        const currentUserInfo = common_vendor.index.getStorageSync("userInfo");
        if (!currentUserInfo || !currentUserInfo.userId) {
          return;
        }
        const checkPromises = users.map(async (user) => {
          if (user.id === currentUserInfo.userId) {
            return;
          }
          try {
            const response = await utils_request.request.get(`/miniapp/user/checkFollowStatus/${user.id}`);
            if (response && response.data && response.data.code === 200) {
              user.isCollected = response.data.data.isFollowing || false;
            }
          } catch (error) {
            user.isCollected = false;
          }
        });
        await Promise.all(checkPromises);
      } catch (error) {
      }
    },
    // 关注卡片
    async collectCard(user) {
      var _a;
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      if (!user.id) {
        common_vendor.index.showToast({
          title: "无法获取用户信息",
          icon: "none"
        });
        return;
      }
      try {
        const currentUserInfo = common_vendor.index.getStorageSync("userInfo");
        if (currentUserInfo && currentUserInfo.userId === user.id) {
          common_vendor.index.showToast({
            title: "不能关注自己",
            icon: "none"
          });
          return;
        }
      } catch (error) {
      }
      try {
        let response;
        let actionText;
        if (user.isCollected) {
          response = await utils_request.request.delete(`/miniapp/user/unfollow/${user.id}`);
          actionText = "取消关注";
        } else {
          response = await utils_request.request.post(`/miniapp/user/follow/${user.id}`);
          actionText = "关注";
        }
        if (response && response.data && response.data.code === 200) {
          user.isCollected = !user.isCollected;
          common_vendor.index.showToast({
            title: `${actionText}成功`,
            icon: "success"
          });
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || `${actionText}失败`;
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        let errorMessage = "网络错误，请重试";
        if (error.statusCode === 401) {
          errorMessage = "请先登录";
        } else if (error.statusCode === 404) {
          errorMessage = "用户不存在";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none"
        });
      }
    },
    // 导航到首页
    navigateToHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    // 导航到需求广场
    navigateToDemandSquare() {
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    },
    // 导航到产业资源
    navigateToIndustry() {
      common_vendor.index.reLaunch({
        url: "/pages/industry/index"
      });
    },
    // 导航到我的
    navigateToMine() {
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 跳转到老人脉资源页面
    goToOldContacts() {
      common_vendor.index.navigateTo({
        url: "/pages/index/contacts"
      });
    },
    // 跳转到聊天页面
    goToChat() {
      common_vendor.index.navigateTo({
        url: "/pages/index/chat"
      });
    },
    // 加载活跃用户排行榜
    async loadActiveUsersRanking() {
      try {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1237", "📊 开始获取活跃用户排行榜...");
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1238", "📊 API地址: GET /miniapp/ranking/app/activeUsers");
        const response = await utils_request.request.get("/miniapp/ranking/app/activeUsers");
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1241", "📊 活跃用户排行榜响应:", response);
        if (response && response.data && response.data.code === 200) {
          let rankingData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1245", "📊 活跃用户原始数据:", rankingData);
          this.activeUsers = rankingData.slice(0, 3).map((user) => ({
            userId: user.userId || user.id,
            name: user.realName || user.nickName || user.name || "未知用户",
            avatar: user.portraitUrl || user.avatarUrl || "",
            points: user.totalPoints || user.points || user.followers || 0
          }));
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1255", "📊 活跃用户排行榜加载成功，共", this.activeUsers.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1256", "📊 处理后的活跃用户数据:", this.activeUsers);
        } else {
          common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1258", "📊 获取活跃用户排行榜失败:", response);
          this.activeUsers = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1262", "📊 加载活跃用户排行榜异常:", error);
        this.activeUsers = [];
      }
    },
    // 加载热度用户排行榜
    async loadPopularUsersRanking() {
      try {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1270", "🔥 开始获取热度用户排行榜...");
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1272", "🔥 API地址: GET /miniapp/ranking/app/popularUsers (临时使用相同接口)");
        const response = await utils_request.request.get("/miniapp/ranking/app/popularUsers");
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1275", "🔥 热度用户排行榜响应:", response);
        if (response && response.data && response.data.code === 200) {
          let rankingData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1279", "🔥 热度用户原始数据:", rankingData);
          this.popularUsers = rankingData.slice(0, 3).map((user) => ({
            userId: user.userId || user.id,
            name: user.realName || user.nickName || user.name || "未知用户",
            avatar: user.portraitUrl || user.avatarUrl || "",
            followers: user.followersCount || user.followers || user.popularity || 0
          }));
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1291", "🔥 热度用户排行榜加载成功，共", this.popularUsers.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1292", "🔥 处理后的热度用户数据:", this.popularUsers);
        } else {
          common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1294", "🔥 获取热度用户排行榜失败:", response);
          this.popularUsers = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1298", "🔥 加载热度用户排行榜异常:", error);
        this.popularUsers = [];
      }
    },
    // 显示排行榜详情弹窗
    async showRankingDetail(type) {
      common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1305", "🏆 显示排行榜详情:", type);
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      this.rankingModalType = type;
      this.rankingModalTitle = type === "active" ? "活跃用户 (周榜)" : "热度用户 (周榜)";
      if (type === "active") {
        this.loadFullActiveUsersRanking();
      } else {
        this.loadFullPopularUsersRanking();
      }
      this.showRankingModal = true;
    },
    // 关闭排行榜弹窗
    closeRankingModal() {
      this.showRankingModal = false;
      this.fullRankingList = [];
    },
    // 加载完整的活跃用户排行榜
    async loadFullActiveUsersRanking() {
      try {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1335", "🏆 加载完整活跃用户排行榜...");
        const response = await utils_request.request.get("/miniapp/ranking/app/activeUsers");
        if (response && response.data && response.data.code === 200) {
          let rankingData = response.data.data || [];
          this.fullRankingList = rankingData.slice(0, 10).map((user) => ({
            name: user.realName || user.nickName || user.name || "未知用户",
            points: user.totalPoints || user.points || user.followers || 0,
            avatar: user.portraitUrl || user.avatarUrl || ""
          }));
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1348", "🏆 完整活跃用户排行榜加载成功:", this.fullRankingList);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1351", "🏆 加载完整活跃用户排行榜失败:", error);
      }
    },
    // 加载完整的热度用户排行榜
    async loadFullPopularUsersRanking() {
      try {
        common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1358", "🏆 加载完整热度用户排行榜...");
        const response = await utils_request.request.get("/miniapp/ranking/app/popularUsers");
        if (response && response.data && response.data.code === 200) {
          let rankingData = response.data.data || [];
          this.fullRankingList = rankingData.slice(0, 10).map((user) => ({
            name: user.realName || user.nickName || user.name || "未知用户",
            followers: user.followersCount || user.followers || user.popularity || 0,
            avatar: user.portraitUrl || user.avatarUrl || ""
          }));
          common_vendor.index.__f__("log", "at pages/index/contacts-new.vue:1371", "🏆 完整热度用户排行榜加载成功:", this.fullRankingList);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts-new.vue:1374", "🏆 加载完整热度用户排行榜失败:", error);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.searchKeyword,
    b: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    c: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    d: $options.getImagePath("filter.png"),
    e: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args)),
    f: $options.getImagePath("rank_list2.png"),
    g: common_vendor.o(($event) => $options.showRankingDetail("active")),
    h: $data.activeUsers && $data.activeUsers.length > 0
  }, $data.activeUsers && $data.activeUsers.length > 0 ? {
    i: common_vendor.f($data.activeUsers.slice(0, 3), (user, index, i0) => {
      return {
        a: $options.processServerImageUrl(user.avatar, $options.getImagePath("avatar.png")),
        b: common_vendor.t(user.name),
        c: index,
        d: common_vendor.o(($event) => $options.goToUserProfile(user), index)
      };
    })
  } : {}, {
    j: $options.getImagePath("rank_list1.png"),
    k: common_vendor.o(($event) => $options.showRankingDetail("popular")),
    l: $data.popularUsers && $data.popularUsers.length > 0
  }, $data.popularUsers && $data.popularUsers.length > 0 ? {
    m: common_vendor.f($data.popularUsers.slice(0, 3), (user, index, i0) => {
      return {
        a: $options.processServerImageUrl(user.avatar, $options.getImagePath("avatar.png")),
        b: common_vendor.t(user.name),
        c: index,
        d: common_vendor.o(($event) => $options.goToUserProfile(user), index)
      };
    })
  } : {}, {
    n: $data.userCards && $data.userCards.length > 0
  }, $data.userCards && $data.userCards.length > 0 ? {
    o: common_vendor.f($options.visibleCards, (user, index, i0) => {
      return {
        a: user.avatar,
        b: common_vendor.t(user.name),
        c: common_vendor.t(user.location),
        d: common_vendor.t(user.points),
        e: common_vendor.t(user.education),
        f: common_vendor.t(user.company),
        g: common_vendor.t(user.position),
        h: common_vendor.f(user.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        i: $options.getImagePath(user.isCollected ? "heart1.png" : "heart.png"),
        j: common_vendor.t(user.isCollected ? "已关注" : "关注名片"),
        k: common_vendor.o(($event) => $options.collectCard(user), user.id),
        l: user.id,
        m: common_vendor.s($options.getCardStyle(index)),
        n: common_vendor.o(($event) => $options.handleCardTap(user), user.id)
      };
    }),
    p: $options.getImagePath("score_icon.png"),
    q: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    r: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    s: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  } : {
    t: $options.getImagePath("icon8.png")
  }, {
    v: $data.showFilter
  }, $data.showFilter ? {
    w: common_vendor.o((...args) => $options.hideFilterModal && $options.hideFilterModal(...args))
  } : {}, {
    x: $data.showFilter
  }, $data.showFilter ? common_vendor.e({
    y: $data.searchKeyword
  }, $data.searchKeyword ? {
    z: common_vendor.t($data.searchKeyword)
  } : {}, {
    A: common_vendor.f($data.filterCategories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: index,
        c: $data.activeCategory === index ? 1 : "",
        d: common_vendor.o(($event) => $options.scrollToSection(index), index)
      };
    }),
    B: common_vendor.f($data.industryOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedIndustry === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectIndustry(option.value), index)
      };
    }),
    C: common_vendor.f($data.regionOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedRegion === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectRegion(option.value), index)
      };
    }),
    D: common_vendor.f($data.gradeOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.selectedGrade === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectGrade(option.value), index)
      };
    }),
    E: $data.scrollTop,
    F: $data.scrollIntoView,
    G: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args))
  }) : {}, {
    H: $options.getImagePath("bottom_order_icon1.png"),
    I: common_vendor.o((...args) => $options.navigateToHome && $options.navigateToHome(...args)),
    J: $options.getImagePath("bottom_order_icon2_active.png"),
    K: $options.getImagePath("bottom_order_icon.png"),
    L: common_vendor.o((...args) => $options.navigateToDemandSquare && $options.navigateToDemandSquare(...args)),
    M: $options.getImagePath("bottom_order_icon3.png"),
    N: common_vendor.o((...args) => $options.navigateToIndustry && $options.navigateToIndustry(...args)),
    O: $options.getImagePath("bottom_order_icon4.png"),
    P: common_vendor.o((...args) => $options.navigateToMine && $options.navigateToMine(...args)),
    Q: $data.showRankingModal
  }, $data.showRankingModal ? common_vendor.e({
    R: $options.getImagePath("cha.png"),
    S: common_vendor.o((...args) => $options.closeRankingModal && $options.closeRankingModal(...args)),
    T: $options.getImagePath($data.rankingModalType === "active" ? "rank_list2.png" : "rank_list1.png"),
    U: common_vendor.t($data.rankingModalTitle),
    V: $options.getImagePath("login_bg.png"),
    W: $data.fullRankingList[1]
  }, $data.fullRankingList[1] ? {
    X: $options.getImagePath("top_bg.png"),
    Y: $data.fullRankingList[1].avatar || $options.getImagePath("avatar.png"),
    Z: common_vendor.t($data.fullRankingList[1].name),
    aa: common_vendor.t($data.rankingModalType === "active" ? $data.fullRankingList[1].points : $data.fullRankingList[1].followers),
    ab: common_vendor.n($data.rankingModalType === "active" ? "modal-score2" : "modal-popular-score2"),
    ac: common_vendor.t($data.rankingModalType === "active" ? "积分" : "关注")
  } : {}, {
    ad: $data.fullRankingList[0]
  }, $data.fullRankingList[0] ? {
    ae: $options.getImagePath("top_bg.png"),
    af: $data.fullRankingList[0].avatar || $options.getImagePath("avatar.png"),
    ag: common_vendor.t($data.fullRankingList[0].name),
    ah: common_vendor.t($data.rankingModalType === "active" ? $data.fullRankingList[0].points : $data.fullRankingList[0].followers),
    ai: common_vendor.n($data.rankingModalType === "active" ? "modal-score1" : "modal-popular-score1"),
    aj: common_vendor.t($data.rankingModalType === "active" ? "积分" : "关注")
  } : {}, {
    ak: $data.fullRankingList[2]
  }, $data.fullRankingList[2] ? {
    al: $options.getImagePath("top_bg.png"),
    am: $data.fullRankingList[2].avatar || $options.getImagePath("avatar.png"),
    an: common_vendor.t($data.fullRankingList[2].name),
    ao: common_vendor.t($data.rankingModalType === "active" ? $data.fullRankingList[2].points : $data.fullRankingList[2].followers),
    ap: common_vendor.n($data.rankingModalType === "active" ? "modal-score3" : "modal-popular-score3"),
    aq: common_vendor.t($data.rankingModalType === "active" ? "积分" : "关注")
  } : {}, {
    ar: common_vendor.f($data.fullRankingList.slice(3, 10), (user, index, i0) => {
      return {
        a: common_vendor.t(String(index + 4).padStart(2, "0")),
        b: user.avatar || $options.getImagePath("avatar.png"),
        c: common_vendor.t(user.name),
        d: common_vendor.t($data.rankingModalType === "active" ? user.points : user.followers),
        e: index,
        f: common_vendor.n(index % 2 === 0 ? "bg-white" : "bg-light-blue")
      };
    }),
    as: common_vendor.n($data.rankingModalType === "active" ? "modal-active-score" : "modal-popular-score"),
    at: common_vendor.t($data.rankingModalType === "active" ? "积分" : "关注"),
    av: common_vendor.o(() => {
    }),
    aw: common_vendor.o((...args) => $options.closeRankingModal && $options.closeRankingModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c3d5c24b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/contacts-new.js.map
