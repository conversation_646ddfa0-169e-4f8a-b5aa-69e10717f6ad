.intro-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.header-section image {
  width: 100%;
}
.header-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}
.main-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}
.sub-title {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}
.content-section {
  padding: 0rpx 30rpx;
  margin-top: -60rpx;
  position: relative;
  z-index: 10;
}
.intro-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 51, 153, 0.1);
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #e8e8e8;
}
.header-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  margin-left: 5rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #003399;
}
.intro-content {
  line-height: 1.8;
}
.intro-text {
  font-size: 28rpx;
  color: #333;
  text-align: justify;
}
/* 富文本内容样式 */
.rich-text-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  word-wrap: break-word;
  word-break: break-all;
}
/* 富文本内部图片样式限制 */
.rich-text-content img {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  box-sizing: border-box !important;
}
/* 针对微信小程序的特殊处理 */
.intro-content {
  overflow: hidden;
  max-width: 100%;
}
/* 全局图片样式 - 确保所有图片都不超出 */
.intro-content image {
  max-width: 100% !important;
  height: auto !important;
}
/* 加载状态样式 */
.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.function-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  gap: 20rpx;
}
.function-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.function-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx #b8ccef;
  margin-bottom: 15rpx;
  overflow: hidden;
}
.btn-icon {
  width: 100%;
  height: 100%;
}
.btn-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}
.sponsor-section {
  margin-bottom: 40rpx;
  text-align: center;
}
.sponsor-banner {
  width: calc(100% - 40rpx);
  border-radius: 15rpx;
}
.contact-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 51, 153, 0.1);
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.contact-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}
.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
