.product-detail-container {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}
/* 顶部图片 */
.product-image-section {
  width: 100%;
  height: 400rpx;
  background-color: #fff;
}
.product-image {
  width: 100%;
  height: 100%;
}
/* 富文本内容 */
.rich-text-content {
  width: 100%;
  overflow: hidden;
  max-width: 100%;
}
/* 加载和空状态样式 */
.loading-content,
.no-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text,
.no-content-text {
  font-size: 28rpx;
  color: #999;
}
/* 底部按钮 - 参考需求分类详情页面 */
.bottom-buttons {
  display: flex;
  height: 120rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}
.bottom-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-btn image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}
.consult-btn {
  background: #98bae0;
}
.exchange-btn {
  background: #de4e4e;
}
.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}
