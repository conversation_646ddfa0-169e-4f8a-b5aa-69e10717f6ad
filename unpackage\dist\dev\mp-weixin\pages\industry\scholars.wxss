.scholars-container.data-v-10fbadec {
  min-height: 100vh;
  background-color: #f5f6fa;
}
/* 顶部搜索区域样式 - 完全参考活动报名页面 */
.header-section.data-v-10fbadec {
  background: linear-gradient(270deg, #013fb0 0%, #002566 100%);
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  overflow: hidden;
}
.header-section image.data-v-10fbadec {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
}
.search-container.data-v-10fbadec {
  display: flex;
  align-items: center;
  gap: 20rpx;
  position: relative;
  z-index: 10;
}
.search-input.data-v-10fbadec {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  border: 1px solid #fff;
}
.search-input.data-v-10fbadec::-webkit-input-placeholder {
  color: #999;
}
.search-input.data-v-10fbadec::placeholder {
  color: #999;
}
.search-btn.data-v-10fbadec {
  width: 120rpx;
  height: 80rpx;
  background: #72a5ff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-text.data-v-10fbadec {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}
/* 列表区域样式 */
.scholars-list.data-v-10fbadec {
  padding: 40rpx 30rpx;
}
.loading-container.data-v-10fbadec {
  text-align: center;
  padding: 100rpx 0;
}
.loading-text.data-v-10fbadec {
  font-size: 28rpx;
  color: #999;
}
/* 专家卡片样式 */
.scholar-item.data-v-10fbadec {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.flex.data-v-10fbadec {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20rpx;
  gap: 20rpx;
}
.flex .scholar-name.data-v-10fbadec {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.flex .scholar-unit.data-v-10fbadec {
  font-size: 28rpx;
  color: #666;
}
.scholar-tags.data-v-10fbadec {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.tag-item.data-v-10fbadec {
  background-color: #cbd5f1;
  color: #000;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.scholar-description.data-v-10fbadec {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}
/* 空状态样式 */
.empty-container.data-v-10fbadec {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-icon.data-v-10fbadec {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.empty-text.data-v-10fbadec {
  font-size: 32rpx;
  color: #999;
  display: block;
  margin-bottom: 20rpx;
}
.empty-desc.data-v-10fbadec {
  font-size: 26rpx;
  color: #ccc;
  display: block;
}
/* 右侧悬浮按钮 */
.floating-buttons.data-v-10fbadec {
  position: fixed;
  right: 0rpx;
  top: 36%;
  transform: translateY(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx #c1c5d4;
}
.floating-btn.data-v-10fbadec {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.floating-btn.question-btn.data-v-10fbadec {
  background: #023caa;
  color: white;
}
.floating-btn.demand-btn.data-v-10fbadec {
  background: #fad676;
  color: #333;
}
.floating-btn-text.data-v-10fbadec {
  font-size: 26rpx;
}
/* 联系人弹窗样式 - 与需求广场完全一致 */
.contact-popup-mask.data-v-10fbadec {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content.data-v-10fbadec {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn.data-v-10fbadec {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon.data-v-10fbadec {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title.data-v-10fbadec {
  margin-bottom: 40rpx;
}
.title-line.data-v-10fbadec {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone.data-v-10fbadec {
  margin-bottom: 40rpx;
}
.phone-number.data-v-10fbadec {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container.data-v-10fbadec {
  display: flex;
  justify-content: center;
}
.qr-code-image.data-v-10fbadec {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text.data-v-10fbadec {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
