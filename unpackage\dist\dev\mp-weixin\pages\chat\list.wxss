.chat-list-container {
  background-color: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;
  background: linear-gradient(180deg, #f8f8f8, #fff);
}
.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.flex {
  display: flex;
  align-items: center;
  gap: 35rpx;
}
.flex .search-icon {
  width: 42rpx;
  height: 42rpx;
  cursor: pointer;
}
/* 搜索容器样式 */
.search-container {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}
.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}
.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
}
.search-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-left: 20rpx;
}
.clear-btn,
.cancel-btn {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
  cursor: pointer;
}
.clear-btn {
  color: #999;
}
.cancel-btn {
  color: #007AFF;
}
/* 系统消息按钮容器 */
.system-message-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.system-message-container .message-icon {
  width: 46rpx;
  height: 46rpx;
}
/* 系统消息红点徽章 */
.system-message-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF4757;
  border-radius: 20rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  border: 2rpx solid #ffffff;
}
.system-message-count {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
}
.chat-list {
  flex: 1;
  background-color: #ffffff;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.chat-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #ffffff;
  transition: background-color 0.2s;
}
.chat-item:active {
  background-color: #f8f8f8;
}
.avatar-container {
  position: relative;
  margin-right: 24rpx;
}
.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: #f0f0f0;
}
.online-dot {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #4CAF50;
  border-radius: 10rpx;
  border: 3rpx solid #ffffff;
}
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}
.name-time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.time {
  font-size: 24rpx;
  color: #999999;
  margin-left: 16rpx;
  flex-shrink: 0;
}
.message-badge-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.last-message {
  display: block;
  font-size: 28rpx;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  width: 85%;
}
.unread-badge {
  background-color: #FF4757;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  padding: 0 8rpx;
}
.unread-count {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
}
/* 适配不同屏幕 */
@media screen and (max-width: 750rpx) {
.chat-item {
    padding: 20rpx 24rpx;
}
.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
}
.user-name {
    font-size: 30rpx;
}
.last-message {
    font-size: 26rpx;
}
}
/* 长按菜单样式 */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.3);
}
.context-menu {
  position: absolute;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-width: 200rpx;
}
.menu-item {
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  transition: background-color 0.2s;
}
.menu-item:active {
  background-color: #f5f5f5;
}
.delete-item {
  color: #ff4757;
}
.menu-text {
  font-size: 32rpx;
  color: #333;
}
.delete-item .menu-text {
  color: #ff4757;
}
.menu-divider {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 0 16rpx;
}
