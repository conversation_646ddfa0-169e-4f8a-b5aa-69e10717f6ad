
.container.data-v-d761ddfb {
	min-height: 100vh;
	background: #ffffff;
}
.loading-container.data-v-d761ddfb {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}
.loading-text.data-v-d761ddfb {
	font-size: 28rpx;
	color: #999;
}
.empty-container.data-v-d761ddfb {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}
.empty-text.data-v-d761ddfb {
	font-size: 28rpx;
	color: #999;
}
.rich-text-container.data-v-d761ddfb {
	width: 100%;
	min-height: 100vh;
}
.rich-text-content.data-v-d761ddfb {
	width: 100%;
	line-height: 1.8;
	font-size: 28rpx;
	color: #333;
	word-wrap: break-word;
	word-break: break-all;
}

/* 富文本内部图片样式限制 */
.rich-text-content.data-v-d761ddfb img {
	max-width: 100% !important;
	width: auto !important;
	height: auto !important;
	display: block !important;
	box-sizing: border-box !important;
	margin: 10rpx 0 !important;
}

/* 针对微信小程序的特殊处理 */
.rich-text-container.data-v-d761ddfb {
	overflow: hidden;
	max-width: 100%;
}
