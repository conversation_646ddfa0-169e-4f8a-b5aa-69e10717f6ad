<view class="products-container"><view class="products-content"><view wx:if="{{a}}" class="products-grid"><view wx:for="{{b}}" wx:for-item="product" wx:key="g" class="product-item" bindtap="{{product.h}}"><view class="product-image-wrapper"><image wx:if="{{product.a}}" src="{{product.b}}" class="product-image" mode="aspectFill" binderror="{{product.c}}"></image><view wx:else class="product-image-placeholder"><text class="placeholder-text">暂无图片</text></view></view><view class="product-info"><text class="product-name">{{product.d}}</text><text class="product-desc">{{product.e}}</text><view class="product-footer"><text class="product-price">{{product.f}}积分</text><view class="exchange-btn"><text class="btn-text">兑换</text></view></view></view></view></view><view wx:if="{{c}}" class="loading-container"><text class="loading-text">加载中...</text></view><view wx:if="{{d}}" class="empty-container"><text class="empty-text">该分类暂无商品</text></view></view></view>