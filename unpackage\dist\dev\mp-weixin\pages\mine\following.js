"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      currentTab: "following",
      // 当前选中的tab: 'following' | 'followers'
      followingList: [],
      // 我的关注列表
      followersList: [],
      // 关注我的列表
      loading: false,
      checkingStatus: false,
      // 是否正在检查关注状态
      needRefreshOnShow: false,
      // 是否需要在页面显示时刷新状态
      // 聊天解锁弹框
      showChatUnlockDialog: false,
      chatUnlockDialogImage: null,
      // 弹框背景图
      currentChatUser: null,
      // 当前要聊天的用户
      chatUnlockInfo: null
      // 聊天解锁信息
    };
  },
  computed: {
    // 当前显示的列表
    currentList() {
      return this.currentTab === "following" ? this.followingList : this.followersList;
    }
  },
  onLoad() {
    this.loadCurrentTabData();
    this.getChatUnlockDialogImage();
  },
  onShow() {
    if (this.currentTab === "followers" && this.followersList.length > 0) {
      common_vendor.index.__f__("log", "at pages/mine/following.vue:159", "👥 页面显示，重新检查粉丝关注状态");
      this.checkFollowersStatus();
    }
    this.needRefreshOnShow = false;
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 我的关注";
    },
    // 自定义分享内容
    getShareContent() {
      return "我的关注分享";
    },
    // 切换tab
    switchTab(tab) {
      if (this.currentTab === tab)
        return;
      this.currentTab = tab;
      this.loadCurrentTabData();
    },
    // 加载当前tab的数据
    loadCurrentTabData() {
      if (this.currentTab === "following") {
        this.loadMyFollowingList();
      } else {
        this.loadMyFollowersList();
      }
    },
    // 获取我的关注列表
    async loadMyFollowingList() {
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/mine/following.vue:203", "👥 开始获取我的关注列表...");
        const response = await utils_request.request.get("/miniapp/user/getMyFollowingList");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:206", "👥 我的关注列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const followingData = response.data.data.followingList || [];
          common_vendor.index.__f__("log", "at pages/mine/following.vue:210", "👥 原始关注数据:", followingData);
          this.followingList = followingData.map((item) => {
            const followedInfo = item.followedInfo || {};
            const industryTags = this.processIndustryNames(followedInfo.industryNames || "");
            return {
              id: item.followedId || followedInfo.userId || item.id,
              name: followedInfo.realName || followedInfo.userName || followedInfo.nickName || "暂无姓名",
              avatar: utils_imageUtils.processServerImageUrl(followedInfo.portraitUrl, utils_imageUtils.getImagePath("avatar.png")),
              grade: followedInfo.graduateSchool || "暂无院校",
              region: followedInfo.region || "暂无地区",
              industry: followedInfo.major || "暂无专业",
              company: followedInfo.currentCompany || "暂无公司",
              position: this.extractPositionName(followedInfo.positionTitle || ""),
              industryTags,
              rawData: {
                // 构造与人脉资源页面相同的数据结构
                userId: item.followedId || followedInfo.userId,
                id: item.followedId || followedInfo.userId,
                realName: followedInfo.realName || followedInfo.userName,
                nickName: followedInfo.nickName,
                portraitUrl: utils_imageUtils.processServerImageUrl(followedInfo.portraitUrl),
                region: followedInfo.region,
                graduateSchool: followedInfo.graduateSchool,
                major: followedInfo.major,
                currentCompany: followedInfo.currentCompany,
                positionTitle: followedInfo.positionTitle,
                college: followedInfo.college,
                totalPoints: followedInfo.totalPoints,
                industryNames: followedInfo.industryNames,
                personalIntroduction: followedInfo.personalIntroduction,
                isFollowed: true
                // 从我的关注进入，默认已关注
              }
            };
          });
          common_vendor.index.__f__("log", "at pages/mine/following.vue:251", "👥 我的关注列表获取成功，共", this.followingList.length, "条数据");
        } else {
          common_vendor.index.__f__("log", "at pages/mine/following.vue:253", "👥 ❌ 我的关注数据获取失败！");
          this.followingList = [];
          if (response && response.data && response.data.msg) {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:264", "👥 获取我的关注列表失败:", error);
        this.followingList = [];
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 获取我的粉丝列表
    async loadMyFollowersList() {
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/mine/following.vue:279", "👥 开始获取我的粉丝列表...");
        const response = await utils_request.request.get("/miniapp/user/getMyFollowersList");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:282", "👥 我的粉丝列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const followersData = response.data.data.followersList || [];
          common_vendor.index.__f__("log", "at pages/mine/following.vue:286", "👥 原始粉丝数据:", followersData);
          this.followersList = followersData.map((item) => {
            const followerInfo = item.followerInfo || {};
            const industryTags = this.processIndustryNames(followerInfo.industryNames || "");
            return {
              id: item.followerId || followerInfo.userId || item.id,
              name: followerInfo.realName || followerInfo.userName || followerInfo.nickName || "暂无姓名",
              avatar: utils_imageUtils.processServerImageUrl(followerInfo.portraitUrl, utils_imageUtils.getImagePath("avatar.png")),
              grade: followerInfo.graduateSchool || "暂无院校",
              region: followerInfo.region || "暂无地区",
              industry: followerInfo.major || "暂无专业",
              company: followerInfo.currentCompany || "暂无公司",
              position: this.extractPositionName(followerInfo.positionTitle || ""),
              industryTags,
              isFollowedBack: false,
              // 是否已经回关，需要检查
              rawData: {
                // 构造与人脉资源页面相同的数据结构
                userId: item.followerId || followerInfo.userId,
                id: item.followerId || followerInfo.userId,
                realName: followerInfo.realName || followerInfo.userName,
                nickName: followerInfo.nickName,
                portraitUrl: utils_imageUtils.processServerImageUrl(followerInfo.portraitUrl),
                region: followerInfo.region,
                graduateSchool: followerInfo.graduateSchool,
                major: followerInfo.major,
                currentCompany: followerInfo.currentCompany,
                positionTitle: followerInfo.positionTitle,
                college: followerInfo.college,
                totalPoints: followerInfo.totalPoints,
                industryNames: followerInfo.industryNames,
                personalIntroduction: followerInfo.personalIntroduction,
                isFollowed: false
                // 需要检查是否已关注
              }
            };
          });
          await this.checkFollowersStatus();
          common_vendor.index.__f__("log", "at pages/mine/following.vue:331", "👥 我的粉丝列表获取成功，共", this.followersList.length, "条数据");
        } else {
          common_vendor.index.__f__("log", "at pages/mine/following.vue:333", "👥 ❌ 我的粉丝数据获取失败！");
          this.followersList = [];
          if (response && response.data && response.data.msg) {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:344", "👥 获取我的粉丝列表失败:", error);
        this.followersList = [];
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理行业名称字符串，转换为标签数组
    processIndustryNames(industryNames) {
      if (!industryNames || typeof industryNames !== "string") {
        return [];
      }
      return industryNames.split(",").map((name) => name.trim()).filter((name) => name.length > 0).slice(0, 3);
    },
    // 提取职位名称（去掉公司名称部分）
    extractPositionName(positionTitle) {
      if (!positionTitle)
        return "暂无职位";
      const parts = positionTitle.split(/[·•\-\s]+/);
      return parts.length > 1 ? parts[parts.length - 1].trim() : positionTitle;
    },
    // 检查粉丝的关注状态
    async checkFollowersStatus() {
      if (this.followersList.length === 0)
        return;
      common_vendor.index.__f__("log", "at pages/mine/following.vue:381", "👥 开始检查粉丝关注状态...");
      if (this.checkingStatus) {
        common_vendor.index.__f__("log", "at pages/mine/following.vue:385", "👥 正在检查中，跳过重复检查");
        return;
      }
      this.checkingStatus = true;
      try {
        const checkPromises = this.followersList.map(async (user) => {
          try {
            const userId = user.id;
            if (!userId)
              return;
            const response = await utils_request.request.get(`/miniapp/user/checkFollowStatus/${userId}`);
            if (response && response.data && response.data.code === 200) {
              const isFollowing = response.data.data.isFollowing || false;
              user.isFollowedBack = isFollowing;
              user.rawData.isFollowed = isFollowing;
              common_vendor.index.__f__("log", "at pages/mine/following.vue:403", `👥 用户 ${user.name} 关注状态: ${isFollowing ? "已关注" : "未关注"}`);
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/mine/following.vue:406", "👥 检查用户关注状态失败:", error);
            user.isFollowedBack = false;
          }
        });
        await Promise.all(checkPromises);
        common_vendor.index.__f__("log", "at pages/mine/following.vue:412", "👥 粉丝关注状态检查完成");
      } finally {
        this.checkingStatus = false;
      }
    },
    // 回关操作
    async followBack(user) {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/mine/following.vue:421", "👥 回关用户:", user);
        if (!user.id) {
          common_vendor.index.showToast({
            title: "无法获取用户信息",
            icon: "none"
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/mine/following.vue:433", "📤 调用关注接口，用户ID:", user.id);
        const response = await utils_request.request.post(`/miniapp/user/follow/${user.id}`);
        common_vendor.index.__f__("log", "at pages/mine/following.vue:435", "📥 关注接口响应:", response);
        if (response && response.data && response.data.code === 200) {
          user.isFollowedBack = true;
          user.rawData.isFollowed = true;
          common_vendor.index.__f__("log", "at pages/mine/following.vue:443", "✅ 回关成功");
          common_vendor.index.showToast({
            title: "回关成功",
            icon: "success"
          });
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "回关失败，请重试";
          common_vendor.index.__f__("log", "at pages/mine/following.vue:451", "❌ 回关失败:", errorMsg);
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:459", "👥 回关失败:", error);
        let errorMessage = "网络错误，请重试";
        if (error.statusCode === 401) {
          errorMessage = "请先登录";
        } else if (error.statusCode === 403) {
          errorMessage = "没有权限执行此操作";
        } else if (error.statusCode === 404) {
          errorMessage = "用户不存在";
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none"
        });
      }
    },
    // 开始聊天
    async startChat(user) {
      common_vendor.index.__f__("log", "at pages/mine/following.vue:480", "👥 开始聊天:", user);
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "error"
          });
          return;
        }
        const currentUserInfo = common_vendor.index.getStorageSync("userInfo");
        if (!currentUserInfo || !currentUserInfo.userId) {
          common_vendor.index.showToast({
            title: "获取用户信息失败",
            icon: "error"
          });
          return;
        }
        if (currentUserInfo.userId === user.id) {
          common_vendor.index.showToast({
            title: "不能与自己聊天",
            icon: "none"
          });
          return;
        }
        this.currentChatUser = user;
        common_vendor.index.__f__("log", "at pages/mine/following.vue:516", "💬 检查聊天权限，对方用户ID:", user.id);
        const permissionResult = await this.checkChatPermission(user.id);
        if (!permissionResult) {
          return;
        }
        this.navigateToChatPage(user);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:527", "👥 开始聊天失败:", error);
        common_vendor.index.showToast({
          title: "启动聊天失败",
          icon: "error"
        });
      }
    },
    // 检查聊天权限
    async checkChatPermission(otherUserId) {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/mine/following.vue:538", "💬 开始检查聊天权限...");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:539", "💬 API地址: POST /miniapp/chat/app/checkPermission");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:540", "💬 对方用户ID:", otherUserId);
        common_vendor.index.showLoading({
          title: "检查权限中..."
        });
        const response = await utils_request.request.post(`/miniapp/chat/app/checkPermission?otherUserId=${otherUserId}`);
        common_vendor.index.__f__("log", "at pages/mine/following.vue:549", "💬 聊天权限检查完整响应:", JSON.stringify(response, null, 2));
        common_vendor.index.hideLoading();
        if (response && response.success && response.data && response.data.code === 200) {
          const chatData = response.data.data;
          common_vendor.index.__f__("log", "at pages/mine/following.vue:555", "💬 聊天权限检查成功，数据:", chatData);
          if (chatData.canChat) {
            common_vendor.index.__f__("log", "at pages/mine/following.vue:559", "💬 可以直接聊天");
            return true;
          } else if (chatData.needUnlock) {
            common_vendor.index.__f__("log", "at pages/mine/following.vue:563", "💬 需要解锁聊天功能");
            this.chatUnlockInfo = chatData;
            this.showChatUnlockDialog = true;
            return false;
          } else {
            common_vendor.index.__f__("log", "at pages/mine/following.vue:569", "💬 无法聊天:", chatData.message);
            common_vendor.index.showToast({
              title: chatData.message || "暂无聊天权限",
              icon: "none"
            });
            return false;
          }
        } else {
          common_vendor.index.__f__("log", "at pages/mine/following.vue:577", "💬 聊天权限检查失败:", response);
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "权限检查失败",
            icon: "none"
          });
          return false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:585", "💬 检查聊天权限异常:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "权限检查失败，请重试",
          icon: "none"
        });
        return false;
      }
    },
    // 获取聊天解锁弹框背景图
    async getChatUnlockDialogImage() {
      try {
        common_vendor.index.__f__("log", "at pages/mine/following.vue:598", "🖼️ 开始获取聊天解锁弹框背景图...");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:599", "🖼️ API地址: POST /miniapp/topimage/app/getEnabledListByPage");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:600", "🖼️ 请求参数: pageCode = ChatUnlockDialog");
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "ChatUnlockDialog");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:603", "🖼️ 聊天解锁弹框背景图完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const imageData = response.data.data;
          if (imageData && imageData.length > 0) {
            this.chatUnlockDialogImage = utils_imageUtils.processServerImageUrl(imageData[0].imageUrl);
            common_vendor.index.__f__("log", "at pages/mine/following.vue:610", "🖼️ 聊天解锁弹框背景图获取成功:", this.chatUnlockDialogImage);
          } else {
            common_vendor.index.__f__("log", "at pages/mine/following.vue:612", "🖼️ 聊天解锁弹框背景图数据为空");
          }
        } else {
          common_vendor.index.__f__("error", "at pages/mine/following.vue:615", "🖼️ 聊天解锁弹框背景图获取失败:", response);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:618", "🖼️ 获取聊天解锁弹框背景图异常:", error);
      }
    },
    // 关闭聊天解锁弹框
    closeChatUnlockDialog() {
      this.showChatUnlockDialog = false;
      this.currentChatUser = null;
      this.chatUnlockInfo = null;
    },
    // 解锁聊天功能
    async unlockChatFunction() {
      var _a;
      try {
        if (!this.currentChatUser || !this.currentChatUser.id) {
          common_vendor.index.showToast({
            title: "用户信息不存在",
            icon: "none"
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/mine/following.vue:640", "💬 开始解锁聊天功能...");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:641", "💬 API地址: POST /miniapp/chat/app/unlockChat");
        common_vendor.index.__f__("log", "at pages/mine/following.vue:642", "💬 对方用户ID:", this.currentChatUser.id);
        common_vendor.index.showLoading({
          title: "解锁中..."
        });
        const response = await utils_request.request.post(`/miniapp/chat/app/unlockChat?otherUserId=${this.currentChatUser.id}`);
        common_vendor.index.__f__("log", "at pages/mine/following.vue:650", "💬 聊天解锁完整响应:", JSON.stringify(response, null, 2));
        common_vendor.index.hideLoading();
        if (response && response.success && response.data && response.data.code === 200) {
          common_vendor.index.__f__("log", "at pages/mine/following.vue:655", "💬 聊天解锁成功");
          common_vendor.index.showToast({
            title: "解锁成功",
            icon: "success"
          });
          this.navigateToChatPage(this.currentChatUser);
          this.closeChatUnlockDialog();
        } else {
          common_vendor.index.__f__("log", "at pages/mine/following.vue:669", "💬 聊天解锁失败:", response);
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "解锁失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/following.vue:676", "💬 解锁聊天功能异常:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "解锁失败，请重试",
          icon: "none"
        });
      }
    },
    // 跳转到聊天页面的通用方法
    navigateToChatPage(user) {
      const chatParams = {
        userId: user.id,
        userName: user.name,
        userAvatar: user.avatar,
        // 传递完整的用户数据，以便聊天页面使用
        userData: encodeURIComponent(JSON.stringify({
          userId: user.id,
          realName: user.name,
          portraitUrl: user.avatar,
          ...user.rawData
        }))
      };
      const queryString = Object.keys(chatParams).map((key) => `${key}=${chatParams[key]}`).join("&");
      common_vendor.index.__f__("log", "at pages/mine/following.vue:704", "👥 跳转到聊天页面，参数:", chatParams);
      common_vendor.index.navigateTo({
        url: `/pages/chat/index?${queryString}`
      });
    },
    // 查看联系人详情
    viewContactDetail(contact) {
      common_vendor.index.__f__("log", "at pages/mine/following.vue:713", "👥 查看用户详情:", contact);
      const userData = contact.rawData;
      common_vendor.index.navigateTo({
        url: `/pages/index/business-card-detail?userData=${encodeURIComponent(JSON.stringify(userData))}`
      });
      this.needRefreshOnShow = true;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentTab === "following" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchTab("following")),
    c: $data.currentTab === "followers" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchTab("followers")),
    e: $data.loading
  }, $data.loading ? {} : $options.currentList.length === 0 ? {
    g: common_vendor.t($data.currentTab === "following" ? "暂无关注的用户" : "暂无粉丝")
  } : {
    h: common_vendor.f($options.currentList, (user, index, i0) => {
      return common_vendor.e({
        a: user.avatar || $options.getImagePath("avatar.png"),
        b: common_vendor.t(user.name),
        c: common_vendor.t(user.region),
        d: common_vendor.t(user.grade),
        e: common_vendor.t(user.industry),
        f: common_vendor.t(user.company),
        g: common_vendor.t(user.position),
        h: user.industryTags && user.industryTags.length > 0
      }, user.industryTags && user.industryTags.length > 0 ? {
        i: common_vendor.f(user.industryTags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, $data.currentTab === "followers" ? common_vendor.e({
        j: user.isFollowedBack
      }, user.isFollowedBack ? {
        k: common_vendor.o(($event) => $options.startChat(user), index)
      } : {
        l: common_vendor.o(($event) => $options.followBack(user), index)
      }) : {}, {
        m: common_vendor.o(($event) => $options.viewContactDetail(user), index),
        n: index,
        o: common_vendor.o(($event) => $options.viewContactDetail(user), index)
      });
    }),
    i: $data.currentTab === "followers"
  }, {
    f: $options.currentList.length === 0,
    j: $data.showChatUnlockDialog
  }, $data.showChatUnlockDialog ? common_vendor.e({
    k: $options.getImagePath("cha.png"),
    l: common_vendor.o((...args) => $options.closeChatUnlockDialog && $options.closeChatUnlockDialog(...args)),
    m: $data.chatUnlockDialogImage
  }, $data.chatUnlockDialogImage ? {
    n: $data.chatUnlockDialogImage
  } : {}, {
    o: common_vendor.o((...args) => $options.unlockChatFunction && $options.unlockChatFunction(...args)),
    p: common_vendor.o(() => {
    }),
    q: common_vendor.o((...args) => $options.closeChatUnlockDialog && $options.closeChatUnlockDialog(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/following.js.map
