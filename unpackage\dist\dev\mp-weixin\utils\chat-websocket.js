"use strict";
const common_vendor = require("../common/vendor.js");
const config_index = require("../config/index.js");
class ChatWebSocket {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.config = {
      serverUrl: config_index.config.wsURL,
      // 使用config中的WebSocket地址
      maxReconnectAttempts: 3,
      reconnectInterval: 3e3,
      heartbeatInterval: 3e4,
      // 30秒心跳
      messageTimeout: 1e4,
      // 10秒消息超时
      connectionTimeout: 15e3
      // 15秒连接超时
    };
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:26", "[ChatWebSocket] 初始化配置:", {
      serverUrl: this.config.serverUrl,
      baseURL: config_index.config.baseURL
    });
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    this.eventListeners = /* @__PURE__ */ new Map();
    this.messageQueue = [];
    this.currentToken = null;
    this.pendingMessages = /* @__PURE__ */ new Map();
    this.listenersSetup = false;
    this.connectionTimer = null;
    this.connectPromise = null;
    this.socketTask = null;
  }
  /**
   * 连接WebSocket
   */
  connect(token) {
    if (!token) {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:49", "[ChatWebSocket] Token不能为空");
      return Promise.reject(new Error("Token不能为空"));
    }
    if (this.isConnected) {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:55", "[ChatWebSocket] 已连接，直接返回");
      return Promise.resolve();
    }
    if (this.isConnecting && this.connectPromise) {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:61", "[ChatWebSocket] 正在连接中，返回当前Promise");
      return this.connectPromise;
    }
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:65", "[ChatWebSocket] 开始新的连接...");
    this.currentToken = token;
    this.isConnecting = true;
    this.connectPromise = new Promise((resolve, reject) => {
      const wsUrl = `${this.config.serverUrl}/websocket/chat/${token}`;
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:72", "[ChatWebSocket] 连接URL:", wsUrl);
      this.setupGlobalEventListeners();
      this.currentResolve = resolve;
      this.currentReject = reject;
      this.connectionTimer = setTimeout(() => {
        common_vendor.index.__f__("error", "at utils/chat-websocket.js:83", "[ChatWebSocket] 连接超时");
        this.handleConnectionFailure(new Error("WebSocket连接超时"));
      }, this.config.connectionTimeout);
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:88", "[ChatWebSocket] 创建SocketTask连接...");
      this.socketTask = common_vendor.index.connectSocket({
        url: wsUrl,
        success: () => {
          common_vendor.index.__f__("log", "at utils/chat-websocket.js:92", "[ChatWebSocket] SocketTask创建成功");
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at utils/chat-websocket.js:95", "[ChatWebSocket] SocketTask创建失败:", error);
          this.handleConnectionFailure(error);
        }
      });
      this.setupSocketTaskEvents();
    });
    return this.connectPromise;
  }
  // 处理连接失败
  handleConnectionFailure(error) {
    common_vendor.index.__f__("error", "at utils/chat-websocket.js:109", "[ChatWebSocket] 连接失败处理:", error);
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
    this.isConnected = false;
    this.isConnecting = false;
    this.connectPromise = null;
    this.emit("error", { type: "connection", error });
    if (this.currentReject) {
      this.currentReject(error);
      this.currentResolve = null;
      this.currentReject = null;
    }
  }
  /**
   * 设置全局WebSocket事件监听器（只设置一次）
   */
  setupGlobalEventListeners() {
    if (this.listenersSetup) {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:139", "[ChatWebSocket] 全局事件监听器已设置，跳过");
      return;
    }
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:143", "[ChatWebSocket] 设置全局WebSocket事件监听器...");
    this.listenersSetup = true;
    common_vendor.index.onSocketOpen((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:148", "[ChatWebSocket] ✅ 全局WebSocket连接成功!", res);
      this.handleSocketOpen(res);
    });
    common_vendor.index.onSocketMessage((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:153", "[ChatWebSocket] 📨 全局收到消息:", res);
      this.handleSocketMessage(res);
    });
    common_vendor.index.onSocketClose((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:158", "[ChatWebSocket] ❌ 全局连接关闭:", res);
      this.handleSocketClose(res);
    });
    common_vendor.index.onSocketError((res) => {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:163", "[ChatWebSocket] ❌ 全局连接错误:", res);
      this.handleSocketError(res);
    });
  }
  /**
   * 设置SocketTask事件监听器
   */
  setupSocketTaskEvents() {
    if (!this.socketTask) {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:173", "[ChatWebSocket] SocketTask不存在，无法设置事件监听");
      return;
    }
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:177", "[ChatWebSocket] 设置SocketTask事件监听器...");
    this.socketTask.onOpen((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:181", "[ChatWebSocket] ✅ SocketTask连接成功!", res);
      this.handleSocketOpen(res);
    });
    this.socketTask.onMessage((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:186", "[ChatWebSocket] 📨 SocketTask收到消息:", res);
      this.handleSocketMessage(res);
    });
    this.socketTask.onClose((res) => {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:191", "[ChatWebSocket] ❌ SocketTask连接关闭:", res);
      this.handleSocketClose(res);
    });
    this.socketTask.onError((res) => {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:196", "[ChatWebSocket] ❌ SocketTask连接错误:", res);
      this.handleSocketError(res);
    });
  }
  /**
   * 处理WebSocket连接成功
   */
  handleSocketOpen(res) {
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:205", "[ChatWebSocket] 处理连接成功事件");
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
    this.isConnected = true;
    this.isConnecting = false;
    this.connectPromise = null;
    this.reconnectAttempts = 0;
    this.startHeartbeat();
    this.processMessageQueue();
    this.emit("connected");
    if (this.currentResolve) {
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:228", "[ChatWebSocket] 调用连接成功回调");
      this.currentResolve();
      this.currentResolve = null;
      this.currentReject = null;
    }
  }
  /**
   * 处理WebSocket消息
   */
  handleSocketMessage(res) {
    try {
      const message = JSON.parse(res.data);
      common_vendor.index.__f__("log", "at utils/chat-websocket.js:241", "[ChatWebSocket] 处理收到的消息:", message);
      this.handleMessage(message);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:244", "[ChatWebSocket] 消息解析失败:", error);
    }
  }
  /**
   * 处理WebSocket连接关闭
   */
  handleSocketClose(res) {
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:252", "[ChatWebSocket] 处理连接关闭事件");
    this.handleDisconnect();
    if (this.currentToken && this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.attemptReconnect();
    }
  }
  /**
   * 处理WebSocket连接错误
   */
  handleSocketError(res) {
    common_vendor.index.__f__("error", "at utils/chat-websocket.js:265", "[ChatWebSocket] 处理连接错误事件:", res);
    this.handleConnectionFailure(res);
  }
  /**
   * 断开连接
   */
  disconnect() {
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:273", "[ChatWebSocket] 断开连接...");
    this.currentToken = null;
    this.connectPromise = null;
    this.stopHeartbeat();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
    if (this.socketTask) {
      this.socketTask.close();
      this.socketTask = null;
    }
    if (this.ws) {
      common_vendor.index.closeSocket();
      this.ws = null;
    }
    this.handleDisconnect();
  }
  /**
   * 处理断开连接
   */
  handleDisconnect() {
    this.isConnected = false;
    this.isConnecting = false;
    this.stopHeartbeat();
    this.emit("disconnected");
  }
  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendHeartbeat();
      }
    }, this.config.heartbeatInterval);
  }
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
  /**
   * 发送心跳
   */
  sendHeartbeat() {
    if (!this.isConnected)
      return;
    try {
      const heartbeat = {
        type: "heartbeat",
        timestamp: Date.now()
      };
      if (this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify(heartbeat),
          success: () => {
            common_vendor.index.__f__("log", "at utils/chat-websocket.js:356", "[ChatWebSocket] SocketTask心跳发送成功");
          },
          fail: (error) => {
            common_vendor.index.__f__("error", "at utils/chat-websocket.js:359", "[ChatWebSocket] SocketTask心跳发送失败:", error);
          }
        });
      } else {
        common_vendor.index.sendSocketMessage({
          data: JSON.stringify(heartbeat),
          success: () => {
            common_vendor.index.__f__("log", "at utils/chat-websocket.js:367", "[ChatWebSocket] 全局心跳发送成功");
          },
          fail: (error) => {
            common_vendor.index.__f__("error", "at utils/chat-websocket.js:370", "[ChatWebSocket] 全局心跳发送失败:", error);
          }
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/chat-websocket.js:375", "[ChatWebSocket] 心跳发送失败:", error);
    }
  }
  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (!this.currentToken || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.emit("reconnectFailed");
      return;
    }
    this.reconnectAttempts++;
    common_vendor.index.__f__("log", "at utils/chat-websocket.js:389", `[ChatWebSocket] 尝试重连 (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
    this.emit("reconnecting", { attempt: this.reconnectAttempts });
    this.reconnectTimer = setTimeout(() => {
      this.connect(this.currentToken).catch(() => {
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.attemptReconnect();
        } else {
          this.emit("reconnectFailed");
        }
      });
    }, this.config.reconnectInterval);
  }
  /**
   * 处理接收到的消息
   */
  handleMessage(message) {
    const { type } = message;
    switch (type) {
      case "system":
        this.emit("system", message);
        break;
      case "error":
        common_vendor.index.__f__("error", "at utils/chat-websocket.js:415", "[ChatWebSocket] 服务器错误:", message.error);
        this.emit("error", { type: "server", message: message.error });
        break;
      case "message":
        this.emit("message", message);
        break;
      case "sent_confirm":
        this.handleSentConfirm(message);
        this.emit("sent_confirm", message);
        break;
      case "heartbeat_response":
        break;
      default:
        common_vendor.index.__f__("warn", "at utils/chat-websocket.js:429", "[ChatWebSocket] 未知消息类型:", type);
    }
  }
  /**
   * 处理发送确认
   */
  handleSentConfirm(message) {
    if (message.tempId && this.pendingMessages.has(message.tempId)) {
      const { resolve } = this.pendingMessages.get(message.tempId);
      this.pendingMessages.delete(message.tempId);
      resolve(message);
    }
  }
  /**
   * 发送消息（对应后端ChatMessageRequest）
   */
  sendMessage(messageData) {
    const tempId = `temp_${Date.now()}_${Math.random()}`;
    const message = {
      receiverId: messageData.receiverId,
      messageType: messageData.messageType,
      content: messageData.content || null,
      mediaUrl: messageData.mediaUrl || null,
      mediaSize: messageData.mediaSize || null,
      mediaDuration: messageData.mediaDuration || null,
      tempId
      // 临时ID，用于消息确认
    };
    return new Promise((resolve, reject) => {
      this.pendingMessages.set(tempId, { resolve, reject });
      setTimeout(() => {
        if (this.pendingMessages.has(tempId)) {
          this.pendingMessages.delete(tempId);
          reject(new Error("消息发送超时"));
        }
      }, this.config.messageTimeout);
      this.sendRawMessage(message).catch(reject);
    });
  }
  /**
   * 发送文本消息
   */
  sendTextMessage(receiverId, content) {
    return this.sendMessage({
      receiverId,
      messageType: "text",
      content
    });
  }
  /**
   * 发送原始消息
   */
  sendRawMessage(message) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        this.messageQueue.push({ message, resolve, reject });
        return;
      }
      try {
        if (this.socketTask) {
          this.socketTask.send({
            data: JSON.stringify(message),
            success: resolve,
            fail: reject
          });
        } else {
          common_vendor.index.sendSocketMessage({
            data: JSON.stringify(message),
            success: resolve,
            fail: reject
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }
  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (!this.isConnected || this.messageQueue.length === 0) {
      return;
    }
    const queue = [...this.messageQueue];
    this.messageQueue = [];
    queue.forEach(({ message, resolve, reject }) => {
      this.sendRawMessage(message).then(resolve).catch(reject);
    });
  }
  /**
   * 事件监听
   */
  on(event, handler) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(handler);
  }
  /**
   * 移除事件监听
   */
  off(event, handler) {
    if (!this.eventListeners.has(event))
      return;
    const handlers = this.eventListeners.get(event);
    const index = handlers.indexOf(handler);
    if (index > -1) {
      handlers.splice(index, 1);
    }
  }
  /**
   * 触发事件
   */
  emit(event, data) {
    if (!this.eventListeners.has(event))
      return;
    this.eventListeners.get(event).forEach((handler) => {
      try {
        handler(data);
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/chat-websocket.js:567", `[ChatWebSocket] 事件处理失败 (${event}):`, error);
      }
    });
  }
  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queueSize: this.messageQueue.length,
      pendingMessages: this.pendingMessages.size
    };
  }
}
const chatWebSocket = new ChatWebSocket();
exports.chatWebSocket = chatWebSocket;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/chat-websocket.js.map
