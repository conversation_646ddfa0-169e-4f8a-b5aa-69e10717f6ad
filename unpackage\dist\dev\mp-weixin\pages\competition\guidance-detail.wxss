.guidance-detail-container {
  width: 100%;
  min-height: 100vh;
  background: #f5f7fa;
  padding-bottom: 120rpx;
}
.header-section {
  position: relative;
}
.header-section .header-image-container {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}
.header-section .header-image-container .header-image {
  width: 100%;
  height: 100%;
}
.detail-card,
.form-card {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
}
.detail-card .card-header,
.form-card .card-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.detail-card .card-header .card-icon,
.form-card .card-header .card-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.detail-card .card-header .card-title,
.form-card .card-header .card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2a5298;
}
.info-section .info-item {
  margin-bottom: 30rpx;
}
.info-section .info-item:last-child {
  margin-bottom: 0;
}
.info-section .info-item .info-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}
.info-section .info-item .info-value {
  display: block;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}
.form-item {
  margin-bottom: 40rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-item .form-label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.form-item .form-label-row .form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.form-item .form-label-row .form-label .required-star {
  color: #ff4757 !important;
  margin-right: 4rpx;
  font-weight: bold;
}
.form-item .form-label-row .form-required {
  font-size: 24rpx;
  color: #ff0000;
}
.form-item .input-container {
  position: relative;
}
.form-item .input-container .form-input,
.form-item .input-container .form-textarea {
  width: 100%;
  height: 88rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  line-height: 88rpx;
}
.form-item .input-container .form-input:focus,
.form-item .input-container .form-textarea:focus {
  border-color: #2a5298;
}
.form-item .input-container .form-textarea {
  height: 120rpx;
  padding: 20rpx 30rpx;
  line-height: 1.5;
  resize: none;
}
.form-item .input-container .picker-input {
  width: 100%;
  height: 88rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
.form-item .input-container .picker-input .picker-text {
  font-size: 28rpx;
  color: #333;
}
.form-item .input-container .picker-input .picker-text.placeholder {
  color: #999;
}
.form-item .input-container .picker-input .picker-arrow {
  font-size: 24rpx;
  color: #999;
}
.form-item .input-container .radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}
.form-item .input-container .radio-group .radio-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
}
.form-item .input-container .radio-group .radio-item radio {
  margin-right: 10rpx;
}
.form-item .input-container .radio-group .radio-item .radio-text {
  font-size: 28rpx;
  color: #333;
}
.form-item .input-container .checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}
.form-item .input-container .checkbox-group .checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
}
.form-item .input-container .checkbox-group .checkbox-item checkbox {
  margin-right: 10rpx;
}
.form-item .input-container .checkbox-group .checkbox-item .checkbox-text {
  font-size: 28rpx;
  color: #333;
}
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}
.submit-section .action-buttons {
  display: flex;
  gap: 20rpx;
  align-items: center;
}
.submit-section .share-btn {
  width: 160rpx;
  height: 88rpx;
  background: #ffffff;
  border: 2rpx solid #2a5298;
  border-radius: 44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}
.submit-section .share-btn .share-icon {
  font-size: 24rpx;
}
.submit-section .share-btn .share-text {
  color: #2a5298;
  font-size: 24rpx;
  font-weight: 500;
}
.submit-section .share-btn:active {
  background: #f8f9fa;
}
.submit-section .submit-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #2a5298 0%, #1e3a72 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(42, 82, 152, 0.3);
}
.submit-section .submit-btn.disabled {
  background: #ccc;
  box-shadow: none;
}
.submit-section .submit-btn .submit-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
