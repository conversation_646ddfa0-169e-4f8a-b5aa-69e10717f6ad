<view class="profile-container"><view class="header-section"><image src="{{a}}" mode="widthFix"></image><view class="header-content"><text class="header-title">完善个人资料</text><text class="header-subtitle">完善资料可获得 200 积分</text><view class="progress-container"><view class="progress-bar"><view class="progress-fill" style="{{'width:' + b}}"></view></view><text class="progress-text">完成度 <text style="color:#003898;font-weight:900">{{c}}%</text></text></view></view></view><scroll-view class="form-content" scroll-y><view class="info-card"><view class="card-header"><image src="{{d}}" mode="" class="card-icon"></image><text class="card-title">基础信息</text><view class="{{['points-badge', f && 'completed', g && 'earned']}}">{{e}}</view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{h}}" class="required-star">*</text>姓名</text></view><input class="{{['form-input', i && 'input-error']}}" placeholder="请输入真实姓名" bindinput="{{j}}" bindblur="{{k}}" value="{{l}}"/></view><view class="form-item"><view class="form-label-row"><text class="form-label">性别</text></view><view class="gender-radio-group"><view class="radio-item" bindtap="{{o}}"><view class="{{['radio-circle', n && 'checked']}}"><view wx:if="{{m}}" class="radio-dot"></view></view><text class="radio-text">男</text></view><view class="radio-item" bindtap="{{r}}"><view class="{{['radio-circle', q && 'checked']}}"><view wx:if="{{p}}" class="radio-dot"></view></view><text class="radio-text">女</text></view></view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{s}}" class="required-star">*</text>出生日期</text></view><picker mode="date" value="{{x}}" bindchange="{{y}}" class="date-picker"><view class="{{['picker-input', w && 'input-error']}}"><text wx:if="{{t}}" class="picker-text">{{v}}</text><text wx:else class="picker-text" style="color:#8c8c8c">年/月/日</text><text class="picker-icon">📅</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{z}}" class="required-star">*</text>籍贯</text></view><view class="native-place-selector"><picker mode="selector" range="{{D}}" range-key="{{'name'}}" bindchange="{{E}}" class="selector-picker province-picker"><view class="{{['picker-input', C && 'input-error']}}"><text wx:if="{{A}}" class="picker-text">{{B}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择省份</text><text class="picker-arrow">▼</text></view></picker></view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{F}}" class="required-star">*</text>联系方式</text></view><input class="{{['form-input', G && 'input-error']}}" placeholder="请输入手机号码" bindinput="{{H}}" bindblur="{{I}}" maxlength="11" type="number" value="{{J}}"/><text wx:if="{{K}}" class="error-text">{{L}}</text></view><view class="form-item"><view class="form-label-row"><text class="form-label">形象照</text></view><view class="image-upload-container"><view class="image-upload-area" bindtap="{{P}}"><button class="upload-btn">{{M}}</button><text class="upload-hint">{{N}}</text><text wx:if="{{O}}" class="upload-status">✓</text></view></view></view><view class="form-item"><view class="form-label-row"><text class="form-label">自我介绍</text></view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请简要概述，后续将会在人脉资源广场进行展示" maxlength="100" bindinput="{{Q}}" show-confirm-bar="false" value="{{R}}"></textarea></block><view class="char-count">{{S}}/100</view></view></view><view class="info-card"><view class="card-header"><image src="{{T}}" mode="" class="card-icon"></image><text class="card-title">教育背景</text><view class="{{['points-badge', V && 'completed', W && 'earned']}}">{{U}}</view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{X}}" class="required-star">*</text>毕业院校</text></view><input class="{{['form-input', Y && 'input-error']}}" placeholder="请输入院校名称" bindinput="{{Z}}" bindblur="{{aa}}" value="{{ab}}"/></view><view class="form-item"><view class="form-label-row"><text class="form-label">所属学院</text></view><input class="form-input" placeholder="请输入学院" bindinput="{{ac}}" value="{{ad}}"/></view><view class="form-item"><view class="form-label-row"><text class="form-label">毕业年份</text></view><picker mode="selector" range="{{ag}}" bindchange="{{ah}}" class="selector-picker"><view class="picker-input"><text wx:if="{{ae}}" class="picker-text">{{af}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择毕业年份</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{ai}}" class="required-star">*</text>所学专业</text></view><input class="{{['form-input', aj && 'input-error']}}" placeholder="请输入专业名称" bindinput="{{ak}}" bindblur="{{al}}" value="{{am}}"/></view></view><view class="info-card" style="padding-bottom:60rpx"><view class="card-header"><image src="{{an}}" mode="" class="card-icon"></image><text class="card-title">职业信息</text><view class="{{['points-badge', ap && 'completed', aq && 'earned']}}">{{ao}}</view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{ar}}" class="required-star">*</text>目前所属企业名称</text></view><input class="{{['form-input', as && 'input-error']}}" placeholder="请输入企业工商注册全称，如：天津海河教育园区投资有限公司" bindinput="{{at}}" bindblur="{{av}}" value="{{aw}}"/><text wx:if="{{ax}}" class="error-text">{{ay}}</text></view><view class="form-item"><view class="form-label-row"><text class="form-label">职位名称</text></view><view class="position-input-group"><picker mode="selector" range="{{aB}}" bindchange="{{aC}}" class="position-type-picker"><view class="position-type-input"><text wx:if="{{az}}" class="picker-text">{{aA}}</text><text wx:else class="picker-text" style="color:#8c8c8c">主体类型</text><text class="picker-arrow">▼</text></view></picker><input class="position-name-input" placeholder="请输入职位名称" bindinput="{{aD}}" value="{{aE}}"/></view></view><view class="form-item"><view class="form-label-row"><text class="form-label"><text wx:if="{{aF}}" class="required-star">*</text>行业标签</text></view><view class="industry-selector" bindtap="{{aJ}}"><view class="{{['picker-input', aI && 'input-error']}}"><text wx:if="{{aG}}" class="picker-text">{{aH}}</text><text wx:else class="picker-text" style="color:#8c8c8c">请选择行业</text><text class="picker-arrow">▼</text></view></view></view></view><view class="submit-section"><button class="{{['submit-btn', aL && 'disabled']}}" disabled="{{aM}}" bindtap="{{aN}}"> 完成资料填写(获得{{aK}}积分) </button></view></scroll-view><view wx:if="{{aO}}" class="industry-picker-modal" bindtap="{{aT}}"><view class="industry-picker-content" catchtap="{{aS}}"><view class="industry-header"><text class="industry-title">请选择行业</text><view class="industry-actions"><text class="cancel-btn" bindtap="{{aP}}">取消</text><text class="confirm-btn" bindtap="{{aQ}}">确定</text></view></view><scroll-view class="industry-tree-container" scroll-y="true"><view wx:for="{{aR}}" wx:for-item="level1" wx:key="l" class="tree-node level1-node"><view class="{{['tree-item', 'level1-item', level1.f && 'disabled']}}" bindtap="{{level1.g}}"><view class="tree-content"><view wx:if="{{level1.a}}" class="{{['expand-icon', level1.c && 'expanded']}}">{{level1.b}}</view><text class="tree-label">{{level1.d}}</text><text wx:if="{{level1.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level1.h}}" class="tree-children level2-children"><block wx:if="{{level1.i}}"><view wx:for="{{level1.j}}" wx:for-item="categoryName" wx:key="d"><view wx:if="{{categoryName.a}}" class="stream-category"><view class="stream-title">{{categoryName.b}}</view><view wx:for="{{categoryName.c}}" wx:for-item="level2" wx:key="j" class="tree-node level2-node"><view class="{{['tree-item', 'level2-item', level2.f && 'disabled']}}" bindtap="{{level2.g}}"><view class="tree-content"><view wx:if="{{level2.a}}" class="{{['expand-icon', level2.c && 'expanded']}}">{{level2.b}}</view><text class="tree-label">{{level2.d}}</text><text wx:if="{{level2.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level2.h}}" class="tree-children level3-children"><view wx:for="{{level2.i}}" wx:for-item="level3" wx:key="f" class="tree-node level3-node"><view class="tree-item level3-item" bindtap="{{level3.e}}"><view class="tree-content"><text class="tree-label">{{level3.a}}</text></view><view class="{{['tree-checkbox', level3.c && 'checked']}}" catchtap="{{level3.d}}"><view wx:if="{{level3.b}}" class="checkbox-inner"></view></view></view></view></view></view></view></view></block><block wx:else><view wx:for="{{level1.k}}" wx:for-item="level2" wx:key="j" class="tree-node level2-node"><view class="{{['tree-item', 'level2-item', level2.f && 'disabled']}}" bindtap="{{level2.g}}"><view class="tree-content"><view wx:if="{{level2.a}}" class="{{['expand-icon', level2.c && 'expanded']}}">{{level2.b}}</view><text class="tree-label">{{level2.d}}</text><text wx:if="{{level2.e}}" class="no-children-hint">（无子分类）</text></view></view><view wx:if="{{level2.h}}" class="tree-children level3-children"><view wx:for="{{level2.i}}" wx:for-item="level3" wx:key="f" class="tree-node level3-node"><view class="tree-item level3-item" bindtap="{{level3.e}}"><view class="tree-content"><text class="tree-label">{{level3.a}}</text></view><view class="{{['tree-checkbox', level3.c && 'checked']}}" catchtap="{{level3.d}}"><view wx:if="{{level3.b}}" class="checkbox-inner"></view></view></view></view></view></view></block></view></view></scroll-view></view></view><image-cropper wx:if="{{aW}}" bindconfirm="{{aU}}" bindcancel="{{aV}}" u-i="51416cea-0" bind:__l="__l" u-p="{{aW}}"></image-cropper></view>