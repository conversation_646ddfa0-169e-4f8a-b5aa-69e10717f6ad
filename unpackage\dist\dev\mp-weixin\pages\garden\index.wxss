.garden-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #d3e2ff, #fff);
}
/* 顶部区域 */
.header-section {
  position: relative;
  width: 100%;
}
.header-image-container {
  width: 100%;
}
.header-image-container .header-image {
  width: 100%;
  display: block;
}
.header-content {
  text-align: center;
  color: white;
  padding: 80rpx 40rpx 60rpx;
  position: relative;
  z-index: 2;
}
.main-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}
.sub-title {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}
/* 园区介绍区域 */
.intro-section {
  padding: 30rpx;
  margin-top: -80rpx;
  position: relative;
  z-index: 10;
}
.intro-section .intro-image {
  width: 100%;
  display: block;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 51, 153, 0.1);
}
/* 科技园区列表 */
.garden-list {
  padding: 0 30rpx 60rpx;
}
.garden-item {
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 51, 153, 0.1);
  transition: transform 0.3s ease;
}
.garden-item:active {
  transform: scale(0.98);
}
.garden-item:last-child {
  margin-bottom: 0;
}
.garden-item .garden-image {
  width: 100%;
  display: block;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}
.loading-container .loading-text {
  font-size: 28rpx;
  color: #666;
}
/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}
.empty-container .empty-text {
  font-size: 28rpx;
  color: #999;
}
