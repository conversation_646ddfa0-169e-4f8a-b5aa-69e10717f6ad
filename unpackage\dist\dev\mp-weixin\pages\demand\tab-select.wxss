.tab-select-container {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}
.header-section {
  padding: 60rpx 30rpx 40rpx 30rpx;
  text-align: center;
}
.title-area {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1a365d;
  margin-bottom: 20rpx;
}
.sub-title {
  font-size: 28rpx;
  color: #4a5568;
}
.button-section {
  padding: 40rpx 30rpx;
}
.select-button {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  position: relative;
}
.select-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}
.button-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-image {
  width: 60rpx;
  height: 60rpx;
}
.button-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.button-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a365d;
  margin-bottom: 10rpx;
}
.button-desc {
  font-size: 26rpx;
  color: #718096;
}
.arrow-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow {
  font-size: 32rpx;
  color: #a0aec0;
  font-weight: bold;
}
.factory-btn {
  border-left: 6rpx solid #e74c3c;
}
.office-btn {
  border-left: 6rpx solid #27ae60;
}
.jiegang-btn {
  border-left: 6rpx solid #d4af37;
}
.conversion-btn {
  border-left: 6rpx solid #5dade2;
}
.default-btn {
  border-left: 6rpx solid #95a5a6;
}
.tip-section {
  padding: 40rpx 30rpx;
  text-align: center;
}
.tip-text {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.6;
}
