"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      jobId: "",
      jobDetail: {},
      jobTags: [],
      loading: true
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/index/job-detail.vue:79", "职位详情页面加载参数:", options);
    if (options.jobData) {
      try {
        this.jobDetail = JSON.parse(decodeURIComponent(options.jobData));
        common_vendor.index.__f__("log", "at pages/index/job-detail.vue:86", "💼 使用传递的职位数据:", this.jobDetail);
        if (this.jobDetail.jobTags) {
          this.jobTags = this.jobDetail.jobTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0);
        }
        common_vendor.index.setNavigationBarTitle({
          title: this.jobDetail.jobTitle || "职位详情"
        });
        this.loading = false;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/job-detail.vue:100", "解析职位数据失败:", error);
        if (options.id) {
          this.jobId = options.id;
          this.loadJobDetail();
        } else {
          this.showErrorAndGoBack("数据解析失败");
        }
      }
    } else if (options.id) {
      this.jobId = options.id;
      this.loadJobDetail();
    } else {
      this.showErrorAndGoBack("缺少职位参数");
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 自定义分享标题
    getShareTitle() {
      return `天大海棠 - ${this.jobDetail.jobTitle || "职位详情"}`;
    },
    // 自定义分享路径
    getSharePath() {
      return `/pages/index/job-detail?id=${this.jobId}`;
    },
    // 自定义分享内容
    getShareContent() {
      return `职位详情分享 - ${this.jobDetail.jobTitle || "职位"}`;
    },
    // 显示错误并返回
    showErrorAndGoBack(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 获取职位详情
    async loadJobDetail() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/index/job-detail.vue:152", "💼 开始获取职位详情，职位ID:", this.jobId);
        const response = await utils_request.request.get(`/miniapp/job/detail/${this.jobId}`);
        common_vendor.index.__f__("log", "at pages/index/job-detail.vue:155", "💼 职位详情响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.jobDetail = response.data.data || {};
          common_vendor.index.__f__("log", "at pages/index/job-detail.vue:159", "💼 职位详情数据:", this.jobDetail);
          if (this.jobDetail.jobTags) {
            this.jobTags = this.jobDetail.jobTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0);
          }
          common_vendor.index.setNavigationBarTitle({
            title: this.jobDetail.jobTitle || "职位详情"
          });
        } else {
          common_vendor.index.__f__("log", "at pages/index/job-detail.vue:172", "💼 ❌ 职位详情获取失败");
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "获取职位详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/job-detail.vue:179", "💼 获取职位详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "暂无";
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/job-detail.vue:200", "日期格式化失败:", error);
        return dateString;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : {}, {
    b: !$data.loading
  }, !$data.loading ? common_vendor.e({
    c: common_vendor.t($data.jobDetail.jobTitle || "职位名称"),
    d: common_vendor.t($data.jobDetail.companyName || "公司名称"),
    e: common_vendor.t($data.jobDetail.companyType || "天开"),
    f: common_vendor.t($data.jobDetail.companyScale || "50人"),
    g: $data.jobTags.length > 0
  }, $data.jobTags.length > 0 ? {
    h: common_vendor.f($data.jobTags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    i: common_vendor.t($data.jobDetail.jobDescription || "暂无岗位描述"),
    j: common_vendor.t($data.jobDetail.requirements || "暂无任职要求")
  }) : {}, {
    k: !$data.loading
  }, !$data.loading ? common_vendor.e({
    l: $data.jobDetail.workLocation
  }, $data.jobDetail.workLocation ? {
    m: $options.getImagePath("icon-b1.png"),
    n: common_vendor.t($data.jobDetail.address)
  } : {}, {
    o: $data.jobDetail.contactInfo
  }, $data.jobDetail.contactInfo ? {
    p: $options.getImagePath("icon-b2.png"),
    q: common_vendor.t($data.jobDetail.contactInfo)
  } : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/job-detail.js.map
