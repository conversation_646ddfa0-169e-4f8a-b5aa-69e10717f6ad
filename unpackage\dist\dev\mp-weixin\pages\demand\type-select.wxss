.type-select-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
}
.header-section {
  padding: 40rpx 50rpx 20rpx;
}
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  height: 88rpx;
}
.nav-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}
.back-icon {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}
.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}
.nav-title {
  font-size: 36rpx;
  color: white;
  font-weight: 600;
}
.nav-right {
  width: 80rpx;
}
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx #c7cce1;
  transition: transform 0.2s ease;
}
.category-item:active {
  transform: scale(0.95);
}
.category-icon {
  margin-bottom: 0rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-icon image {
  width: 48rpx;
  height: 48rpx;
}
.category-text {
  font-size: 24rpx;
  color: #111111;
  font-weight: 900;
}
.tip-section {
  padding: 40rpx 30rpx;
  text-align: center;
}
.tip-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
