.industry-list-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 20rpx;
}
/* 企业列表 */
.companies-list {
  padding: 20rpx;
  margin-top: 0;
}
/* 加载和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.empty-desc {
  font-size: 24rpx;
  color: #999;
}
.company-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  display: flex;
  align-items: center;
  gap: 30rpx;
}
/* 头像部分 */
.company-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  flex-shrink: 0;
  overflow: hidden;
}
.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
/* 标题+标签部分 */
.company-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.company-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.2;
}
.company-tags {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.tag-item {
  font-size: 24rpx;
  color: #000;
  background-color: #c6f0ff;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
}
/* 右箭头部分 */
.arrow-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.arrow-image {
  width: 40rpx;
  height: 40rpx;
}
/* 加载更多和完成按钮 */
.load-more,
.complete-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  margin-top: 20rpx;
}
.load-more-text {
  font-size: 36rpx;
  color: #505050;
  font-weight: 900;
}
.complete-btn {
  background-color: #51b7dd;
  border-radius: 50rpx;
  margin: 20rpx 40rpx;
  padding: 20rpx 0;
}
.complete-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}
/* 新的分页加载样式 */
.load-more-container {
  text-align: center;
  padding: 40rpx 0;
  margin-top: 20rpx;
}
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-more-text {
  font-size: 28rpx;
  color: #999;
}
.no-more {
  padding: 20rpx 0;
}
.no-more-text {
  font-size: 26rpx;
  color: #999;
}
.search-result-tip {
  text-align: center;
  padding: 40rpx 0;
  margin-top: 20rpx;
}
.search-result-text {
  font-size: 28rpx;
  color: #666;
}
/* 调试样式 */
.debug-container {
  padding: 20rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  border: 2rpx solid #f0f0f0;
}
.debug-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.debug-btn {
  background-color: #007aff;
  color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
}
/* 右侧悬浮按钮 */
.floating-buttons {
  position: fixed;
  right: 0rpx;
  top: 36%;
  transform: translateY(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx #c1c5d4;
}
.floating-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.floating-btn.question-btn {
  background: #023caa;
  color: white;
}
.floating-btn.demand-btn {
  background: #fad676;
  color: #333;
}
.floating-btn-text {
  font-size: 26rpx;
}
/* 联系人弹窗样式 - 与需求广场完全一致 */
.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
