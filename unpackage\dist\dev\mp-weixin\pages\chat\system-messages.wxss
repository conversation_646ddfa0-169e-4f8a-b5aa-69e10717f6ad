.system-messages-container.data-v-91dfb17b {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
/* 顶部标题栏 */
.header.data-v-91dfb17b {
  padding: 20rpx 30rpx;
  padding-top: 60rpx;
}
.header-content.data-v-91dfb17b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-title.data-v-91dfb17b {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
}
.close-btn.data-v-91dfb17b {
  padding: 10rpx;
}
.close-icon.data-v-91dfb17b {
  width: 32rpx;
  height: 32rpx;
}
/* 消息列表 */
.messages-list.data-v-91dfb17b {
  flex: 1;
  background-color: #f3f7fc;
  padding-top: 20rpx;
}
/* 加载状态 */
.loading-container.data-v-91dfb17b {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-text.data-v-91dfb17b {
  font-size: 28rpx;
  color: #666;
}
/* 空状态 */
.empty-container.data-v-91dfb17b {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.empty-text.data-v-91dfb17b {
  font-size: 28rpx;
  color: #999;
}
/* 消息项 */
.message-item.data-v-91dfb17b {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 30rpx 30rpx;
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  transition: transform 0.2s;
}
.message-item.data-v-91dfb17b:active {
  transform: scale(0.98);
}
.avatar-container.data-v-91dfb17b {
  position: relative;
  margin-right: 24rpx;
}
.avatar.data-v-91dfb17b {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f0f0;
}
.unread-dot.data-v-91dfb17b {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: #FF4757;
  border-radius: 100rpx;
  border: 2rpx solid #ffffff;
}
.content-area.data-v-91dfb17b {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
}
.title-time-row.data-v-91dfb17b {
  display: flex;
  flex-direction: column;
  margin-bottom: 12rpx;
}
.message-title.data-v-91dfb17b {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.time.data-v-91dfb17b {
  font-size: 24rpx;
  color: #999999;
  flex-shrink: 0;
  margin-left: 16rpx;
}
.content-action-row.data-v-91dfb17b {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.message-content.data-v-91dfb17b {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 10rpx;
}
.action-text.data-v-91dfb17b {
  font-size: 24rpx;
  color: #003399;
  font-weight: 500;
}
/* 顶部操作按钮样式 */
.top-actions.data-v-91dfb17b {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(180deg, #ffffff, #f3f7fc);
  border-bottom: 1rpx solid #f0f0f0;
  gap: 40rpx;
}
.top-action-btn.data-v-91dfb17b {
  font-size: 28rpx;
  color: #003399;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.2s;
}
.top-action-btn.data-v-91dfb17b:active {
  background-color: #f0f4ff;
  opacity: 0.8;
}
