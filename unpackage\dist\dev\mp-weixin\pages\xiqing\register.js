"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      topImageData: null,
      loading: true,
      formFields: [],
      // 动态表单字段
      formData: {},
      // 表单数据
      formErrors: {},
      // 表单验证错误
      activityData: null
      // 活动详情数据
    };
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/xiqing/register.vue:262", "🎭 西青金种子路演报名页面加载");
    common_vendor.index.setNavigationBarTitle({
      title: "路演报名"
    });
    this.getTopImage();
    this.loadActivityConfig();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 西青金种子路演报名";
    },
    // 自定义分享内容
    getShareContent() {
      return "西青金种子路演报名分享";
    },
    // 获取顶图
    async getTopImage() {
      try {
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:291", "🖼️ 开始获取路演报名顶图...");
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "roadshow");
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:294", "🖼️ 路演报名顶图响应:", response);
        if (response && response.success && response.data && response.data.code === 200) {
          const topImageList = response.data.data || [];
          if (topImageList.length > 0) {
            const topImage = topImageList[0];
            this.topImageData = {
              imageUrl: utils_imageUtils.processServerImageUrl(topImage.imageUrl)
            };
            common_vendor.index.__f__("log", "at pages/xiqing/register.vue:303", "🖼️ 处理后的顶图数据:", this.topImageData);
          } else {
            this.topImageData = null;
          }
        } else {
          this.topImageData = null;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:311", "🖼️ 获取路演报名顶图失败:", error);
        this.topImageData = null;
      }
    },
    // 顶图加载失败处理
    onTopImageError() {
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:318", "🖼️ 顶图加载失败，使用默认样式");
      this.topImageData = null;
    },
    // 加载活动配置
    async loadActivityConfig() {
      try {
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:325", "🎭 开始获取默认路演活动详情...");
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:326", "🎭 API地址: GET /miniapp/xiqing/activity-config/app/getDefaultActivity");
        this.loading = true;
        const response = await utils_request.request.get("/miniapp/xiqing/activity-config/app/getDefaultActivity");
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:331", "🎭 活动配置API响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const activityData = response.data.data;
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:335", "🎭 活动配置数据:", activityData);
          this.activityData = activityData;
          if (activityData && activityData.formConfig) {
            try {
              const formConfig = JSON.parse(activityData.formConfig);
              common_vendor.index.__f__("log", "at pages/xiqing/register.vue:344", "🎭 解析后的表单配置:", formConfig);
              this.formFields = this.processFormFields(formConfig);
              this.initFormData();
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/xiqing/register.vue:348", "🎭 解析formConfig失败:", error);
              this.generateDefaultFormFields();
            }
          } else if (activityData && activityData.formFields) {
            this.formFields = activityData.formFields;
            this.initFormData();
          } else if (activityData && Array.isArray(activityData)) {
            this.formFields = activityData;
            this.initFormData();
          } else {
            common_vendor.index.__f__("log", "at pages/xiqing/register.vue:359", "🎭 没有找到表单字段，使用默认配置");
            this.generateDefaultFormFields();
          }
        } else {
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:363", "🎭 ❌ 活动配置数据获取失败！");
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:364", "🎭 响应详情:", response);
          this.generateDefaultFormFields();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:368", "🎭 获取活动配置失败:", error);
        this.generateDefaultFormFields();
        common_vendor.index.showToast({
          title: "表单配置加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理表单字段配置
    processFormFields(formConfig) {
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:383", "🎭 开始处理表单字段:", formConfig);
      return formConfig.map((field, index) => {
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:386", `🎭 处理第${index + 1}个字段:`, field);
        const processedField = {
          fieldName: field.name,
          fieldLabel: field.label,
          required: field.required || false,
          placeholder: `请输入${field.label}`,
          options: []
        };
        switch (field.type) {
          case "input":
            if (field.options && field.options.trim()) {
              processedField.fieldType = "picker";
              processedField.placeholder = `请选择${field.label}`;
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            } else {
              processedField.fieldType = "input";
              processedField.inputType = "text";
            }
            break;
          case "tel":
            processedField.fieldType = "input";
            processedField.inputType = "number";
            processedField.placeholder = `请输入${field.label}`;
            break;
          case "number":
            processedField.fieldType = "input";
            processedField.inputType = "number";
            processedField.placeholder = `请输入${field.label}`;
            break;
          case "textarea":
            processedField.fieldType = "textarea";
            processedField.maxLength = 500;
            processedField.placeholder = `请输入${field.label}`;
            break;
          case "radio":
            processedField.fieldType = "radio";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "radio_other":
            processedField.fieldType = "radio_other";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "select":
            processedField.fieldType = "picker";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "select_other":
            processedField.fieldType = "select_other";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "checkbox":
            processedField.fieldType = "checkbox";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "checkbox_other":
            processedField.fieldType = "checkbox_other";
            processedField.placeholder = `请选择${field.label}`;
            if (field.options && field.options.trim()) {
              processedField.options = field.options.split(",").map((opt) => opt.trim());
            }
            break;
          case "date":
            processedField.fieldType = "date";
            processedField.placeholder = `请选择${field.label}`;
            break;
          case "file":
            processedField.fieldType = "file";
            processedField.placeholder = field.placeholder || `请上传${field.label}`;
            break;
          default:
            processedField.fieldType = "input";
            processedField.inputType = "text";
        }
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:486", `🎭 处理后的字段:`, processedField);
        return processedField;
      });
    },
    // 初始化表单数据
    initFormData() {
      const formData = {};
      const formErrors = {};
      this.formFields.forEach((field) => {
        formData[field.fieldName] = field.defaultValue || "";
        formErrors[field.fieldName] = false;
      });
      this.formData = formData;
      this.formErrors = formErrors;
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:504", "🎭 初始化表单数据:", this.formData);
    },
    // 生成默认表单字段
    generateDefaultFormFields() {
      this.formFields = [
        {
          fieldName: "projectName",
          fieldLabel: "项目名称",
          fieldType: "input",
          required: true,
          placeholder: "请输入项目名称"
        },
        {
          fieldName: "contactName",
          fieldLabel: "联系人姓名",
          fieldType: "input",
          required: true,
          placeholder: "请输入联系人姓名"
        },
        {
          fieldName: "contactPhone",
          fieldLabel: "联系电话",
          fieldType: "input",
          inputType: "number",
          required: true,
          placeholder: "请输入联系电话"
        },
        {
          fieldName: "projectDescription",
          fieldLabel: "项目介绍",
          fieldType: "textarea",
          required: true,
          placeholder: "请详细描述您的项目",
          maxLength: 500
        }
      ];
      this.initFormData();
    },
    // 获取字段值
    getFieldValue(fieldName) {
      return this.formData[fieldName] || "";
    },
    // 获取字段错误状态
    getFieldError(fieldName) {
      return this.formErrors[fieldName] || false;
    },
    // 选择器变化事件
    onPickerChange(fieldName, options, event) {
      const index = event.detail.value;
      this.formData[fieldName] = options[index];
    },
    // 日期选择器变化事件
    onDateChange(fieldName, event) {
      this.formData[fieldName] = event.detail.value;
    },
    // 文件选择
    chooseFile(fieldName) {
      common_vendor.index.showActionSheet({
        itemList: ["选择图片", "选择文档"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.chooseImageFile(fieldName);
          } else if (res.tapIndex === 1) {
            this.chooseDocumentFile(fieldName);
          }
        }
      });
    },
    // 选择图片文件
    chooseImageFile(fieldName) {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:591", "选择图片文件成功:", tempFilePath);
          this.uploadFile(fieldName, tempFilePath);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/xiqing/register.vue:595", "选择图片失败:", error);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 选择文档文件
    chooseDocumentFile(fieldName) {
      common_vendor.index.chooseMessageFile({
        count: 1,
        type: "file",
        success: (res) => {
          const tempFile = res.tempFiles[0];
          const tempFilePath = tempFile.path;
          const fileName = tempFile.name;
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:613", "选择文档文件成功:", tempFilePath, "文件名:", fileName);
          this.uploadFile(fieldName, tempFilePath, fileName);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/xiqing/register.vue:617", "选择文档失败:", error);
          common_vendor.index.showToast({
            title: "选择文档失败",
            icon: "none"
          });
        }
      });
    },
    // 统一的文件上传方法
    uploadFile(fieldName, tempFilePath, fileName = "") {
      common_vendor.index.showLoading({
        title: "上传中..."
      });
      utils_request.request.upload("/common/upload", tempFilePath, "file").then((result) => {
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:635", "上传结果完整信息:", result);
        if (result.success) {
          let fileUrl = result.url;
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:641", "🔥 开始保存数据");
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:642", "🔥 字段名:", fieldName);
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:643", "🔥 文件URL:", fileUrl);
          common_vendor.index.__f__("log", "at pages/xiqing/register.vue:644", "🔥 文件名:", fileName);
          this.formData[fieldName] = fileUrl;
          common_vendor.index.showToast({
            title: "上传成功",
            icon: "success"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/xiqing/register.vue:654", "文件上传失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "上传失败",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:662", "文件上传失败:", error);
        common_vendor.index.showToast({
          title: "上传失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 复选框切换
    toggleCheckbox(fieldName, option) {
      if (!this.formData[fieldName]) {
        this.formData[fieldName] = [];
      }
      const currentValues = Array.isArray(this.formData[fieldName]) ? this.formData[fieldName] : [];
      const index = currentValues.indexOf(option);
      if (index > -1) {
        currentValues.splice(index, 1);
      } else {
        currentValues.push(option);
      }
      this.formData[fieldName] = [...currentValues];
    },
    // 检查复选框是否选中
    isCheckboxChecked(fieldName, option) {
      const currentValues = this.formData[fieldName];
      return Array.isArray(currentValues) && currentValues.includes(option);
    },
    // 单选框选择
    selectRadio(fieldName, option) {
      this.formData[fieldName] = option;
      if (this.formData[fieldName + "_other"]) {
        this.formData[fieldName + "_other"] = "";
      }
    },
    // 选择单选框的其他选项
    selectRadioOther(fieldName) {
      this.formData[fieldName] = "其他";
    },
    // 检查单选框其他是否选中
    isRadioOtherSelected(fieldName) {
      return this.formData[fieldName] === "其他";
    },
    // 单选框其他输入
    onRadioOtherInput(fieldName, event) {
      this.formData[fieldName + "_other"] = event.detail.value;
    },
    // 切换复选框其他选项
    toggleCheckboxOther(fieldName) {
      if (!this.formData[fieldName]) {
        this.formData[fieldName] = [];
      }
      const currentValues = Array.isArray(this.formData[fieldName]) ? this.formData[fieldName] : [];
      const index = currentValues.indexOf("其他");
      if (index > -1) {
        currentValues.splice(index, 1);
        this.formData[fieldName + "_other"] = "";
      } else {
        currentValues.push("其他");
      }
      this.formData[fieldName] = [...currentValues];
    },
    // 检查复选框其他是否选中
    isCheckboxOtherSelected(fieldName) {
      const currentValues = this.formData[fieldName];
      return Array.isArray(currentValues) && currentValues.includes("其他");
    },
    // 获取下拉选择器其他的选项
    getSelectOtherOptions(options) {
      return [...options, "其他"];
    },
    // 下拉选择器其他变化
    onSelectOtherChange(fieldName, options, event) {
      const allOptions = this.getSelectOtherOptions(options);
      const index = event.detail.value;
      const selectedValue = allOptions[index];
      this.formData[fieldName] = selectedValue;
      if (selectedValue !== "其他") {
        this.formData[fieldName + "_other"] = "";
      }
    },
    // 检查下拉选择器其他是否选中
    isSelectOtherSelected(fieldName) {
      return this.formData[fieldName] === "其他";
    },
    // 获取下拉选择器其他的显示值
    getSelectOtherDisplayValue(fieldName) {
      const value = this.formData[fieldName];
      if (value === "其他" && this.formData[fieldName + "_other"]) {
        return `其他: ${this.formData[fieldName + "_other"]}`;
      }
      return value;
    },
    // 验证字段
    validateField(field) {
      const value = this.getFieldValue(field.fieldName);
      let hasError = false;
      if (field.required) {
        if (!value || value.trim() === "") {
          hasError = true;
        }
      }
      if (value && value.trim() !== "") {
        if (field.fieldType === "input" && field.inputType === "number" && (field.fieldLabel.includes("电话") || field.fieldLabel.includes("手机") || field.fieldName.includes("phone"))) {
          const phoneRegex = /^1[3-9]\d{9}$/;
          if (!phoneRegex.test(value.trim())) {
            hasError = true;
          }
        }
        if (field.fieldType === "input" && field.inputType === "number" && !field.fieldLabel.includes("电话") && !field.fieldLabel.includes("手机") && !field.fieldName.includes("phone")) {
          const numberRegex = /^\d+(\.\d+)?$/;
          if (!numberRegex.test(value.trim())) {
            hasError = true;
          }
        }
      }
      this.formErrors[field.fieldName] = hasError;
    },
    // 验证所有字段
    validateAllFields() {
      let hasError = false;
      let errorMessages = [];
      this.formFields.forEach((field) => {
        this.validateField(field);
        if (this.getFieldError(field.fieldName)) {
          hasError = true;
          const value = this.getFieldValue(field.fieldName);
          if (!value || value.trim() === "") {
            if (field.required) {
              errorMessages.push(`请填写${field.fieldLabel}`);
            }
          } else {
            if (field.fieldType === "input" && field.inputType === "number") {
              if (field.fieldLabel.includes("电话") || field.fieldLabel.includes("手机") || field.fieldName.includes("phone")) {
                errorMessages.push(`${field.fieldLabel}格式不正确`);
              } else {
                errorMessages.push(`${field.fieldLabel}必须为数字`);
              }
            }
          }
        }
      });
      if (hasError && errorMessages.length > 0) {
        common_vendor.index.showToast({
          title: errorMessages[0],
          icon: "none",
          duration: 2e3
        });
      }
      return !hasError;
    },
    // 构建表单数据，参考需求提交的处理方式
    buildFormDataWithAnswers() {
      try {
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:864", "🎭 开始构建表单数据...");
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:865", "🎭 原始表单字段:", this.formFields);
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:866", "🎭 用户填写数据:", this.formData);
        const formDataWithAnswers = this.formFields.map((field) => {
          let fieldValue = this.formData[field.fieldName] || "";
          if (fieldValue === "其他" && this.formData[field.fieldName + "_other"]) {
            fieldValue = this.formData[field.fieldName + "_other"];
          }
          if (Array.isArray(fieldValue)) {
            const otherIndex = fieldValue.indexOf("其他");
            if (otherIndex > -1 && this.formData[field.fieldName + "_other"]) {
              fieldValue[otherIndex] = this.formData[field.fieldName + "_other"];
            }
            fieldValue = fieldValue.join(",");
          }
          return {
            name: field.fieldName,
            type: field.fieldType,
            label: field.fieldLabel,
            options: field.options ? field.options.join(",") : "",
            required: field.required,
            value: fieldValue
            // 用户填写的答案
          };
        });
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:898", "🎭 构建的表单数据:", formDataWithAnswers);
        return JSON.stringify(formDataWithAnswers);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:901", "🎭 构建表单数据失败:", error);
        return JSON.stringify(this.formData);
      }
    },
    // 获取用户信息
    async getCurrentUserInfo() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:911", "👤 获取到的用户信息:", userInfo);
        return userInfo;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:914", "👤 获取用户信息失败:", error);
        return null;
      }
    },
    // 提交表单
    async submitForm() {
      var _a, _b, _c, _d;
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:921", "🎭 提交路演报名表单");
      if (!this.validateAllFields()) {
        return;
      }
      const userInfo = await this.getCurrentUserInfo();
      if (!userInfo || !userInfo.userId) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:938", "✅ 表单验证通过");
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:939", "👤 用户信息:", userInfo);
      common_vendor.index.__f__("log", "at pages/xiqing/register.vue:940", "🎭 表单数据:", this.formData);
      common_vendor.index.showLoading({
        title: "提交中...",
        mask: true
      });
      try {
        const submitData = {
          activityId: ((_a = this.activityData) == null ? void 0 : _a.activityId) || 0,
          activityTitle: ((_b = this.activityData) == null ? void 0 : _b.activityTitle) || "西青金种子路演",
          formData: this.buildFormDataWithAnswers(),
          // 包含完整的表单配置和答案
          userId: userInfo.userId,
          remark: "",
          status: "0"
          // 默认状态
        };
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:959", "📤 提交路演报名数据:", submitData);
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:960", "📤 API地址: POST /miniapp/xiqing/registration-manage/app/register");
        const response = await utils_request.request.post("/miniapp/xiqing/registration-manage/app/register", submitData);
        common_vendor.index.__f__("log", "at pages/xiqing/register.vue:964", "📥 API响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.success && response.data && response.data.code === 200) {
          common_vendor.index.showModal({
            title: "报名成功",
            content: "您的路演报名已成功提交，我们会尽快审核您的申请",
            showCancel: false,
            confirmText: "确定",
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        } else {
          const errorMsg = ((_c = response == null ? void 0 : response.data) == null ? void 0 : _c.msg) || ((_d = response == null ? void 0 : response.data) == null ? void 0 : _d.message) || (response == null ? void 0 : response.message) || "报名失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/xiqing/register.vue:992", "🎭 提交路演报名失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none",
          duration: 2e3
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.processServerImageUrl($data.topImageData.imageUrl),
    b: common_vendor.o((...args) => $options.onTopImageError && $options.onTopImageError(...args)),
    c: $data.loading
  }, $data.loading ? {} : {
    d: $options.getImagePath("title_icon1.png"),
    e: common_vendor.f($data.formFields, (field, index, i0) => {
      return common_vendor.e({
        a: field.required && !$options.getFieldValue(field.fieldName)
      }, field.required && !$options.getFieldValue(field.fieldName) ? {} : {}, {
        b: common_vendor.t(field.fieldLabel),
        c: field.fieldType === "input"
      }, field.fieldType === "input" ? {
        d: $options.getFieldError(field.fieldName) ? 1 : "",
        e: field.placeholder || `请输入${field.fieldLabel}`,
        f: field.inputType || "text",
        g: common_vendor.o(($event) => $options.validateField(field), index),
        h: $data.formData[field.fieldName],
        i: common_vendor.o(($event) => $data.formData[field.fieldName] = $event.detail.value, index)
      } : field.fieldType === "textarea" ? {
        k: $options.getFieldError(field.fieldName) ? 1 : "",
        l: field.placeholder || `请输入${field.fieldLabel}`,
        m: field.maxLength || 500,
        n: common_vendor.o(($event) => $options.validateField(field), index),
        o: $data.formData[field.fieldName],
        p: common_vendor.o(($event) => $data.formData[field.fieldName] = $event.detail.value, index)
      } : field.fieldType === "picker" ? common_vendor.e({
        r: $data.formData[field.fieldName]
      }, $data.formData[field.fieldName] ? {
        s: common_vendor.t($data.formData[field.fieldName])
      } : {
        t: common_vendor.t(field.placeholder || `请选择${field.fieldLabel}`)
      }, {
        v: $options.getFieldError(field.fieldName) ? 1 : "",
        w: field.options || [],
        x: common_vendor.o(($event) => $options.onPickerChange(field.fieldName, field.options, $event), index)
      }) : field.fieldType === "radio" ? {
        z: common_vendor.f(field.options, (option, optIndex, i1) => {
          return common_vendor.e({
            a: $data.formData[field.fieldName] === option
          }, $data.formData[field.fieldName] === option ? {} : {}, {
            b: $data.formData[field.fieldName] === option ? 1 : "",
            c: common_vendor.t(option),
            d: optIndex,
            e: common_vendor.o(($event) => $options.selectRadio(field.fieldName, option), optIndex)
          });
        })
      } : field.fieldType === "radio_other" ? common_vendor.e({
        B: common_vendor.f(field.options, (option, optIndex, i1) => {
          return common_vendor.e({
            a: $data.formData[field.fieldName] === option
          }, $data.formData[field.fieldName] === option ? {} : {}, {
            b: $data.formData[field.fieldName] === option ? 1 : "",
            c: common_vendor.t(option),
            d: optIndex,
            e: common_vendor.o(($event) => $options.selectRadio(field.fieldName, option), optIndex)
          });
        }),
        C: $options.isRadioOtherSelected(field.fieldName)
      }, $options.isRadioOtherSelected(field.fieldName) ? {} : {}, {
        D: $options.isRadioOtherSelected(field.fieldName) ? 1 : "",
        E: common_vendor.o(($event) => $options.selectRadioOther(field.fieldName), index),
        F: $options.isRadioOtherSelected(field.fieldName)
      }, $options.isRadioOtherSelected(field.fieldName) ? {
        G: common_vendor.o([($event) => $data.formData[field.fieldName + "_other"] = $event.detail.value, index, ($event) => $options.onRadioOtherInput(field.fieldName, $event), index], index),
        H: $data.formData[field.fieldName + "_other"]
      } : {}) : field.fieldType === "checkbox" ? {
        J: common_vendor.f(field.options, (option, optIndex, i1) => {
          return common_vendor.e({
            a: $options.isCheckboxChecked(field.fieldName, option)
          }, $options.isCheckboxChecked(field.fieldName, option) ? {} : {}, {
            b: $options.isCheckboxChecked(field.fieldName, option) ? 1 : "",
            c: common_vendor.t(option),
            d: optIndex,
            e: common_vendor.o(($event) => $options.toggleCheckbox(field.fieldName, option), optIndex)
          });
        })
      } : field.fieldType === "checkbox_other" ? common_vendor.e({
        L: common_vendor.f(field.options, (option, optIndex, i1) => {
          return common_vendor.e({
            a: $options.isCheckboxChecked(field.fieldName, option)
          }, $options.isCheckboxChecked(field.fieldName, option) ? {} : {}, {
            b: $options.isCheckboxChecked(field.fieldName, option) ? 1 : "",
            c: common_vendor.t(option),
            d: optIndex,
            e: common_vendor.o(($event) => $options.toggleCheckbox(field.fieldName, option), optIndex)
          });
        }),
        M: $options.isCheckboxOtherSelected(field.fieldName)
      }, $options.isCheckboxOtherSelected(field.fieldName) ? {} : {}, {
        N: $options.isCheckboxOtherSelected(field.fieldName) ? 1 : "",
        O: common_vendor.o(($event) => $options.toggleCheckboxOther(field.fieldName), index),
        P: $options.isCheckboxOtherSelected(field.fieldName)
      }, $options.isCheckboxOtherSelected(field.fieldName) ? {
        Q: $data.formData[field.fieldName + "_other"],
        R: common_vendor.o(($event) => $data.formData[field.fieldName + "_other"] = $event.detail.value, index)
      } : {}) : field.fieldType === "select_other" ? common_vendor.e({
        T: $data.formData[field.fieldName]
      }, $data.formData[field.fieldName] ? {
        U: common_vendor.t($data.formData[field.fieldName])
      } : {
        V: common_vendor.t(field.placeholder || `请选择${field.fieldLabel}`)
      }, {
        W: $options.getFieldError(field.fieldName) ? 1 : "",
        X: $options.getSelectOtherOptions(field.options),
        Y: common_vendor.o(($event) => $options.onSelectOtherChange(field.fieldName, field.options, $event), index),
        Z: $options.isSelectOtherSelected(field.fieldName)
      }, $options.isSelectOtherSelected(field.fieldName) ? {
        aa: $data.formData[field.fieldName + "_other"],
        ab: common_vendor.o(($event) => $data.formData[field.fieldName + "_other"] = $event.detail.value, index)
      } : {}) : field.fieldType === "date" ? common_vendor.e({
        ad: $data.formData[field.fieldName]
      }, $data.formData[field.fieldName] ? {
        ae: common_vendor.t($data.formData[field.fieldName])
      } : {
        af: common_vendor.t(field.placeholder || `请选择${field.fieldLabel}`)
      }, {
        ag: $options.getFieldError(field.fieldName) ? 1 : "",
        ah: common_vendor.o(($event) => $options.onDateChange(field.fieldName, $event), index)
      }) : field.fieldType === "file" ? common_vendor.e({
        aj: common_vendor.t($data.formData[field.fieldName] ? "重新选择" : "选择文件"),
        ak: common_vendor.t($data.formData[field.fieldName] ? "已选择文件" : field.placeholder),
        al: $data.formData[field.fieldName]
      }, $data.formData[field.fieldName] ? {} : {}, {
        am: common_vendor.o(($event) => $options.chooseFile(field.fieldName), index)
      }) : {}, {
        j: field.fieldType === "textarea",
        q: field.fieldType === "picker",
        y: field.fieldType === "radio",
        A: field.fieldType === "radio_other",
        I: field.fieldType === "checkbox",
        K: field.fieldType === "checkbox_other",
        S: field.fieldType === "select_other",
        ac: field.fieldType === "date",
        ai: field.fieldType === "file",
        an: index
      });
    }),
    f: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  }, {
    g: !$data.loading && $data.formFields.length === 0
  }, !$data.loading && $data.formFields.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/xiqing/register.js.map
