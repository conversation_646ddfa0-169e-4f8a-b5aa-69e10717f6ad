.industry-map-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
}
/* 产业头部区域 */
.industry-header {
  display: flex;
  align-items: flex-start;
  padding: 60rpx 30rpx;
  color: #fff;
  position: relative;
  background-color: #e4f5ff;
}
/* 背景图片样式 */
.header-bg-image {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 0;
}
.industry-title-section {
  flex: 1;
  position: relative;
  z-index: 1;
}
.flex {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 企业数量统计 - 移到右上角 */
}
.flex .industry-main-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.flex .industry-stats {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  padding: 8rpx 32rpx;
  border-radius: 100rpx;
  border: 1px solid #4675bb;
}
.industry-subtitle {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  display: block;
  margin-bottom: 50rpx;
}
/* 上中下游标识 */
.stream-tags {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.stream-tag {
  background: #88b0ee;
  padding: 6rpx 24rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.stream-tag.active {
  background: #4675bb;
  transform: scale(1.05);
}
.stream-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 600;
}
.industry-icon-section {
  width: 120rpx;
  height: 120rpx;
  position: relative;
}
.industry-main-icon {
  width: 100%;
  height: 100%;
}
.stats-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}
/* 分类标题 */
.category-section {
  margin-bottom: 30rpx;
  padding: 0 30rpx 1rpx;
  border-radius: 16rpx;
}
/* 上游背景色 */
#upstream-section {
  background-color: #4675bb;
}
/* 中游背景色 */
#midstream-section {
  background-color: #fcc584;
}
/* 下游背景色 */
#downstream-section {
  background-color: #f28686;
}
/* 单一模块背景色 - 使用上游的蓝色 */
.single-category {
  background-color: #4675bb;
  padding: 30rpx 30rpx 1rpx;
  /* 顶部和左右内边距，底部最小内边距 */
  border-radius: 16rpx;
}
/* 单一模块中的所有section-block都有完整圆角 */
.single-category .section-block {
  border-radius: 16rpx !important;
}
/* 中游和下游文字颜色调整 */
#midstream-section .category-name,
#downstream-section .category-name {
  color: #fff;
}
.category-title {
  margin: 0 10rpx;
  padding: 30rpx 0rpx;
}
.category-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #fff;
}
/* 产业细分列表 */
.industry-sections {
  margin-top: 30rpx;
  padding: 0 30rpx 30rpx;
}
.section-block {
  background: #fff;
  margin-bottom: 20rpx;
  overflow: hidden;
}
/* 有上中下游分类时的样式 */
.category-section:not(.single-category) .section-block:first-child {
  border-radius: 0 0 16rpx 16rpx;
}
.category-section:not(.single-category) .section-block:not(:first-child) {
  border-radius: 16rpx;
}
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.section-stats {
  display: flex;
  align-items: center;
}
.stats-count {
  font-size: 26rpx;
  color: #bcbcbc;
  margin-right: 10rpx;
}
.expand-icon {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}
.subsection-list {
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0rpx 30rpx 20rpx;
}
.subsection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  width: 100%;
  background-color: #ececec;
  margin-top: 20rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
}
.subsection-item:last-child {
  border-bottom: none;
}
.subsection-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}
.arrow-icon {
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  transform: rotate(90deg);
}
/* 空状态样式 */
.empty-state {
  background: #fff;
  padding: 60rpx 30rpx;
  text-align: center;
  border-radius: 0 0 20rpx 20rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 企业信息解锁弹框样式 */
.unlock-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.unlock-dialog {
  position: relative;
  width: 540rpx;
  overflow: visible;
}
.dialog-close {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 54rpx;
  height: 54rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2961b8;
  border-radius: 50%;
  z-index: 10000;
  border: 2px solid #fff;
}
.close-icon {
  width: 32rpx;
  height: 32rpx;
}
.dialog-content {
  width: 100%;
  position: relative;
}
.dialog-background {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20rpx;
}
.unlock-button {
  position: absolute;
  bottom: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(180deg, #254d7d, #2861b5, #254d7d);
  border-radius: 50rpx;
  padding: 12rpx 80rpx;
  border: 3rpx solid #fff;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.unlock-button-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
}
/* 右侧悬浮按钮 */
.floating-buttons {
  position: fixed;
  right: 0rpx;
  top: 36%;
  transform: translateY(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx #c1c5d4;
}
.floating-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.floating-btn.question-btn {
  background: #023caa;
  color: white;
}
.floating-btn.demand-btn {
  background: #fad676;
  color: #333;
}
.floating-btn-text {
  font-size: 26rpx;
}
/* 联系人弹窗样式 - 与需求广场完全一致 */
.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
