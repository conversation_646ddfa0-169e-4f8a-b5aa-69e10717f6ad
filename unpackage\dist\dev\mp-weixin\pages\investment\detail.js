"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      investmentId: "",
      investmentDetail: {},
      loading: true,
      // 处理后的富文本内容
      processedDetailContent: "",
      // 处理后的详细介绍内容
      // 解析后的标签数组
      parsedTags: []
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/investment/detail.vue:110", "项目投资详情页面加载，参数:", options);
    if (options.projectData) {
      try {
        this.investmentDetail = JSON.parse(decodeURIComponent(options.projectData));
        common_vendor.index.__f__("log", "at pages/investment/detail.vue:117", "🏢 使用传递的项目数据:", this.investmentDetail);
        this.parsedTags = this.parseTags(this.investmentDetail.tags);
        this.processDetailContent();
        common_vendor.index.setNavigationBarTitle({
          title: this.investmentDetail.projectName || "项目详情"
        });
        this.loading = false;
        return;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/investment/detail.vue:133", "🏢 解析项目数据失败:", error);
      }
    }
    if (options.id) {
      this.investmentId = options.id;
      this.getInvestmentDetail();
    } else {
      common_vendor.index.__f__("error", "at pages/investment/detail.vue:142", "❌ 缺少项目ID参数");
      common_vendor.index.showToast({
        title: "缺少项目参数",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return `天大海棠 - ${this.investmentDetail.projectName || "项目投资详情"}`;
    },
    // 自定义分享路径
    getSharePath() {
      return `/pages/investment/detail?id=${this.investmentId}`;
    },
    // 自定义分享内容
    getShareContent() {
      return `项目投资详情分享 - ${this.investmentDetail.projectName || "项目"}`;
    },
    // 解析tags字符串为数组
    parseTags(tagsString) {
      if (!tagsString || typeof tagsString !== "string") {
        return [];
      }
      const trimmedTags = tagsString.trim();
      if (!trimmedTags) {
        return [];
      }
      if (trimmedTags.includes(",")) {
        return trimmedTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0);
      }
      return [trimmedTags];
    },
    // 获取项目投资详情
    async getInvestmentDetail() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/investment/detail.vue:201", "🏢 开始获取项目投资详情...");
        common_vendor.index.__f__("log", "at pages/investment/detail.vue:202", "🏢 项目ID:", this.investmentId);
        common_vendor.index.__f__("log", "at pages/investment/detail.vue:203", "🏢 API地址: GET /miniapp/investment/detail/" + this.investmentId);
        const response = await utils_request.request.get(`/miniapp/investment/detail/${this.investmentId}`);
        common_vendor.index.__f__("log", "at pages/investment/detail.vue:206", "🏢 API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.investmentDetail = response.data.data || {};
          common_vendor.index.__f__("log", "at pages/investment/detail.vue:210", "🏢 项目投资详情:", this.investmentDetail);
          this.parsedTags = this.parseTags(this.investmentDetail.tags);
          this.processDetailContent();
          common_vendor.index.setNavigationBarTitle({
            title: this.investmentDetail.projectName || "项目详情"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/investment/detail.vue:223", "🏢 获取项目投资详情失败:", response);
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "获取详情失败",
            icon: "none"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/investment/detail.vue:233", "🏢 获取项目投资详情异常:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } finally {
        this.loading = false;
      }
    },
    // 处理富文本内容
    processDetailContent() {
      let content = this.investmentDetail.detailContent || this.investmentDetail.briefIntroduction || "";
      common_vendor.index.__f__("log", "at pages/investment/detail.vue:251", "🏢 原始内容:", content);
      common_vendor.index.__f__("log", "at pages/investment/detail.vue:252", "🏢 投资详情数据:", this.investmentDetail);
      if (!content || content.trim() === "") {
        this.processedDetailContent = '<p style="color: #999; text-align: center; padding: 40rpx 0;">暂无详细介绍</p>';
        return;
      }
      if (!content.includes("<") && !content.includes(">")) {
        content = `<p>${content}</p>`;
      }
      content = utils_imageUtils.processHtmlImageUrls(content);
      this.processedDetailContent = content;
      common_vendor.index.__f__("log", "at pages/investment/detail.vue:268", "🏢 处理后的富文本内容:", this.processedDetailContent);
    },
    // 顶图加载错误处理
    onTopImageError() {
      common_vendor.index.__f__("log", "at pages/investment/detail.vue:273", "🏢 顶图加载失败");
    },
    // Logo加载错误处理
    onLogoError() {
      common_vendor.index.__f__("log", "at pages/investment/detail.vue:278", "🏢 项目Logo加载失败");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.investmentDetail.topImageUrl || $data.investmentDetail.coverImageUrl
  }, $data.investmentDetail.topImageUrl || $data.investmentDetail.coverImageUrl ? {
    c: $options.processServerImageUrl($data.investmentDetail.topImageUrl || $data.investmentDetail.coverImageUrl),
    d: common_vendor.o((...args) => $options.onTopImageError && $options.onTopImageError(...args))
  } : {}, {
    e: $options.processServerImageUrl($data.investmentDetail.coverImageUrl, $options.getImagePath("avatar.png")),
    f: common_vendor.o((...args) => $options.onLogoError && $options.onLogoError(...args)),
    g: common_vendor.t($data.investmentDetail.projectName || "暂无名称"),
    h: $data.investmentDetail.financingRound
  }, $data.investmentDetail.financingRound ? {
    i: common_vendor.t($data.investmentDetail.financingRound)
  } : {}, {
    j: $data.investmentDetail.industryName
  }, $data.investmentDetail.industryName ? {
    k: common_vendor.t($data.investmentDetail.industryName)
  } : {}, {
    l: $data.investmentDetail.region
  }, $data.investmentDetail.region ? {
    m: common_vendor.t($data.investmentDetail.region)
  } : {}, {
    n: $data.parsedTags && $data.parsedTags.length > 0
  }, $data.parsedTags && $data.parsedTags.length > 0 ? {
    o: common_vendor.f($data.parsedTags, (tag, tagIndex, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tagIndex
      };
    })
  } : {}, {
    p: $data.processedDetailContent,
    q: !$data.loading && ($data.investmentDetail.contactPerson && $data.investmentDetail.contactPerson.trim() || $data.investmentDetail.contactInfo && $data.investmentDetail.contactInfo.trim())
  }, !$data.loading && ($data.investmentDetail.contactPerson && $data.investmentDetail.contactPerson.trim() || $data.investmentDetail.contactInfo && $data.investmentDetail.contactInfo.trim()) ? common_vendor.e({
    r: $data.investmentDetail.contactPerson && $data.investmentDetail.contactPerson.trim()
  }, $data.investmentDetail.contactPerson && $data.investmentDetail.contactPerson.trim() ? {
    s: $options.getImagePath("intro-icon_s_1.png"),
    t: common_vendor.t($data.investmentDetail.contactPerson)
  } : {}, {
    v: $data.investmentDetail.contactInfo && $data.investmentDetail.contactInfo.trim()
  }, $data.investmentDetail.contactInfo && $data.investmentDetail.contactInfo.trim() ? {
    w: $options.getImagePath("intro-icon_s_2.png"),
    x: common_vendor.t($data.investmentDetail.contactInfo)
  } : {}) : {}));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b3297ecb"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/investment/detail.js.map
