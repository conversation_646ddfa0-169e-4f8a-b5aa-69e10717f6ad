.points-mall-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}
/* 每日签到区域 */
.daily-checkin-section {
  padding: 30rpx;
}
.checkin-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0rpx;
}
.checkin-title {
  font-size: 38rpx;
  font-weight: 700;
  color: #151e61;
}
.checkin-btn {
  background: #557db8;
  padding: 6rpx 16rpx;
  border-radius: 40rpx;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkin-btn.checked {
  background: #999;
  opacity: 0.8;
}
.checkin-btn.loading {
  background: #557db8;
  opacity: 0.7;
}
.checkin-btn-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}
.checkin-subtitle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.subtitle-left {
  display: flex;
  align-items: center;
  gap: 10rpx;
}
.checkin-subtitle {
  font-size: 26rpx;
  color: #666;
}
.reminder-switch {
  transform: scale(0.4);
  width: 30rpx;
}
.subtitle-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.remaining-text {
  font-size: 26rpx;
  color: #666;
}
.question-icon {
  width: 30rpx;
  height: 30rpx;
}
/* 年月切换器 */
.date-switcher {
  margin-bottom: 20rpx;
}
.date-switcher-container {
  display: flex;
  align-items: center;
}
.date-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  height: 30rpx;
  background-color: #1e4ba8;
  border-radius: 8rpx;
  cursor: pointer;
}
.date-arrow .arrow-text {
  color: #fff;
  font-size: 16rpx;
  font-weight: bold;
}
.date-display {
  text-align: center;
  margin: 0 15rpx 6rpx;
}
.date-text {
  font-size: 26rpx;
  color: #1e4ba8;
}
/* 签到日历 */
.checkin-calendar {
  margin-bottom: 40rpx;
}
.calendar-labels {
  display: flex;
  margin-bottom: 16rpx;
  gap: 6rpx;
}
.week-label {
  flex: 1;
  text-align: center;
  font-size: 22rpx;
  color: #999;
  font-weight: 400;
}
.calendar-days {
  display: flex;
  gap: 6rpx;
}
.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  border-radius: 12rpx;
  background-color: #f5f7fa;
  flex: 1;
  justify-content: center;
}
/* 已签到状态 - 浅蓝色 */
.calendar-day.checked {
  background-color: #d7e5ff;
}
/* 今日签到状态 - 深蓝色 */
.calendar-day.today {
  background-color: #023caa;
}
/* 补签状态 - 深蓝色 */
.calendar-day.supplement {
  background-color: #d7e5ff;
}
/* 待签状态 - 灰色 */
.calendar-day.waiting {
  background-color: #e8e8e8;
}
/* 可补签状态 - 蓝色背景 */
.calendar-day.can-makeup {
  background-color: #557db8;
  cursor: pointer;
}
/* 今日签到文字颜色 */
.calendar-day.today .day-number,
.calendar-day.today .day-status {
  color: #fff;
}
/* 补签文字颜色 */
.calendar-day.supplement .day-status {
  color: #fff;
  background-color: #023caa;
  padding: 2rpx 16rpx;
  border-radius: 100rpx;
}
/* 已签到文字颜色 */
.calendar-day.checked .day-number,
.calendar-day.checked .day-status {
  color: #7da0e7;
}
/* 可补签文字颜色 */
.calendar-day.can-makeup .day-number,
.calendar-day.can-makeup .day-status {
  color: #fff;
  font-weight: 600;
}
.day-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
  line-height: 1;
}
.day-status {
  font-size: 18rpx;
  color: #b3b3b3;
  font-weight: 400;
  text-align: center;
}
/* 展开日历样式 */
.expanded-calendar {
  position: relative;
}
.full-month-calendar {
  margin-top: 20rpx;
}
.calendar-week {
  display: flex;
  gap: 6rpx;
  margin-bottom: 12rpx;
}
.calendar-week:last-child {
  margin-bottom: 0;
}
.calendar-week .calendar-day {
  position: relative;
}
.calendar-day.empty {
  background-color: transparent;
  pointer-events: none;
}
.reward-icon {
  position: absolute;
  top: -12rpx;
  right: -10rpx;
  font-size: 20rpx;
}
.makeup-icon {
  position: absolute;
  top: -12rpx;
  right: -10rpx;
  font-size: 16rpx;
  color: #fff;
}
.checkin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.checkin-tip {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.monthly-reward {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.reward-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}
.arrow-icon {
  width: 28rpx;
  height: 28rpx;
}
/* 活动区域 */
.activities-section {
  padding: 0 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
}
.activity-card {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #333;
}
.challenge-card {
  background: linear-gradient(180deg, #fedada 0%, #ffffff 100%);
}
.task-card {
  background: linear-gradient(180deg, #dbe7ff 0%, #ffffff 100%);
}
.activity-content {
  margin-bottom: 24rpx;
}
.activity-content .flex {
  display: flex;
  align-items: center;
}
.activity-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}
.activity-time {
  font-size: 22rpx;
  margin-bottom: 16rpx;
  display: block;
}
.activity-reward,
.activity-task {
  font-size: 26rpx;
  margin-bottom: 8rpx;
  display: block;
}
.activity-more {
  font-size: 24rpx;
  display: block;
}
.activity-btn {
  width: 100%;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.red-btn {
  background: #dd2726;
}
.red-btn.disabled {
  background: #ccc;
  opacity: 0.6;
  pointer-events: none;
}
.blue-btn {
  background: #023caa;
}
.btn-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
}
/* 邀请注册区域 */
.invite-section {
  padding: 0 30rpx 30rpx;
}
.invite-card-image {
  width: 100%;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  border-radius: 20rpx;
  overflow: hidden;
}
/* 积分兑换区域 */
.exchange-block {
  position: relative;
  margin: 30rpx 0;
}
.exchange-content {
  padding: 0 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.invite-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}
.gift-image {
  width: 100%;
  height: 100%;
}
.invite-content {
  flex: 1;
}
.invite-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.invite-reward {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.highlight-red {
  color: #f54b4c;
  font-weight: 700;
  margin: 0 2rpx;
}
.highlight-blue {
  color: #77a0ef;
  font-weight: 700;
  margin: 0 2rpx;
}
.invite-subtitle {
  font-size: 22rpx;
  color: #999;
  display: block;
}
/* 积分兑换区域 */
.exchange-block {
  margin: 0rpx 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  position: relative;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.exchange-block .list_bg {
  width: 100%;
  position: absolute;
  top: -4rpx;
  left: 0;
  z-index: 16;
  border-radius: 20rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  position: relative;
}
.section-title {
  font-size: 36rpx;
  font-weight: 900;
  color: #003399;
  margin-left: 10rpx;
  z-index: 100;
}
.category-tabs {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.category-tab {
  padding: 10rpx 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}
.category-tab.active {
  background: #557db8;
  color: #fff;
}
.goods-section {
  margin-top: 30rpx;
}
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}
.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.back-to-all {
  font-size: 26rpx;
  color: #557db8;
  cursor: pointer;
}
.goods-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
}
.goods-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  cursor: pointer;
}
.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.goods-image-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}
.placeholder-text {
  font-size: 24rpx;
  color: #999;
}
.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.goods-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goods-points {
  font-size: 30rpx;
  font-weight: 600;
  color: #dd2726;
}
.goods-stock {
  font-size: 24rpx;
  color: #999;
}
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.more-link {
  font-size: 28rpx;
  color: #003399;
  margin-right: 10rpx;
  z-index: 100;
}
.exchange-content {
  position: relative;
  z-index: 20;
  padding: 30rpx;
}
.card-image {
  width: 100%;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  border-radius: 20rpx;
  overflow: hidden;
}
.card-header {
  margin-bottom: 20rpx;
}
.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.consulting-card .card-title {
  color: white;
}
.card-body {
  flex: 1;
}
.service-grid,
.gift-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx 40rpx;
}
.service-item,
.gift-item {
  font-size: 28rpx;
  line-height: 1.4;
}
.consulting-card .service-item {
  color: rgba(255, 255, 255, 0.9);
}
.gift-card .gift-item {
  color: #666;
}
.card-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}
.more-btn {
  padding: 12rpx 24rpx;
  border-radius: 25rpx;
}
.consulting-card .more-btn {
  background: rgba(255, 255, 255, 0.9);
}
.gift-card .more-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid #e0e0e0;
}
.more-btn-text {
  font-size: 26rpx;
  font-weight: 500;
}
.consulting-card .more-btn-text {
  color: #1a237e;
}
.gift-card .more-btn-text {
  color: #666;
}
/* 任务弹窗样式 */
.task-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}
.task-modal-container {
  position: relative;
  width: 100%;
  background-color: #dde8fe;
  border-radius: 40rpx 40rpx 0 0;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}
@keyframes slideUp {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.task-modal-header {
  display: flex;
  justify-content: center;
}
.task-modal-header image {
  width: 60%;
}
.task-modal-title-img {
  width: 300rpx;
  height: auto;
}
.task-modal-close {
  position: absolute;
  top: -80rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-icon {
  width: 40rpx;
  height: 40rpx;
}
.task-list {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.task-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.task-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.task-icon-img {
  width: 100%;
  height: 100%;
}
.task-content {
  flex: 1;
  margin-right: 20rpx;
}
.task-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.task-desc {
  font-size: 24rpx;
  color: #9e9e9e;
  line-height: 1.4;
  display: block;
}
.task-progress {
  margin-top: 8rpx;
}
.progress-text {
  font-size: 24rpx;
  color: #4a90e2;
}
.task-btn {
  padding: 6rpx 32rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(180deg, #1a48ab 0%, #6094dd 100%);
}
.task-btn-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 800;
}
/* 补签说明弹框样式 */
.makeup-explain-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.makeup-explain-dialog {
  position: relative;
  width: 600rpx;
  max-width: 90%;
}
.makeup-explain-bg {
  width: 100%;
  display: block;
}
.makeup-explain-btn {
  position: absolute;
  bottom: 35rpx;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 70rpx);
  height: 65rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 40rpx;
  border: none;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.3);
}
.makeup-explain-btn:active {
  transform: translateX(-50%) scale(0.95);
}
.makeup-explain-close {
  position: absolute;
  bottom: -85rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-circle {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
}
.close-icon {
  width: 35rpx;
  height: 35rpx;
}
/* 任务列表加载和空状态样式 */
.task-loading,
.task-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  color: #999;
  font-size: 28rpx;
}
