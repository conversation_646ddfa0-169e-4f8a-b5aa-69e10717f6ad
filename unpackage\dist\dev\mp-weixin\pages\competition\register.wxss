.register-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.header-section {
  position: relative;
}
/* 顶图容器样式 */
.header-image-container {
  width: 100%;
  position: relative;
}
.header-image {
  width: 100%;
  display: block;
  min-height: 300rpx;
  object-fit: cover;
}
/* 默认头部内容样式 */
.header-content {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  padding: 60rpx 40rpx 80rpx 40rpx;
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  letter-spacing: 2rpx;
}
.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}
.form-card {
  position: relative;
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.card-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  margin-left: 6rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.form-label .required-star {
  color: #ff4757 !important;
  margin-right: 4rpx;
  font-weight: bold;
}
.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.input-container .form-input,
.input-container picker,
.input-container .picker-input,
.input-container .form-textarea,
.input-container .image-upload-container {
  flex: 1;
  width: 0;
  min-width: 0;
}
.form-required {
  font-size: 24rpx;
  color: #ff0000;
  font-weight: normal;
  white-space: nowrap;
}
.form-optional {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  white-space: nowrap;
}
.form-input {
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  width: 100% !important;
}
.form-input::-webkit-input-placeholder {
  color: #999;
}
.form-input::placeholder {
  color: #999;
}
.form-textarea {
  min-height: 160rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
}
picker {
  width: 100% !important;
}
.picker-input {
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100% !important;
}
.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.picker-text.placeholder {
  color: #999;
}
.picker-arrow {
  font-size: 24rpx;
  color: #999;
}
.image-upload-container {
  width: 100%;
  position: relative;
}
.image-upload-area {
  width: 100%;
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  position: relative;
  box-sizing: border-box;
}
.upload-btn {
  background: #E9ECEF;
  border: none;
  border-radius: 8rpx;
  padding: 0rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 24rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-hint {
  font-size: 28rpx;
  color: #999;
  flex: 1;
}
.upload-status {
  position: absolute;
  top: 50%;
  right: 24rpx;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #52c41a;
  font-weight: bold;
}
.file-upload {
  width: 100%;
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
}
.upload-btn {
  background: #E9ECEF;
  border: none;
  border-radius: 8rpx;
  padding: 0rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 24rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-text {
  font-size: 28rpx;
  color: #999;
  flex: 1;
}
.sponsor-section {
  padding: 30rpx;
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  text-align: center;
}
.sponsor-image {
  width: calc(100% - 60rpx);
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.sponsor-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.sponsor-fallback {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}
.fallback-text {
  font-size: 28rpx;
  color: #ccc;
}
.submit-section {
  padding: 30rpx;
  margin: 30rpx;
  margin-top: 20rpx;
  border-radius: 20rpx;
}
.submit-btn {
  width: 80%;
  height: 80rpx;
  background: linear-gradient(90deg, #6192ee 0%, #4a7de8 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn:active {
  opacity: 0.8;
}
.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
.picker-input.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
.form-textarea.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
/* 动态表单样式 */
.radio-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.radio-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  cursor: pointer;
}
.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid #ddd;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.radio-circle.radio-selected {
  border-color: #007aff;
  background-color: #007aff;
}
.radio-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: white;
  border-radius: 50%;
}
.radio-text {
  font-size: 32rpx;
  color: #333;
}
.radio-text-disabled {
  color: #ccc !important;
}
.radio-item.radio-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.radio-circle.radio-disabled {
  border-color: #ddd !important;
  background-color: #f5f5f5 !important;
}
.select-other-container {
  width: 100%;
}
.other-input {
  margin-top: 20rpx;
  width: 100%;
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.other-input::-webkit-input-placeholder {
  color: #999;
  font-size: 28rpx;
}
.other-input::placeholder {
  color: #999;
  font-size: 28rpx;
}
.image-upload-container {
  width: 100%;
}
