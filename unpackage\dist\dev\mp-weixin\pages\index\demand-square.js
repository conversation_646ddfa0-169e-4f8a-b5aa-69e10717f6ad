"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      selectedCategory: "",
      selectedCategoryIndex: 0,
      // 选中的分类索引
      selectedStatus: "",
      // 选择的状态筛选
      selectedStatusIndex: 0,
      // 选中的状态索引
      selectedTimeRange: "",
      // 选择的时间筛选
      selectedTimeIndex: 0,
      // 选中的时间索引
      demandCategories: [],
      // 动态获取的需求类型列表
      // 固定的图标映射，按顺序对应8个位置
      iconMapping: [],
      // 状态选项
      statusOptions: [
        { label: "全部", value: "" },
        { label: "已发布", value: "1" },
        { label: "已对接", value: "2" }
      ],
      // 时间选项
      timeOptions: [
        { label: "时间", value: "" },
        { label: "一周内", value: "week_within" },
        { label: "满一周", value: "week_over" },
        { label: "满一月", value: "month_over" },
        { label: "满一年", value: "year_over" }
      ],
      demandList: [],
      // 动态获取的需求列表
      isLoading: false,
      // 加载状态
      showContactPopup: false,
      // 是否显示联系人弹窗
      contactInfo: {
        // 联系人信息
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  computed: {
    // 计算显示的分类数据，最多显示8个
    displayCategories() {
      return this.demandCategories.slice(0, 8).map((category, index) => ({
        ...category,
        iconPath: utils_imageUtils.processServerImageUrl(category.icon, this.iconMapping[index] || utils_imageUtils.getImagePath("icon8.png")),
        // 处理后台返回的图标路径
        displayName: this.formatCategoryName(category.name)
        // 格式化显示名称
      }));
    },
    // 分类筛选选项
    categoryPickerOptions() {
      const options = [{ label: "全部类型", value: "" }];
      if (this.demandCategories && this.demandCategories.length > 0) {
        this.demandCategories.forEach((category) => {
          options.push({
            label: category.name,
            value: category.id.toString()
          });
        });
      }
      return options;
    },
    // 当前选中的分类标签
    currentCategoryLabel() {
      var _a;
      return ((_a = this.categoryPickerOptions[this.selectedCategoryIndex]) == null ? void 0 : _a.label) || "全部类型";
    },
    // 当前选中的时间标签
    currentTimeLabel() {
      var _a;
      return ((_a = this.timeOptions[this.selectedTimeIndex]) == null ? void 0 : _a.label) || "时间";
    },
    // 当前选中的状态标签
    currentStatusLabel() {
      var _a;
      return ((_a = this.statusOptions[this.selectedStatusIndex]) == null ? void 0 : _a.label) || "状态";
    }
  },
  onLoad() {
    this.loadDemandCategories();
    this.loadDemandList();
  },
  onShow() {
    const shouldRefresh = common_vendor.index.getStorageSync("shouldRefreshDemandList");
    const updatedDemandInfo = common_vendor.index.getStorageSync("updatedDemandInfo");
    if (shouldRefresh) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:268", "📋 🔄 检测到需要刷新需求列表，重新加载数据");
      this.loadDemandList();
      common_vendor.index.removeStorageSync("shouldRefreshDemandList");
    } else if (updatedDemandInfo) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:274", "📋 🔄 更新特定需求的浏览量:", updatedDemandInfo);
      this.updateDemandViewCount(updatedDemandInfo.demandId, updatedDemandInfo.newViewCount);
      common_vendor.index.removeStorageSync("updatedDemandInfo");
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 需求广场";
    },
    // 自定义分享内容
    getShareContent() {
      return "需求广场分享";
    },
    // 检查功能权限
    async checkPermission() {
      return await utils_profileCheck.checkFunctionPermission(false);
    },
    // 分类筛选改变事件
    onCategoryChange(e) {
      this.selectedCategoryIndex = e.detail.value;
      const selectedOption = this.categoryPickerOptions[this.selectedCategoryIndex];
      this.selectedCategory = selectedOption.value;
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:305", "选择分类筛选:", selectedOption.label, "ID:", selectedOption.value);
      this.loadDemandList();
    },
    // 时间筛选改变事件
    onTimeChange(e) {
      this.selectedTimeIndex = e.detail.value;
      const selectedOption = this.timeOptions[this.selectedTimeIndex];
      this.selectedTimeRange = selectedOption.value;
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:315", "选择时间筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadDemandList();
    },
    // 状态筛选改变事件
    onStatusChange(e) {
      this.selectedStatusIndex = e.detail.value;
      const selectedOption = this.statusOptions[this.selectedStatusIndex];
      this.selectedStatus = selectedOption.value;
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:325", "选择状态筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadDemandList();
    },
    // 我有疑问 - 与需求分类详情页功能一致
    async askQuestion() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:333", "📋 问题咨询");
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:348", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:365", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:371", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "需求咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/index/demand-square.vue:384", "📋 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        success: () => {
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:419", "拨打电话成功:", phoneNumber);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/demand-square.vue:422", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    },
    // 长按二维码扫码
    scanQRCode() {
      if (!this.contactInfo.qrCodeUrl) {
        common_vendor.index.showToast({
          title: "暂无二维码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.previewImage({
        urls: [this.contactInfo.qrCodeUrl],
        current: this.contactInfo.qrCodeUrl,
        success: () => {
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:446", "预览二维码成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/demand-square.vue:449", "预览二维码失败:", err);
          common_vendor.index.showToast({
            title: "预览失败",
            icon: "none"
          });
        }
      });
    },
    // 我有需求 - 跳转到需求类型选择页面
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:460", "我有需求 - 跳转到类型选择页面");
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/demand/type-select"
      });
    },
    // 发布需求
    publishDemand(item) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:475", "发布需求:", item);
      common_vendor.index.showToast({
        title: "发布功能开发中",
        icon: "none"
      });
    },
    // 获取需求类型列表
    async loadDemandCategories() {
      try {
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:485", "🏷️ 开始获取需求类型列表...");
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:486", "🏷️ API地址: POST /miniapp/demandcategory/app/getEnabledList");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:489", "🏷️ 需求类型列表完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const categoryData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:493", "🏷️ 原始需求类型数据:", categoryData);
          const uniqueCategories = [];
          const seenNames = /* @__PURE__ */ new Set();
          categoryData.forEach((item) => {
            const categoryName = item.categoryName || item.name || "未知类型";
            if (!seenNames.has(categoryName)) {
              seenNames.add(categoryName);
              uniqueCategories.push({
                id: item.categoryId || item.id,
                name: categoryName,
                shortName: item.categoryShortName,
                // 简写名称
                code: item.categoryCode || item.code,
                icon: item.categoryIcon || item.iconUrl || "",
                // 使用后台返回的图标路径
                sortOrder: item.sortOrder || 0,
                status: item.status
              });
            }
          });
          this.demandCategories = uniqueCategories;
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:517", "🏷️ 需求类型列表获取成功，共", this.demandCategories.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:518", "🏷️ 处理后的需求类型数据:", this.demandCategories);
        } else {
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:520", "🏷️ ❌ 需求类型数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:521", "🏷️ 响应详情:", response);
          this.demandCategories = [];
          if (response && response.message) {
            common_vendor.index.showToast({
              title: response.message,
              icon: "none",
              duration: 2e3
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/demand-square.vue:534", "🏷️ 获取需求类型列表失败:", error);
        this.demandCategories = [];
        common_vendor.index.showToast({
          title: "需求类型加载失败",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 加载需求列表
    async loadDemandList() {
      try {
        this.isLoading = true;
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:549", "📋 开始获取需求列表...");
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:550", "📋 API地址: POST /miniapp/demand/list");
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:551", "📋 筛选条件:", {
          selectedCategory: Number(this.selectedCategory),
          selectedStatus: this.selectedStatus,
          demandStatusArray: this.getDemandStatusArray(),
          selectedTimeRange: this.selectedTimeRange
        });
        const requestData = {
          categoryId: this.selectedCategory || "",
          // 根据选择的分类设置
          demandStatusArray: this.getDemandStatusArray(),
          // 状态筛选数组
          timeFilter: this.selectedTimeRange || ""
          // 时间筛选
          // 可以根据需要添加其他筛选条件
        };
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:566", "📋 设置分类筛选 demandType:", requestData.demandType);
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:568", "📋 请求参数:", requestData);
        const response = await utils_request.request.post("/miniapp/demand/list", requestData);
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:571", "📋 需求列表完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const demandData = response.data.rows || [];
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:575", "📋 原始需求列表数据:", demandData);
          this.demandList = demandData.map((item) => ({
            id: item.demandId || item.id,
            category: this.getCategoryShortName(item.categoryName) || item.categoryName || "未知类型",
            // 使用简写名称
            categoryCode: item.categoryCode,
            // 获取分类代码
            categoryClass: this.getCategoryClassByCode(item.categoryCode),
            // 根据代码获取样式
            title: item.demandTitle || item.title || "无标题",
            description: item.demandDesc || "暂无描述",
            date: this.formatDate(item.createTime),
            status: this.formatDemandStatus(item.demandStatus),
            contactName: item.contactName,
            contactPhone: item.contactPhone,
            viewCount: item.viewCount || 0,
            isTop: item.isTop,
            rawData: item
            // 保存原始数据，用于详情页
          }));
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:594", "📋 需求列表获取成功，共", this.demandList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:595", "📋 处理后的需求列表数据:", this.demandList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:597", "📋 ❌ 需求列表数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/demand-square.vue:598", "📋 响应详情:", response);
          this.demandList = [];
          if (response && response.message) {
            common_vendor.index.showToast({
              title: response.message,
              icon: "none",
              duration: 2e3
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/demand-square.vue:611", "📋 获取需求列表失败:", error);
        this.demandList = [];
        common_vendor.index.showToast({
          title: "需求列表加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 导航到首页
    navigateToHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    // 导航到人脉资源
    navigateToContacts() {
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts-new"
      });
    },
    // 导航到产业资源
    navigateToIndustry() {
      common_vendor.index.reLaunch({
        url: "/pages/industry/index"
      });
    },
    // 导航到我的
    navigateToMine() {
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 更新特定需求的浏览量
    updateDemandViewCount(demandId, newViewCount) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:656", "📋 🔄 更新需求浏览量, demandId:", demandId, "newViewCount:", newViewCount);
      const demandIndex = this.demandList.findIndex(
        (item) => item.id && item.id == demandId || item.rawData && (item.rawData.demandId == demandId || item.rawData.id == demandId)
      );
      if (demandIndex !== -1) {
        this.demandList[demandIndex].viewCount = newViewCount;
        if (this.demandList[demandIndex].rawData) {
          this.demandList[demandIndex].rawData.viewCount = newViewCount;
        }
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:671", "📋 🔄 浏览量更新成功, 新浏览量:", newViewCount);
      } else {
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:673", "📋 🔄 未找到对应的需求项，demandId:", demandId);
      }
    },
    // 跳转到需求详情页
    async goToDemandDetail(item) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:679", "跳转到需求详情页:", item);
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      const data = encodeURIComponent(JSON.stringify({
        demandId: item.rawData.demandId || item.rawData.id,
        demandTitle: item.rawData.demandTitle || item.rawData.title,
        title: item.title,
        categoryId: item.rawData.categoryId,
        categoryName: item.rawData.categoryName || item.category,
        contactName: item.rawData.contactName,
        contactPhone: item.rawData.contactPhone,
        createTime: item.rawData.createTime,
        demandDesc: item.rawData.demandDesc || item.description,
        demandStatus: item.rawData.demandStatus || item.status,
        demandType: item.rawData.demandType,
        formData: item.rawData.formData,
        // 从rawData中获取formData
        isTop: item.rawData.isTop,
        remark: item.rawData.remark,
        status: item.rawData.status,
        updateTime: item.rawData.updateTime,
        userId: item.rawData.userId,
        viewCount: item.rawData.viewCount || 0
      }));
      common_vendor.index.navigateTo({
        url: `/pages/demand/detail?data=${data}`
      });
    },
    // 跳转到需求分类详情页
    async goToCategoryDetail(category) {
      common_vendor.index.__f__("log", "at pages/index/demand-square.vue:716", "跳转到需求分类详情页:", category);
      const hasPermission = await this.checkPermission();
      if (!hasPermission) {
        return;
      }
      if (category.code === "scenario") {
        common_vendor.index.__f__("log", "at pages/index/demand-square.vue:726", "🎯 跳转到产业资源页面");
        common_vendor.index.navigateTo({
          url: "/pages/industry/index"
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/demand/category-detail?id=${category.id}&name=${encodeURIComponent(category.name || category.displayName)}`
        });
      }
    },
    // 格式化分类名称，在每个字符间添加空格
    formatCategoryName(name) {
      if (!name)
        return "";
      if (name.length === 2) {
        return name.charAt(0) + " " + name.charAt(1);
      }
      return name;
    },
    // 根据分类ID获取分类名称
    getCategoryNameById(categoryId) {
      if (!categoryId || !this.demandCategories.length)
        return "";
      const category = this.demandCategories.find((cat) => cat.id.toString() === categoryId.toString());
      return category ? category.name : "";
    },
    // 根据分类代码获取样式类名
    getCategoryClassByCode(categoryCode) {
      const codeClassMap = {
        "financing": "financing-category",
        // 融资对接 - 蓝色
        "technology": "technology-category",
        // 技术合作 - 绿色
        "tech": "technology-category",
        "scenario": "scenario-category",
        // 资源场景 - 紫色
        "qualification": "qualification-category",
        // 政策资质 - 橙色
        "office": "office-category",
        // 载体厂房 - 粉色
        "factory": "office-category",
        "exposure": "exposure-category",
        // 曝光 - 青色
        "consult": "factory-category",
        // 管理咨询 - 灰色
        "other": "consult-category"
        // 其他需求 - 灰色
      };
      return codeClassMap[categoryCode] || "default-category";
    },
    // 根据分类名称获取简写名称
    getCategoryShortName(categoryName) {
      if (!categoryName || !this.demandCategories.length)
        return "";
      const category = this.demandCategories.find((cat) => cat.name === categoryName);
      return category ? category.shortName : "";
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 格式化需求状态
    formatDemandStatus(status) {
      const statusMap = {
        "0": "已发布",
        "1": "已对接",
        "2": "已下架"
      };
      return statusMap[status] || "未知状态";
    },
    // 格式化需求状态文本
    formatDemandStatus(status) {
      const statusMap = {
        "0": "待审核",
        "1": "已发布",
        "2": "已对接",
        "3": "已下架",
        "4": "审核拒绝"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类 - 与详情页面保持一致
    getStatusClass(status) {
      const statusClassMap = {
        "0": "status-pending",
        // 待审核 - 橙色
        "1": "status-published",
        // 已发布 - 绿色
        "2": "status-docked",
        // 已对接 - 蓝色
        "3": "status-offline",
        // 已下架 - 灰色
        "4": "status-rejected"
        // 审核拒绝 - 红色
      };
      return statusClassMap[status] || "status-unknown";
    },
    // 获取需求状态数组
    getDemandStatusArray() {
      if (this.selectedStatus === "") {
        return ["1", "2"];
      } else if (this.selectedStatus === "1") {
        return ["1"];
      } else if (this.selectedStatus === "2") {
        return ["2"];
      }
      return ["1", "2"];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($options.displayCategories, (category, index, i0) => {
      return {
        a: category.iconPath,
        b: common_vendor.n(category.iconClass),
        c: common_vendor.t(category.displayName),
        d: category.id || index,
        e: common_vendor.o(($event) => $options.goToCategoryDetail(category), category.id || index)
      };
    }),
    b: common_vendor.t($options.currentCategoryLabel),
    c: $data.selectedCategoryIndex,
    d: $options.categoryPickerOptions,
    e: common_vendor.o((...args) => $options.onCategoryChange && $options.onCategoryChange(...args)),
    f: common_vendor.t($options.currentTimeLabel),
    g: $data.selectedTimeIndex,
    h: $data.timeOptions,
    i: common_vendor.o((...args) => $options.onTimeChange && $options.onTimeChange(...args)),
    j: common_vendor.t($options.currentStatusLabel),
    k: $data.selectedStatusIndex,
    l: $data.statusOptions,
    m: common_vendor.o((...args) => $options.onStatusChange && $options.onStatusChange(...args)),
    n: $data.isLoading
  }, $data.isLoading ? {} : $data.demandList.length === 0 ? {} : {
    p: common_vendor.f($data.demandList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.category),
        b: common_vendor.n(item.categoryClass),
        c: item.isTop === "1"
      }, item.isTop === "1" ? {} : {}, {
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.description),
        f: common_vendor.t(item.date),
        g: common_vendor.t(item.viewCount || 0),
        h: item.status
      }, item.status ? {
        i: common_vendor.t(item.status),
        j: common_vendor.n($options.getStatusClass(item.rawData.demandStatus))
      } : {}, {
        k: index,
        l: common_vendor.o(($event) => $options.goToDemandDetail(item), index)
      });
    })
  }, {
    o: $data.demandList.length === 0,
    q: common_vendor.o((...args) => $options.askQuestion && $options.askQuestion(...args)),
    r: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    s: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    t: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    v: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    w: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    x: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    y: $data.contactInfo.qrCodeUrl,
    z: common_vendor.o((...args) => $options.scanQRCode && $options.scanQRCode(...args))
  } : {}, {
    A: common_vendor.o(() => {
    }),
    B: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {}, {
    C: $options.getImagePath("bottom_order_icon1.png"),
    D: common_vendor.o((...args) => $options.navigateToHome && $options.navigateToHome(...args)),
    E: $options.getImagePath("bottom_order_icon2.png"),
    F: common_vendor.o((...args) => $options.navigateToContacts && $options.navigateToContacts(...args)),
    G: $options.getImagePath("bottom_order_icon.png"),
    H: $options.getImagePath("bottom_order_icon3.png"),
    I: common_vendor.o((...args) => $options.navigateToIndustry && $options.navigateToIndustry(...args)),
    J: $options.getImagePath("bottom_order_icon4.png"),
    K: common_vendor.o((...args) => $options.navigateToMine && $options.navigateToMine(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/demand-square.js.map
