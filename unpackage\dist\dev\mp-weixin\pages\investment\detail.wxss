
.investment-detail-container.data-v-b3297ecb {
	min-height: 100vh;
	background-color: #f3f8fe;
}
.loading-container.data-v-b3297ecb {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
}
.loading-text.data-v-b3297ecb {
	font-size: 32rpx;
	color: #666;
}
.investment-detail-content.data-v-b3297ecb {
	padding-bottom: 40rpx;
}

/* 顶部图片区域 */
.top-image-section.data-v-b3297ecb {
	width: 100%;
	position: relative;
}
.top-image.data-v-b3297ecb {
	width: 100%;
}
.top-image-placeholder.data-v-b3297ecb {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.placeholder-text.data-v-b3297ecb {
	color: #fff;
	font-size: 32rpx;
}

/* 项目信息卡片 - 遮罩效果 */
.founder-card.data-v-b3297ecb {
	background: rgba(1,1,1, 0.3);
	position: absolute;
	z-index: 10;
	left: 0;
	bottom: 10rpx;
	width: 100%;
}
.founder-info.data-v-b3297ecb {
	padding: 10rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.founder-avatar-container.data-v-b3297ecb {
	margin-right: 30rpx;
}
.founder-avatar.data-v-b3297ecb {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
}
.founder-basic-info.data-v-b3297ecb {
	flex: 1;
}
.founder-name.data-v-b3297ecb {
	font-size: 26rpx;
	font-weight: bold;
	color: #fff;
	display: block;
	margin-bottom: 5rpx;
}
.tags-container.data-v-b3297ecb {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
	margin-top: 8rpx;
}
.tag.data-v-b3297ecb {
	background-color: #cad9f8;
	color: #000;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

/* 内容区域 */
.content-section.data-v-b3297ecb {
	background: #ffffff;
	padding: 30rpx;
	box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.content-section-son.data-v-b3297ecb {
	margin: 10rpx 0 70rpx;
}
.section-title.data-v-b3297ecb {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}
.section-content.data-v-b3297ecb {
	line-height: 1.8;
	overflow: hidden;
	max-width: 100%;
}

/* 富文本内容样式优化 */
.rich-text-content.data-v-b3297ecb {
	width: 100% !important;
	max-width: 100% !important;
	overflow: hidden !important;
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	text-align: justify;
	word-wrap: break-word;
	word-break: break-all;
}

/* 富文本内部图片样式限制 */
.rich-text-content.data-v-b3297ecb img {
	max-width: 100% !important;
	width: auto !important;
	height: auto !important;
	display: block !important;
	box-sizing: border-box !important;
	margin: 10rpx 0 !important;
}

/* 联系信息区域 */
.contact-section.data-v-b3297ecb {
	background: #ffffff;
	margin-top: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.contact-title.data-v-b3297ecb {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}
.contact-item.data-v-b3297ecb {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1px solid #f0f0f0;
}
.contact-item.data-v-b3297ecb:last-child {
	border-bottom: none;
	margin-bottom: 0;
}
.contact-icon.data-v-b3297ecb {
	width: 32rpx;
	height: 32rpx;
	margin-right: 15rpx;
	flex-shrink: 0;
}
.contact-text.data-v-b3297ecb {
	font-size: 28rpx;
	color: #666;
	flex: 1;
}
