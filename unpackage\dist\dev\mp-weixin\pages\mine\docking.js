"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      dockingList: [],
      loading: false
    };
  },
  onLoad() {
    this.loadMyDockingList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 我的对接";
    },
    // 自定义分享内容
    getShareContent() {
      return "我的对接分享";
    },
    // 获取当前用户信息
    async getCurrentUserInfo() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (userInfo) {
          return {
            userId: userInfo.userId || userInfo.id,
            userName: userInfo.userName || userInfo.name || userInfo.nickName,
            userPhone: userInfo.phone || userInfo.mobile || ""
          };
        }
        return null;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/docking.vue:81", "获取用户信息失败:", error);
        return null;
      }
    },
    // 获取我的对接列表
    async loadMyDockingList() {
      var _a;
      try {
        this.loading = true;
        const userInfo = await this.getCurrentUserInfo();
        if (!userInfo || !userInfo.userId) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }
        const response = await utils_request.request.get("/miniapp/demand/app/getMyDockingList");
        if (response && response.data && response.data.code === 200) {
          const dockingData = response.data.data.dockingList || [];
          this.dockingList = dockingData.map((item) => {
            const demandInfo = item.demandInfo || {};
            return {
              id: item.dockingId || item.demandId,
              title: item.demandTitle || demandInfo.demandTitle || "对接项目",
              description: demandInfo.demandDesc || "暂无描述",
              category: demandInfo.categoryShortName || "未分类",
              categoryClass: this.getCategoryClassByCode(demandInfo.categoryCode),
              date: this.formatDate(item.dockingTime || item.createTime),
              viewCount: demandInfo.viewCount || 0,
              status: "已对接",
              // 固定显示为已对接，不再判断status
              isTop: demandInfo.isTop || "0",
              rawData: item
              // 保存原始数据
            };
          });
        } else {
          this.dockingList = [];
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "获取对接列表失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        this.dockingList = [];
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 跳转到对接详情页面
    goToDockingDetail(item) {
      const rawData = item.rawData;
      const demandInfo = rawData.demandInfo || {};
      const detailData = {
        demandId: rawData.demandId || demandInfo.demandId,
        demandTitle: rawData.demandTitle || demandInfo.demandTitle,
        title: rawData.demandTitle || demandInfo.demandTitle,
        categoryId: demandInfo.categoryId,
        categoryName: demandInfo.categoryName,
        contactName: demandInfo.contactName,
        contactPhone: demandInfo.contactPhone,
        createTime: rawData.createTime,
        demandDesc: demandInfo.demandDesc,
        demandStatus: demandInfo.demandStatus,
        demandType: demandInfo.demandType,
        formData: demandInfo.formData,
        isTop: demandInfo.isTop,
        remark: demandInfo.remark,
        status: demandInfo.status,
        updateTime: demandInfo.updateTime,
        userId: demandInfo.userId,
        viewCount: demandInfo.viewCount || 0,
        isMyDocking: true
        // 标记这是从我的对接进入的
      };
      common_vendor.index.navigateTo({
        url: `/pages/demand/detail?data=${encodeURIComponent(JSON.stringify(detailData))}`
      });
    },
    // 根据分类代码获取样式类名
    getCategoryClassByCode(categoryCode) {
      const codeClassMap = {
        "financing": "financing-category",
        // 融资对接 - 蓝色
        "technology": "technology-category",
        // 技术合作 - 绿色
        "tech": "technology-category",
        "scenario": "scenario-category",
        // 资源场景 - 紫色
        "qualification": "qualification-category",
        // 政策资质 - 橙色
        "office": "office-category",
        // 载体厂房 - 粉色
        "factory": "office-category",
        "exposure": "exposure-category",
        // 曝光 - 青色
        "consult": "factory-category",
        // 管理咨询 - 灰色
        "other": "consult-category"
        // 其他需求 - 灰色
      };
      return codeClassMap[categoryCode] || "default-category";
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.dockingList.length === 0 ? {} : {
    c: common_vendor.f($data.dockingList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.category),
        b: common_vendor.n(item.categoryClass),
        c: item.isTop === "1"
      }, item.isTop === "1" ? {} : {}, {
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.description),
        f: common_vendor.t(item.date),
        g: common_vendor.t(item.viewCount || 0),
        h: item.status
      }, item.status ? {
        i: common_vendor.t(item.status)
      } : {}, {
        j: index,
        k: common_vendor.o(($event) => $options.goToDockingDetail(item), index)
      });
    })
  }, {
    b: $data.dockingList.length === 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/docking.js.map
