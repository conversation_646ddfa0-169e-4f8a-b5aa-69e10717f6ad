"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      publishList: [],
      loading: false
    };
  },
  onLoad() {
    this.loadMyPublishedDemands();
  },
  onShow() {
    const shouldRefresh = common_vendor.index.getStorageSync("shouldRefreshPublishList");
    if (shouldRefresh) {
      common_vendor.index.__f__("log", "at pages/mine/publish.vue:67", "📝 检测到需要刷新发布列表");
      common_vendor.index.removeStorageSync("shouldRefreshPublishList");
      this.loadMyPublishedDemands();
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 我的发布";
    },
    // 自定义分享内容
    getShareContent() {
      return "我的发布分享";
    },
    // 获取当前用户信息
    async getCurrentUserInfo() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (userInfo) {
          return {
            userId: userInfo.userId || userInfo.id,
            userName: userInfo.userName || userInfo.name || userInfo.nickName,
            userPhone: userInfo.phone || userInfo.mobile || ""
          };
        }
        return null;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/publish.vue:100", "获取用户信息失败:", error);
        return null;
      }
    },
    // 获取我的发布需求列表
    async loadMyPublishedDemands() {
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/mine/publish.vue:109", "📋 开始获取我的发布需求列表...");
        const userInfo = await this.getCurrentUserInfo();
        if (!userInfo || !userInfo.userId) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          this.loading = false;
          return;
        }
        common_vendor.index.__f__("log", "at pages/mine/publish.vue:122", "👤 当前用户信息:", userInfo);
        const response = await utils_request.request.post("/miniapp/demand/app/getMyPublishedDemands", userInfo.userId);
        common_vendor.index.__f__("log", "at pages/mine/publish.vue:126", "📋 我的发布需求API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const demandData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/mine/publish.vue:130", "📋 原始我的发布数据:", demandData);
          this.publishList = demandData.map((item) => ({
            id: item.demandId || item.id,
            category: item.categoryShortName || "未知类型",
            categoryClass: this.getCategoryClassByCode(item.categoryCode || item.code),
            title: item.demandTitle || item.title || "无标题",
            description: item.demandDesc || "暂无描述",
            date: this.formatDate(item.createTime),
            status: this.formatDemandStatus(item.demandStatus),
            contactName: item.contactName,
            contactPhone: item.contactPhone,
            viewCount: item.viewCount || 0,
            isTop: item.isTop,
            rawData: item,
            // 保存原始数据，用于详情页
            isMyPublish: true
            // 标记这是我的发布，用于详情页判断
          }));
          common_vendor.index.__f__("log", "at pages/mine/publish.vue:149", "📋 我的发布列表获取成功，共", this.publishList.length, "条数据");
        } else {
          common_vendor.index.__f__("log", "at pages/mine/publish.vue:151", "📋 ❌ 我的发布数据获取失败！");
          this.publishList = [];
          if (response && response.data && response.data.msg) {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/publish.vue:162", "📋 获取我的发布列表失败:", error);
        this.publishList = [];
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 跳转到需求详情页面
    goToDemandDetail(item) {
      common_vendor.index.__f__("log", "at pages/mine/publish.vue:175", "📋 跳转到需求详情，数据:", item);
      const detailData = {
        ...item.rawData,
        isMyPublish: true
        // 标记这是从我的发布进入的
      };
      common_vendor.index.navigateTo({
        url: `/pages/demand/detail?data=${encodeURIComponent(JSON.stringify(detailData))}`
      });
    },
    // 根据分类代码获取样式类名
    getCategoryClassByCode(categoryCode) {
      const codeClassMap = {
        "financing": "financing-category",
        // 融资对接 - 蓝色
        "technology": "technology-category",
        // 技术合作 - 绿色
        "tech": "technology-category",
        "scenario": "scenario-category",
        // 资源场景 - 紫色
        "qualification": "qualification-category",
        // 政策资质 - 橙色
        "office": "office-category",
        // 载体厂房 - 粉色
        "factory": "office-category",
        "exposure": "exposure-category",
        // 曝光 - 青色
        "consult": "factory-category",
        // 管理咨询 - 灰色
        "other": "consult-category"
        // 其他需求 - 灰色
      };
      return codeClassMap[categoryCode] || "default-category";
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 格式化需求状态 - 与需求广场保持一致
    formatDemandStatus(status) {
      const statusMap = {
        "0": "待审核",
        "1": "已发布",
        "2": "已对接",
        "3": "已下架",
        "4": "审核拒绝"
      };
      return statusMap[status] || "未知状态";
    },
    // 获取状态样式类 - 与详情页面保持一致
    getStatusClass(status) {
      const statusClassMap = {
        "0": "status-pending",
        // 待审核 - 橙色
        "1": "status-published",
        // 已发布 - 绿色
        "2": "status-docked",
        // 已对接 - 蓝色
        "3": "status-offline",
        // 已下架 - 灰色
        "4": "status-rejected"
        // 审核拒绝 - 红色
      };
      return statusClassMap[status] || "status-unknown";
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.publishList.length === 0 ? {} : {
    c: common_vendor.f($data.publishList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.category),
        b: common_vendor.n(item.categoryClass),
        c: item.isTop === "1"
      }, item.isTop === "1" ? {} : {}, {
        d: common_vendor.t(item.title),
        e: common_vendor.t(item.description),
        f: common_vendor.t(item.date),
        g: common_vendor.t(item.viewCount || 0),
        h: item.status
      }, item.status ? {
        i: common_vendor.t(item.status),
        j: common_vendor.n($options.getStatusClass(item.rawData.demandStatus))
      } : {}, {
        k: index,
        l: common_vendor.o(($event) => $options.goToDemandDetail(item), index)
      });
    })
  }, {
    b: $data.publishList.length === 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/publish.js.map
