"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
class ShareRewardManager {
  constructor() {
    this.isProcessingReward = false;
    this.lastShareTime = 0;
    this.shareThrottleTime = 3e3;
  }
  /**
   * 获取当前用户ID
   * @returns {number|null} 用户ID
   */
  getCurrentUserId() {
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        const userId = userInfo.userId || userInfo.id || userInfo.user_id || 0;
        common_vendor.index.__f__("log", "at utils/shareReward.js:25", "📤 获取到用户ID:", userId);
        return userId;
      }
      common_vendor.index.__f__("log", "at utils/shareReward.js:28", "📤 未找到用户信息");
      return null;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/shareReward.js:31", "📤 获取用户ID失败:", error);
      return null;
    }
  }
  /**
   * 调用分享积分奖励接口
   * @param {number} userId - 用户ID
   * @param {string} shareContent - 分享内容（可选）
   * @returns {Promise<boolean>} 是否成功获得积分
   */
  async callShareRewardApi(userId, shareContent = "") {
    var _a;
    try {
      common_vendor.index.__f__("log", "at utils/shareReward.js:44", "📤 调用分享积分奖励接口，参数:", { userId, shareContent });
      let url = `/miniapp/taskReward/share?userId=${userId}`;
      if (shareContent) {
        url += `&shareContent=${encodeURIComponent(shareContent)}`;
      }
      common_vendor.index.__f__("log", "at utils/shareReward.js:52", "📤 请求URL:", url);
      const response = await utils_request.request.post(url);
      common_vendor.index.__f__("log", "at utils/shareReward.js:56", "📤 分享积分奖励接口响应:", response);
      if (response && response.success && response.data && response.data.code === 200) {
        common_vendor.index.__f__("log", "at utils/shareReward.js:59", "✅ 分享积分奖励获取成功");
        const message = response.data.msg || response.data.message || "分享成功，获得积分奖励！";
        common_vendor.index.showToast({
          title: message,
          icon: "none",
          // 去掉固定的success图标
          duration: 3e3
          // 延长显示时间以便用户看清消息
        });
        return true;
      } else {
        common_vendor.index.__f__("log", "at utils/shareReward.js:71", "❌ 分享积分奖励获取失败:", ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || (response == null ? void 0 : response.message) || "未知错误");
        return false;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/shareReward.js:82", "📤 调用分享积分奖励接口失败:", error);
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "none"
      });
      return false;
    }
  }
  /**
   * 处理分享积分奖励
   * @param {string} shareContent - 分享内容（可选）
   * @returns {Promise<boolean>} 是否成功处理
   */
  async processShareReward(shareContent = "") {
    const now = Date.now();
    if (this.isProcessingReward || now - this.lastShareTime < this.shareThrottleTime) {
      common_vendor.index.__f__("log", "at utils/shareReward.js:103", "📤 分享奖励处理中或频率过高，跳过本次处理");
      return false;
    }
    this.isProcessingReward = true;
    this.lastShareTime = now;
    try {
      const userId = this.getCurrentUserId();
      if (!userId) {
        common_vendor.index.__f__("log", "at utils/shareReward.js:114", "📤 用户未登录，无法获取分享积分");
        return false;
      }
      const success = await this.callShareRewardApi(userId, shareContent);
      return success;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/shareReward.js:122", "📤 处理分享积分奖励失败:", error);
      return false;
    } finally {
      this.isProcessingReward = false;
    }
  }
  /**
   * 生成标准的分享配置对象
   * @param {Object} options - 分享配置选项
   * @param {string} options.title - 分享标题
   * @param {string} options.path - 分享路径
   * @param {string} options.imageUrl - 分享图片
   * @returns {Object} 分享配置对象
   */
  generateShareConfig(options = {}) {
    const {
      title = "天大海棠 - 创新创业服务平台",
      path = "/pages/index/home",
      imageUrl = "/static/share-logo.png"
    } = options;
    common_vendor.index.__f__("log", "at utils/shareReward.js:144", "📤 生成分享配置:", { title, path, imageUrl });
    return {
      title,
      path,
      imageUrl
    };
  }
  /**
   * 生成朋友圈分享配置对象
   * @param {Object} options - 分享配置选项
   * @param {string} options.title - 分享标题
   * @param {string} options.imageUrl - 分享图片
   * @returns {Object} 朋友圈分享配置对象
   */
  generateTimelineShareConfig(options = {}) {
    const {
      title = "天大海棠 - 创新创业服务平台",
      imageUrl = "/static/share-logo.png"
    } = options;
    common_vendor.index.__f__("log", "at utils/shareReward.js:166", "📤 生成朋友圈分享配置:", { title, imageUrl });
    return {
      title,
      imageUrl
    };
  }
  /**
   * 监听页面显示事件，检测是否从分享返回
   * 这是一种"假分享"检测方法，当用户点击分享按钮后返回页面时触发积分奖励
   */
  setupShareDetection() {
    let pageHideTime = 0;
    const onHide = () => {
      pageHideTime = Date.now();
      common_vendor.index.__f__("log", "at utils/shareReward.js:186", "📤 页面隐藏，记录时间:", pageHideTime);
    };
    const onShow = () => {
      const pageShowTime = Date.now();
      const hideDuration = pageShowTime - pageHideTime;
      common_vendor.index.__f__("log", "at utils/shareReward.js:194", "📤 页面显示，隐藏时长:", hideDuration);
      if (pageHideTime > 0 && hideDuration >= 2e3 && hideDuration <= 3e4) {
        common_vendor.index.__f__("log", "at utils/shareReward.js:198", "📤 检测到可能的分享操作，处理积分奖励");
        this.processShareReward("页面分享检测");
      }
      pageHideTime = 0;
    };
    return { onHide, onShow };
  }
}
const shareRewardManager = new ShareRewardManager();
exports.shareRewardManager = shareRewardManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/shareReward.js.map
