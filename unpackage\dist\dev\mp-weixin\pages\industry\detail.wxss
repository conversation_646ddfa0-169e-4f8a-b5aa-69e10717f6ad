.company-detail-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
.loading-text {
  font-size: 32rpx;
  color: #999;
}
/* 顶部图片区域 */
.top-image-section {
  width: 100%;
  position: relative;
}
.top-image {
  width: 100%;
}
.top-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.placeholder-text {
  color: #fff;
  font-size: 32rpx;
}
/* 企业信息遮罩 - 参考项目投资详情样式 */
.company-mask {
  background: rgba(1, 1, 1, 0.5);
  position: absolute;
  z-index: 10;
  left: 0;
  bottom: 10rpx;
  width: 100%;
}
.company-info {
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.company-basic-info {
  flex: 1;
}
.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  display: block;
}
/* 内容区域 */
.content-section {
  background: #ffffff;
  padding: 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 30rpx;
}
.tag {
  background-color: #cad9f8;
  color: #000;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.detail-section {
  margin-bottom: 30rpx;
}
.detail-section:last-child {
  margin-bottom: 0;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}
.contact-link {
  color: #0741ab;
  text-decoration: underline;
}
.description-content {
  line-height: 1.8;
  overflow: hidden;
  max-width: 100%;
}
.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}
/* 底部操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
}
.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-btn.primary {
  background-color: #0741ab;
}
.action-btn.secondary {
  background-color: #5cb3cc;
}
.btn-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}
/* 返回按钮 */
.back-button {
  position: fixed;
  top: 60rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
/* 右侧悬浮按钮 */
.floating-buttons {
  position: fixed;
  right: 0rpx;
  top: 36%;
  transform: translateY(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx #c1c5d4;
}
.floating-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.floating-btn.question-btn {
  background: #023caa;
  color: white;
}
.floating-btn.demand-btn {
  background: #fad676;
  color: #333;
}
.floating-btn-text {
  font-size: 26rpx;
}
/* 联系人弹窗样式 - 与需求广场完全一致 */
.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
