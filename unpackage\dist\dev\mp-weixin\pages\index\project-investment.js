"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      searchKeyword: "",
      // 筛选选项索引
      selectedRoundIndex: 0,
      selectedIndustryIndex: 0,
      selectedRegionIndex: 0,
      // 筛选选项数据
      roundOptions: [],
      industryOptions: [],
      regionOptions: [],
      projectsList: [],
      loading: false
    };
  },
  computed: {
    // 当前选中的融资轮次标签
    currentRoundLabel() {
      var _a;
      return ((_a = this.roundOptions[this.selectedRoundIndex]) == null ? void 0 : _a.label) || "融资轮次";
    },
    // 当前选中的行业标签
    currentIndustryLabel() {
      var _a;
      return ((_a = this.industryOptions[this.selectedIndustryIndex]) == null ? void 0 : _a.label) || "所属行业";
    },
    // 当前选中的地区标签
    currentRegionLabel() {
      var _a;
      return ((_a = this.regionOptions[this.selectedRegionIndex]) == null ? void 0 : _a.label) || "所在地区";
    }
  },
  onLoad() {
    this.initFilterData();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 项目投资";
    },
    // 自定义分享内容
    getShareContent() {
      return "项目投资分享";
    },
    // 解析tags字符串为数组
    parseTags(tagsString) {
      if (!tagsString || typeof tagsString !== "string") {
        return [];
      }
      const trimmedTags = tagsString.trim();
      if (!trimmedTags) {
        return [];
      }
      if (trimmedTags.includes(",")) {
        return trimmedTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0);
      }
      return [trimmedTags];
    },
    // 初始化筛选数据
    async initFilterData() {
      this.initRoundOptions();
      this.initRegionOptions();
      await this.loadIndustryOptions();
      this.loadProjectsList();
    },
    // 初始化融资轮次选项
    initRoundOptions() {
      this.roundOptions = [
        { label: "融资轮次", value: "" },
        { label: "未融资", value: "未融资" },
        { label: "A轮及以前", value: "A轮及以前" },
        { label: "B轮", value: "B轮" },
        { label: "C轮及以后", value: "C轮及以后" }
      ];
    },
    // 初始化地区选项（省份数据）
    initRegionOptions() {
      const provinces = [
        { name: "北京" },
        { name: "天津" },
        { name: "河北" },
        { name: "山西" },
        { name: "内蒙古" },
        { name: "辽宁" },
        { name: "吉林" },
        { name: "黑龙江" },
        { name: "上海" },
        { name: "江苏" },
        { name: "浙江" },
        { name: "安徽" },
        { name: "福建" },
        { name: "江西" },
        { name: "山东" },
        { name: "河南" },
        { name: "湖北" },
        { name: "湖南" },
        { name: "广东" },
        { name: "广西" },
        { name: "海南" },
        { name: "重庆" },
        { name: "四川" },
        { name: "贵州" },
        { name: "云南" },
        { name: "西藏" },
        { name: "陕西" },
        { name: "甘肃" },
        { name: "青海" },
        { name: "宁夏" },
        { name: "新疆" },
        { name: "台湾" },
        { name: "香港" },
        { name: "澳门" }
      ];
      const regionOptions = [{ label: "所在地区", value: "" }];
      provinces.forEach((province) => {
        regionOptions.push({
          label: province.name,
          value: province.name
        });
      });
      this.regionOptions = regionOptions;
    },
    // 加载行业选项（通过API获取一级行业数据）
    async loadIndustryOptions() {
      try {
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:245", "🏢 开始获取行业数据...");
        const response = await utils_request.request.get("/miniapp/industry/level/1");
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:247", "🏢 行业数据API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const industryData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/project-investment.vue:251", "🏢 原始行业数据:", industryData);
          const industryOptions = [{ label: "所属行业", value: "" }];
          industryData.forEach((industry) => {
            industryOptions.push({
              label: industry.nodeName,
              value: industry.id
            });
          });
          this.industryOptions = industryOptions;
          common_vendor.index.__f__("log", "at pages/index/project-investment.vue:263", "🏢 行业选项构建完成:", this.industryOptions);
        } else {
          common_vendor.index.__f__("log", "at pages/index/project-investment.vue:265", "🏢 ❌ 行业数据获取失败！");
          this.industryOptions = [{ label: "所属行业", value: "" }];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/project-investment.vue:270", "🏢 获取行业数据失败:", error);
        this.industryOptions = [{ label: "所属行业", value: "" }];
      }
    },
    handleSearch() {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:278", "搜索关键词:", this.searchKeyword);
      this.loadProjectsList();
    },
    // 融资轮次筛选改变事件
    onRoundChange(e) {
      this.selectedRoundIndex = e.detail.value;
      const selectedOption = this.roundOptions[this.selectedRoundIndex];
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:286", "选择融资轮次筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadProjectsList();
    },
    // 行业筛选改变事件
    onIndustryChange(e) {
      this.selectedIndustryIndex = e.detail.value;
      const selectedOption = this.industryOptions[this.selectedIndustryIndex];
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:294", "选择行业筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadProjectsList();
    },
    // 地区筛选改变事件
    onRegionChange(e) {
      this.selectedRegionIndex = e.detail.value;
      const selectedOption = this.regionOptions[this.selectedRegionIndex];
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:302", "选择地区筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadProjectsList();
    },
    // 加载项目列表
    async loadProjectsList() {
      var _a, _b, _c, _d;
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:310", "💼 开始获取项目投资列表...");
        const roundFilter = ((_a = this.roundOptions[this.selectedRoundIndex]) == null ? void 0 : _a.value) || "";
        const industryFilter = ((_b = this.industryOptions[this.selectedIndustryIndex]) == null ? void 0 : _b.value) || "";
        const regionFilter = ((_c = this.regionOptions[this.selectedRegionIndex]) == null ? void 0 : _c.value) || "";
        const params = {};
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim();
        }
        if (roundFilter) {
          params.financingRound = roundFilter;
        }
        if (industryFilter) {
          params.industryId = industryFilter;
        }
        if (regionFilter) {
          params.region = regionFilter;
        }
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:340", "💼 筛选条件:", params);
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:343", "💼 API地址: GET /miniapp/investment/enabled");
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:344", "💼 请求参数:", params);
        const response = await utils_request.request.get("/miniapp/investment/enabled", params);
        common_vendor.index.__f__("log", "at pages/index/project-investment.vue:346", "💼 API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const projectsData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/project-investment.vue:350", "💼 原始项目数据:", projectsData);
          this.projectsList = projectsData.map((project) => ({
            id: project.investmentId,
            name: project.projectName || "未知项目",
            round: project.financingRound || "未知轮次",
            industry: project.industryName || "未知行业",
            region: project.region || "未知地区",
            description: project.briefIntroduction || "暂无简介",
            logo: utils_imageUtils.processServerImageUrl(project.coverImageUrl, utils_imageUtils.getImagePath("avatar.png")),
            tags: project.tags || "",
            // 解析tags为数组
            parsedTags: this.parseTags(project.tags),
            // 保留原始数据以备详情页使用
            originalData: project
          }));
          common_vendor.index.__f__("log", "at pages/index/project-investment.vue:368", "💼 处理后的项目列表:", this.projectsList);
        } else {
          common_vendor.index.__f__("error", "at pages/index/project-investment.vue:370", "💼 API返回错误:", response);
          this.projectsList = [];
          common_vendor.index.showToast({
            title: ((_d = response == null ? void 0 : response.data) == null ? void 0 : _d.msg) || "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/project-investment.vue:379", "💼 获取项目列表失败:", error);
        this.projectsList = [];
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    viewProjectDetail(project) {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:391", "💼 查看项目详情:", project);
      const projectData = project.originalData || project;
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:395", "💼 传递的项目数据:", projectData);
      const projectDataStr = encodeURIComponent(JSON.stringify(projectData));
      common_vendor.index.navigateTo({
        url: `/pages/investment/detail?projectData=${projectDataStr}`
      });
    },
    navigateToHome() {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:405", "点击首页");
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    navigateToContacts() {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:412", "点击人脉资源");
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts-new"
      });
    },
    navigateToMine() {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:419", "点击我的");
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 导航到需求广场页面
    navigateToDemandSquare() {
      common_vendor.index.__f__("log", "at pages/index/project-investment.vue:427", "点击需求广场");
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.t($options.currentRoundLabel),
    f: $data.selectedRoundIndex,
    g: $data.roundOptions,
    h: common_vendor.o((...args) => $options.onRoundChange && $options.onRoundChange(...args)),
    i: common_vendor.t($options.currentIndustryLabel),
    j: $data.selectedIndustryIndex,
    k: $data.industryOptions,
    l: common_vendor.o((...args) => $options.onIndustryChange && $options.onIndustryChange(...args)),
    m: common_vendor.t($options.currentRegionLabel),
    n: $data.selectedRegionIndex,
    o: $data.regionOptions,
    p: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    q: $data.loading
  }, $data.loading ? {} : $data.projectsList.length === 0 ? {} : {
    s: common_vendor.f($data.projectsList, (project, index, i0) => {
      return common_vendor.e({
        a: project.logo || $options.getImagePath("avatar.png"),
        b: common_vendor.t(project.name),
        c: common_vendor.t(project.round),
        d: common_vendor.t(project.industry),
        e: common_vendor.t(project.region),
        f: project.parsedTags && project.parsedTags.length > 0
      }, project.parsedTags && project.parsedTags.length > 0 ? {
        g: common_vendor.f(project.parsedTags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        h: common_vendor.t(project.description),
        i: index,
        j: common_vendor.o(($event) => $options.viewProjectDetail(project), index)
      });
    })
  }, {
    r: $data.projectsList.length === 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/project-investment.js.map
