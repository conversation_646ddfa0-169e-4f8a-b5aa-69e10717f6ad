"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      industryList: []
      // 所有一级行业数据
    };
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 进入产业详情
    goToIndustryDetail(industry) {
      common_vendor.index.__f__("log", "at pages/industry/industry-list.vue:46", "进入产业详情:", industry);
      common_vendor.index.navigateTo({
        url: `/pages/industry/map?industryName=${encodeURIComponent(industry.name)}&industryId=${industry.id}`
      });
    },
    // 获取行业树数据
    async getIndustryTreeData() {
      try {
        const response = await utils_request.request.get("/miniapp/industry/tree/enterprises", {
          level: 1
          // 获取一级行业数据
        });
        if (response.success && response.data && response.data.code === 200) {
          const allIndustryList = response.data.data || [];
          this.industryList = allIndustryList.map((item) => ({
            id: item.nodeInfo.nodeId,
            name: item.nodeInfo.nodeName,
            count: item.statistics.totalCount || 0,
            icon: utils_imageUtils.processServerImageUrl(item.nodeInfo.coverImage),
            coverImage: item.nodeInfo.coverImage
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/industry-list.vue:72", "获取行业数据失败:", error);
      }
    }
  },
  onLoad() {
    this.getIndustryTreeData();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.industryList, (industry, index, i0) => {
      return {
        a: industry.icon,
        b: common_vendor.t(industry.name),
        c: common_vendor.t(industry.count),
        d: index,
        e: common_vendor.o(($event) => $options.goToIndustryDetail(industry), index)
      };
    }),
    b: $options.getImagePath("right.png")
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8b071e04"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/industry-list.js.map
