"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      topImageData: null,
      loading: true,
      roadshowList: [],
      // 路演专区列表
      roadshowLoading: true
      // 路演数据加载状态
    };
  },
  computed: {
    // 动态计算头部样式
    headerStyle() {
      if (this.topImageData && this.topImageData.backgroundColor) {
        return {
          background: this.topImageData.backgroundColor
        };
      }
      return {};
    }
  },
  onLoad() {
    this.getTopImage();
    this.getRoadshowList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 创赛路演";
    },
    // 自定义分享内容
    getShareContent() {
      return "创赛路演分享";
    },
    // 获取创赛路演顶图
    async getTopImage() {
      try {
        common_vendor.index.__f__("log", "at pages/index/competition.vue:98", "🖼️ 开始获取创赛路演顶图...");
        common_vendor.index.__f__("log", "at pages/index/competition.vue:99", "🖼️ API地址: POST /miniapp/topimage/app/getEnabledListByPage");
        common_vendor.index.__f__("log", "at pages/index/competition.vue:100", "🖼️ 请求参数: pageCode = roadshow");
        this.loading = true;
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "roadshow");
        common_vendor.index.__f__("log", "at pages/index/competition.vue:105", "🖼️ 创赛路演顶图完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const topImageList = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/competition.vue:109", "🖼️ 原始顶图数据:", topImageList);
          if (topImageList.length > 0) {
            const topImage = topImageList[0];
            this.topImageData = {
              imageUrl: utils_imageUtils.processServerImageUrl(topImage.imageUrl)
            };
            common_vendor.index.__f__("log", "at pages/index/competition.vue:117", "🖼️ 处理后的顶图数据:", this.topImageData);
          } else {
            common_vendor.index.__f__("log", "at pages/index/competition.vue:119", "🖼️ 没有找到启用的顶图，使用默认样式");
            this.topImageData = null;
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/competition.vue:123", "🖼️ ❌ 顶图数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/competition.vue:124", "🖼️ 响应详情:", response);
          this.topImageData = null;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/competition.vue:128", "🖼️ 获取创赛路演顶图失败:", error);
        this.topImageData = null;
        common_vendor.index.showToast({
          title: "顶图加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.loading = false;
      }
    },
    // 顶图加载失败处理
    onTopImageError() {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:143", "🖼️ 顶图加载失败，切换到默认样式");
      this.topImageData = {
        ...this.topImageData,
        imageUrl: null
      };
    },
    // 获取启用的创赛路演列表
    async getRoadshowList() {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/index/competition.vue:153", "🎭 开始获取创赛路演列表...");
        common_vendor.index.__f__("log", "at pages/index/competition.vue:154", "🎭 API地址: GET /miniapp/roadshow/app/getEnabledList");
        this.roadshowLoading = true;
        const response = await utils_request.request.get("/miniapp/roadshow/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/index/competition.vue:159", "🎭 创赛路演列表完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const roadshowData = response.data.data;
          common_vendor.index.__f__("log", "at pages/index/competition.vue:163", "🎭 路演数据获取成功:", roadshowData);
          if (Array.isArray(roadshowData) && roadshowData.length > 0) {
            this.roadshowList = roadshowData.map((item, index) => ({
              ...item,
              // 确保有必要的字段
              id: item.id || `roadshow_${index}`,
              title: item.title || item.name || `专区${index + 1}`,
              coverImage: item.coverImage || null,
              uniqueCode: item.uniqueCode || item.code || null,
              // 优先使用 uniqueCode
              identifier: item.identifier || item.code || `roadshow_${index}`,
              // 兼容旧版本
              jumpUrl: item.jumpUrl || item.url || null
            }));
            common_vendor.index.__f__("log", "at pages/index/competition.vue:176", "🎭 处理后的路演列表:", this.roadshowList);
          } else {
            common_vendor.index.__f__("log", "at pages/index/competition.vue:178", "🎭 ⚠️ 路演数据为空，使用默认配置");
            this.roadshowList = [];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/competition.vue:182", "🎭 ❌ 路演数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/competition.vue:183", "🎭 错误信息:", ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "未知错误");
          this.roadshowList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/competition.vue:187", "🎭 获取路演列表失败:", error);
        this.roadshowList = [];
      } finally {
        this.roadshowLoading = false;
      }
    },
    // 路演图片加载失败处理
    onRoadshowImageError(roadshow, index) {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:196", `🎭 路演图片加载失败: ${roadshow.title}，使用默认图片`);
      this.$set(this.roadshowList, index, {
        ...roadshow,
        imageUrl: null
      });
    },
    // 进入路演专区（从接口数据）
    enterRoadshowSection(roadshow) {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:206", "🎭 进入路演专区:", roadshow);
      if (roadshow.uniqueCode) {
        this.navigateByUniqueCode(roadshow.uniqueCode, roadshow);
      } else if (roadshow.jumpUrl) {
        if (roadshow.jumpUrl.startsWith("http")) {
          common_vendor.index.navigateTo({
            url: `/pages/webview/index?url=${encodeURIComponent(roadshow.jumpUrl)}&title=${encodeURIComponent(roadshow.title || "路演详情")}`
          });
        } else if (roadshow.jumpUrl.startsWith("/pages/")) {
          common_vendor.index.navigateTo({
            url: roadshow.jumpUrl
          });
        } else {
          common_vendor.index.__f__("log", "at pages/index/competition.vue:224", "🎭 未知跳转URL格式:", roadshow.jumpUrl);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      } else if (roadshow.identifier) {
        this.navigateByIdentifier(roadshow.identifier, roadshow);
      } else {
        common_vendor.index.showToast({
          title: roadshow.title || "功能开发中",
          icon: "none"
        });
      }
    },
    // 根据 uniqueCode 进行页面跳转
    navigateByUniqueCode(uniqueCode, roadshow) {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:244", "🎭 根据 uniqueCode 跳转:", uniqueCode);
      switch (uniqueCode) {
        case "zone_TD":
          common_vendor.index.__f__("log", "at pages/index/competition.vue:249", "🎭 跳转到海棠杯专区");
          common_vendor.index.navigateTo({
            url: "/pages/competition/haitang"
          });
          break;
        case "zone_XQ":
          common_vendor.index.__f__("log", "at pages/index/competition.vue:257", "🎭 跳转到西青金种子路演页面");
          common_vendor.index.navigateTo({
            url: "/pages/xiqing/roadshow"
          });
          break;
        case "zone_XH":
          common_vendor.index.__f__("log", "at pages/index/competition.vue:265", "🎭 宜怀金种子专区暂未开发");
          common_vendor.index.showToast({
            title: "宜怀金种子专区开发中",
            icon: "none"
          });
          break;
        case "zone_QT":
          common_vendor.index.__f__("log", "at pages/index/competition.vue:274", "🎭 其他融资专区暂未开发");
          common_vendor.index.showToast({
            title: "其他融资专区开发中",
            icon: "none"
          });
          break;
        default:
          common_vendor.index.__f__("log", "at pages/index/competition.vue:283", "🎭 未知的 uniqueCode:", uniqueCode);
          common_vendor.index.showToast({
            title: roadshow.title || "功能开发中",
            icon: "none"
          });
          break;
      }
    },
    // 根据标识符进行页面跳转（兼容旧版本）
    navigateByIdentifier(identifier, roadshow) {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:294", "🎭 根据标识符跳转:", identifier);
      switch (identifier.toLowerCase()) {
        case "haitang":
        case "htcup":
        case "haitangcup":
          common_vendor.index.navigateTo({
            url: "/pages/competition/haitang"
          });
          break;
        case "xiqing":
        case "xiqing_roadshow":
        case "xiqingjinzhongzi":
          common_vendor.index.navigateTo({
            url: "/pages/xiqing/roadshow"
          });
          break;
        case "yihuai":
        case "yihuai_roadshow":
        case "yihuaijinzhongzi":
          common_vendor.index.showToast({
            title: "宜怀金种子专区开发中",
            icon: "none"
          });
          break;
        case "other":
        case "other_financing":
        case "qitarongzi":
          common_vendor.index.showToast({
            title: "其他融资专区开发中",
            icon: "none"
          });
          break;
        default:
          common_vendor.index.__f__("log", "at pages/index/competition.vue:337", "🎭 未知的路演标识符:", identifier);
          common_vendor.index.showToast({
            title: roadshow.title || "功能开发中",
            icon: "none"
          });
          break;
      }
    },
    // 进入专区（兼容旧版本）
    enterSection(type) {
      common_vendor.index.__f__("log", "at pages/index/competition.vue:348", "进入专区:", type);
      if (type === "haitang") {
        common_vendor.index.navigateTo({
          url: "/pages/competition/haitang"
        });
      } else if (type === "xiqing") {
        common_vendor.index.__f__("log", "at pages/index/competition.vue:355", "🎭 跳转到西青金种子路演页面");
        common_vendor.index.navigateTo({
          url: "/pages/xiqing/roadshow"
        });
      } else {
        common_vendor.index.showToast({
          title: "功能开发中",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.topImageData && $data.topImageData.imageUrl
  }, $data.topImageData && $data.topImageData.imageUrl ? {
    b: $options.processServerImageUrl($data.topImageData.imageUrl),
    c: common_vendor.o((...args) => $options.onTopImageError && $options.onTopImageError(...args))
  } : {
    d: common_vendor.t($data.topImageData && $data.topImageData.title || "创新创业 天大担当"),
    e: common_vendor.t($data.topImageData && $data.topImageData.subtitle1 || "汇聚创新项目，连接投资机遇"),
    f: common_vendor.t($data.topImageData && $data.topImageData.subtitle2 || "助力创业梦想，共创美好未来")
  }, {
    g: common_vendor.s($options.headerStyle),
    h: common_vendor.f($data.roadshowList, (roadshow, index, i0) => {
      return {
        a: $options.processServerImageUrl(roadshow.coverImage),
        b: common_vendor.o(($event) => $options.onRoadshowImageError(roadshow, index), roadshow.id || index),
        c: roadshow.id || index,
        d: common_vendor.n(`roadshow-card-${index}`),
        e: common_vendor.o(($event) => $options.enterRoadshowSection(roadshow), roadshow.id || index)
      };
    }),
    i: $data.roadshowLoading
  }, $data.roadshowLoading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/competition.js.map
