"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      newsList: []
      // 新闻列表
    };
  },
  onLoad() {
    this.getNewsList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 获取新闻列表 - 使用与海棠杯页面相同的API
    async getNewsList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/more-news.vue:52", "📰 开始获取新闻中心列表...");
        common_vendor.index.__f__("log", "at pages/index/more-news.vue:53", "📰 API地址: GET /miniapp/haitang/news/list");
        const response = await utils_request.request.get("/miniapp/haitang/news/list");
        common_vendor.index.__f__("log", "at pages/index/more-news.vue:56", "📰 新闻列表完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const newsData = response.data.rows || [];
          common_vendor.index.__f__("log", "at pages/index/more-news.vue:60", "📰 原始新闻数据:", newsData);
          this.newsList = newsData.map((item) => ({
            id: item.id,
            title: item.title || "暂无标题",
            desc: item.digest || "暂无摘要",
            time: this.formatTime(item.createdAt || item.wechatCreateTime || item.createTime),
            image: utils_imageUtils.processServerImageUrl(item.thumbUrl, utils_imageUtils.getImagePath("default-news.png")),
            articleUrl: item.wechatArticleUrl,
            // 微信公众号文章链接
            author: item.author || "暂无作者",
            status: item.status
          }));
          common_vendor.index.__f__("log", "at pages/index/more-news.vue:74", "📰 新闻中心列表获取成功，共", this.newsList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/more-news.vue:75", "📰 处理后的新闻中心数据:", this.newsList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/more-news.vue:77", "📰 ❌ 新闻中心数据获取失败！");
          this.newsList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-news.vue:81", "📰 获取新闻中心列表失败:", error);
        this.newsList = [];
      }
    },
    // 查看新闻详情
    viewNewsDetail(news) {
      common_vendor.index.__f__("log", "at pages/index/more-news.vue:88", "📰 点击新闻:", news);
      if (news.articleUrl) {
        if (news.articleUrl.startsWith("http")) {
          this.openInWebview(news.articleUrl, news.title || "新闻详情");
        } else if (news.articleUrl.startsWith("/pages/")) {
          common_vendor.index.navigateTo({
            url: news.articleUrl
          });
        }
      } else {
        common_vendor.index.showToast({
          title: `查看${news.title}详情`,
          icon: "none"
        });
      }
    },
    // 在webview中打开链接
    openInWebview(url, title) {
      const encodedUrl = encodeURIComponent(url);
      const encodedTitle = encodeURIComponent(title);
      common_vendor.index.navigateTo({
        url: `/pages/webview/webview?url=${encodedUrl}&title=${encodedTitle}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/more-news.vue:120", "跳转webview失败:", err);
          common_vendor.index.showToast({
            title: "打开链接失败",
            icon: "none"
          });
        }
      });
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/index/more-news.vue:131", "图片加载失败:", e);
    },
    // 格式化时间显示
    formatTime(timeStr) {
      if (!timeStr)
        return "暂无时间";
      try {
        const time = new Date(timeStr);
        const now = /* @__PURE__ */ new Date();
        const diff = now - time;
        const minutes = Math.floor(diff / (1e3 * 60));
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
        const months = Math.floor(diff / (1e3 * 60 * 60 * 24 * 30));
        if (minutes < 60) {
          return minutes <= 0 ? "刚刚" : `${minutes}分钟前`;
        } else if (hours < 24) {
          return `${hours}小时前`;
        } else if (days < 30) {
          return `${days}天前`;
        } else {
          return `${months}个月前`;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-news.vue:159", "时间格式化失败:", error);
        return "时间格式错误";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.newsList && $data.newsList.length > 0
  }, $data.newsList && $data.newsList.length > 0 ? {
    b: common_vendor.f($data.newsList, (news, index, i0) => {
      return {
        a: news.image,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), news.id || index),
        c: common_vendor.t(news.title),
        d: common_vendor.t(news.desc),
        e: news.id || index,
        f: common_vendor.o(($event) => $options.viewNewsDetail(news), news.id || index)
      };
    })
  } : {
    c: $options.getImagePath("icon8.png")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/more-news.js.map
