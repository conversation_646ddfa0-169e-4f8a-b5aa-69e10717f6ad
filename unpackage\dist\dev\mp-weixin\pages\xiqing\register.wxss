.register-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
  padding-bottom: 40rpx;
}
/* 顶部区域样式 */
.header-section {
  width: 100%;
  position: relative;
}
.header-image-container {
  width: 100%;
}
.header-image {
  width: 100%;
  display: block;
}
.header-content {
  background: linear-gradient(270deg, #013fb0 0%, #002566 100%);
  padding: 80rpx 40rpx 120rpx;
  text-align: center;
  color: white;
}
.main-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}
.sub-title {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}
/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 表单卡片 */
.form-card {
  background: white;
  margin: 0 30rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 20;
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.card-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.required-star {
  color: #ff4757 !important;
  margin-right: 4rpx;
  font-weight: bold;
}
.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
/* 选择器其他容器 - 垂直布局 */
.select-other-container {
  flex-direction: column;
  align-items: stretch;
  gap: 20rpx;
}
.input-container .form-input,
.input-container picker,
.input-container .picker-input,
.input-container .form-textarea {
  flex: 1;
  width: 0;
  min-width: 0;
}
.form-input {
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  width: 100% !important;
}
.form-input::-webkit-input-placeholder {
  color: #999;
}
.form-input::placeholder {
  color: #999;
}
.form-textarea {
  min-height: 160rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
  width: 100% !important;
}
picker {
  width: 100% !important;
}
.picker-input {
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100% !important;
}
.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.picker-text.placeholder {
  color: #999;
}
.picker-arrow {
  font-size: 24rpx;
  color: #999;
}
.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
.picker-input.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
.form-textarea.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
/* 提交按钮 */
.submit-section {
  margin-top: 60rpx;
  display: flex;
  justify-content: center;
}
.submit-btn {
  width: 80%;
  height: 80rpx;
  background: linear-gradient(90deg, #6192ee 0%, #4a7de8 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn:active {
  opacity: 0.8;
}
.submit-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}
/* 复选框样式 */
.checkbox-container {
  width: 100%;
}
.checkbox-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}
.checkbox-box {
  width: 40rpx;
  height: 40rpx;
  min-width: 40rpx;
  /* 防止被挤压 */
  min-height: 40rpx;
  /* 防止被挤压 */
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background: #fff;
  flex-shrink: 0;
  /* 防止收缩 */
}
.checkbox-box.checked {
  background: #6192ee;
  border-color: #6192ee;
}
.checkbox-check {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.checkbox-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
/* 单选框样式 */
.radio-container {
  width: 100%;
}
.radio-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
}
.radio-circle {
  width: 40rpx;
  height: 40rpx;
  min-width: 40rpx;
  /* 防止被挤压 */
  min-height: 40rpx;
  /* 防止被挤压 */
  border: 2rpx solid #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background: #fff;
  flex-shrink: 0;
  /* 防止收缩 */
}
.radio-circle.checked {
  border-color: #6192ee;
}
.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background: #6192ee;
  border-radius: 50%;
}
.radio-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}
/* 其他输入框样式 */
.other-input {
  flex: 1;
  height: 60rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
  color: #333;
  margin-left: 20rpx;
}
.other-input-full {
  width: 100%;
  height: 76rpx !important;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 76rpx;
  /* 确保文字垂直居中 */
}
.radio-other-item,
.checkbox-other-item {
  flex-direction: column;
  align-items: flex-start;
}
.radio-other-item .radio-label,
.checkbox-other-item .checkbox-label {
  margin-bottom: 10rpx;
}
.radio-other-item .other-input,
.checkbox-other-item .other-input {
  width: 100%;
  margin-left: 0;
  margin-top: 10rpx;
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
/* 文件上传样式 */
.image-upload-container {
  width: 100%;
  margin-top: 0;
  position: relative;
}
.image-upload-area {
  width: 100%;
  height: 64rpx;
  background: #f8f9fa;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  position: relative;
  box-sizing: border-box;
}
.upload-btn {
  background: #E9ECEF;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  height: 48rpx;
  line-height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-hint {
  font-size: 28rpx;
  color: #999;
  flex: 1;
  margin-left: 16rpx;
}
.upload-status {
  font-size: 32rpx;
  color: #28a745;
  font-weight: bold;
}
