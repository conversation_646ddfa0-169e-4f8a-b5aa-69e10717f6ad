.investment-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  /* 底部导航栏的高度 */
}
/* 顶部区域 */
.header {
  background: linear-gradient(270deg, #013fb0 0%, #002566 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
}
.header image {
  position: absolute;
  width: 100%;
  top: 120rpx;
}
.search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 40rpx;
  position: relative;
  z-index: 10;
}
.search-input {
  flex: 1;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.search-input::-webkit-input-placeholder {
  color: #fff;
}
.search-input::placeholder {
  color: #fff;
}
.search-btn {
  width: 120rpx;
  height: 80rpx;
  background-color: #72a5ff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
}
/* 筛选条件区域 */
.filter-section {
  background: white;
  display: flex;
  padding: 10rpx 0rpx;
  margin: -30rpx 0rpx 10rpx;
  position: relative;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx #c7d6f2;
}
.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #f0f0f0;
}
.filter-item:last-child {
  border-right: none;
}
.filter-picker {
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 40rpx;
  font-weight: 900;
}
.filter-arrow {
  font-size: 20rpx;
  margin-top: -2rpx;
}
/* 项目列表 */
.projects-list {
  padding: 20rpx;
}
/* 加载和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.project-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
/* 项目头部区域：头像 + 标题 + 标签 */
.project-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.project-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
  border: 2px solid #0741ab;
}
.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.project-title-section {
  flex: 1;
}
.project-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.project-tags {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.tag-item {
  font-size: 24rpx;
  color: #000;
  background-color: #cad9f8;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
}
/* 分割线 */
.divider-line {
  height: 1rpx;
  background-color: #E5E5E5;
  margin: 20rpx 0;
}
/* 项目内容区域：描述 + 操作按钮 */
.project-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.project-action {
  display: flex;
  justify-content: flex-end;
}
.action-btn {
  font-size: 28rpx;
  color: #fff;
  background-color: #151e61;
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-weight: 500;
}
