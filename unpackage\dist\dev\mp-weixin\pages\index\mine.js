"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const ProfileGuideModal = () => "../../components/ProfileGuideModal.js";
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  components: {
    ProfileGuideModal
  },
  data() {
    return {
      // 个人资料完善状态
      isProfileComplete: true,
      // 引导弹窗显示状态
      showProfileGuide: false,
      userProfile: {
        realName: "",
        phonenumber: "",
        region: "",
        graduateSchool: "",
        major: "",
        college: "",
        currentCompany: "",
        positionTitle: "",
        positionType: "",
        positionName: "",
        industryField: "",
        portraitUrl: "",
        points: 0,
        profileCompleteness: 0,
        personalIntroduction: ""
      },
      industryTags: []
      // 处理后的行业标签数组
    };
  },
  onLoad() {
    this.getUserProfileDetail();
  },
  onShow() {
    const needRefresh = common_vendor.index.getStorageSync("minePageNeedRefresh");
    if (needRefresh) {
      common_vendor.index.__f__("log", "at pages/index/mine.vue:225", "检测到需要刷新mine页面数据，重新加载用户资料");
      this.getUserProfileDetail();
      common_vendor.index.removeStorageSync("minePageNeedRefresh");
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 我的";
    },
    // 自定义分享内容
    getShareContent() {
      return "个人中心分享";
    },
    // 显示引导弹窗
    showGuideModal() {
      this.showProfileGuide = true;
    },
    // 关闭引导弹窗
    closeProfileGuide() {
      this.showProfileGuide = false;
    },
    // 前往个人资料页面
    goToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/index/profile?from=mine"
      });
    },
    // 获取用户资料详情
    getUserProfileDetail() {
      common_vendor.index.__f__("log", "at pages/index/mine.vue:265", "开始获取用户资料详情");
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_request.request.get("/miniapp/user/getProfileDetail").then((result) => {
        var _a, _b;
        common_vendor.index.__f__("log", "at pages/index/mine.vue:274", "用户资料详情响应:", result);
        if (result && result.data && result.data.code === 200) {
          const profileData = result.data.data || {};
          const basicInfo = profileData.basicInfo || {};
          const educationInfo = profileData.educationInfo || {};
          const careerInfo = profileData.careerInfo || {};
          this.userProfile = {
            realName: basicInfo.realName || "",
            phonenumber: basicInfo.phonenumber || "",
            region: basicInfo.region || "",
            graduateSchool: educationInfo.graduateSchool || "",
            major: educationInfo.major || "",
            college: educationInfo.college || "",
            currentCompany: careerInfo.currentCompany || "",
            positionTitle: careerInfo.positionTitle || "",
            positionType: this.extractPositionType(careerInfo.positionTitle || ""),
            positionName: this.extractPositionName(careerInfo.positionTitle || ""),
            industryField: careerInfo.industryField || "",
            portraitUrl: basicInfo.portraitUrl || "",
            points: profileData.totalPoints || 0,
            profileCompleteness: profileData.profileCompletionRate !== null ? Math.round(profileData.profileCompletionRate) : null,
            personalIntroduction: profileData.personalIntroduction || ""
          };
          this.processIndustryTags(careerInfo.industryTags);
          common_vendor.index.__f__("log", "at pages/index/mine.vue:305", "用户资料加载成功:", this.userProfile);
          this.checkProfileStatus();
        } else {
          common_vendor.index.__f__("error", "at pages/index/mine.vue:313", "获取用户资料失败:", ((_a = result == null ? void 0 : result.data) == null ? void 0 : _a.msg) || "未知错误");
          common_vendor.index.showToast({
            title: ((_b = result == null ? void 0 : result.data) == null ? void 0 : _b.msg) || "获取资料失败",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/mine.vue:321", "获取用户资料详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请检查网络连接",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 检查个人资料完善状态（异步版本）
    async checkProfileStatus() {
      try {
        const isProfileComplete = await utils_profileCheck.checkProfileCompleteAsync();
        this.isProfileComplete = isProfileComplete;
        common_vendor.index.__f__("log", "at pages/index/mine.vue:337", "个人资料完善状态:", this.isProfileComplete);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/mine.vue:339", "检查个人资料完善状态失败:", error);
        this.isProfileComplete = false;
      }
    },
    // 计算资料完善度
    calculateCompleteness(basicInfo, educationInfo, careerInfo) {
      let completedCount = 0;
      let totalCount = 0;
      const basicFields = ["realName", "phonenumber", "region", "birthDate"];
      basicFields.forEach((field) => {
        totalCount++;
        if (basicInfo[field] && basicInfo[field].toString().trim()) {
          completedCount++;
        }
      });
      const educationFields = ["graduateSchool", "major"];
      educationFields.forEach((field) => {
        totalCount++;
        if (educationInfo[field] && educationInfo[field].toString().trim()) {
          completedCount++;
        }
      });
      const careerFields = ["currentCompany", "positionTitle"];
      careerFields.forEach((field) => {
        totalCount++;
        if (careerInfo[field] && careerInfo[field].toString().trim()) {
          completedCount++;
        }
      });
      totalCount++;
      if (careerInfo.industryField || careerInfo.industryTags && careerInfo.industryTags.length > 0) {
        completedCount++;
      }
      return totalCount > 0 ? Math.round(completedCount / totalCount * 100) : 0;
    },
    // 处理行业标签数据 - 只显示一级分类
    processIndustryTags(industryTags) {
      if (!industryTags) {
        this.industryTags = [];
        return;
      }
      const level1Categories = /* @__PURE__ */ new Set();
      if (Array.isArray(industryTags)) {
        industryTags.forEach((tag) => {
          if (tag.rootNode && tag.rootNode.nodeName) {
            level1Categories.add(tag.rootNode.nodeName);
          }
        });
      } else if (typeof industryTags === "string") {
        common_vendor.index.__f__("log", "at pages/index/mine.vue:408", "检测到旧格式的行业标签数据:", industryTags);
      }
      this.industryTags = Array.from(level1Categories);
      if (this.industryTags.length === 0) {
        this.industryTags = ["暂无行业分类"];
      }
      common_vendor.index.__f__("log", "at pages/index/mine.vue:419", "处理后的一级行业标签:", this.industryTags);
    },
    // 从职位标题中提取职位类型
    extractPositionType(positionTitle) {
      if (!positionTitle)
        return "";
      if (positionTitle.includes(" - ")) {
        return positionTitle.split(" - ")[0].trim();
      }
      return positionTitle;
    },
    // 从职位标题中提取职位名称
    extractPositionName(positionTitle) {
      if (!positionTitle)
        return "";
      if (positionTitle.includes(" - ")) {
        return positionTitle.split(" - ")[1].trim();
      }
      return "";
    },
    navigateToHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    navigateToContacts() {
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts-new"
      });
    },
    navigateToPublish() {
      common_vendor.index.navigateTo({
        url: "/pages/mine/publish"
      });
    },
    navigateToDocking() {
      common_vendor.index.navigateTo({
        url: "/pages/mine/docking"
      });
    },
    navigateToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/index/profile?from=mine"
      });
    },
    navigateToCompanyProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/industry/company-profile"
      });
    },
    navigateToBusinessCard() {
      common_vendor.index.navigateTo({
        url: "/pages/index/business-card-detail?fromMine=true"
      });
    },
    navigateToDemandSquare() {
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    },
    // 导航到产业资源
    navigateToIndustry() {
      common_vendor.index.reLaunch({
        url: "/pages/industry/index"
      });
    },
    navigateToAbout() {
      common_vendor.index.navigateTo({
        url: "/pages/index/about-us"
      });
    },
    navigateToPrivacy() {
      common_vendor.index.navigateTo({
        url: "/pages/agreement/privacy"
      });
    },
    navigateToAccountManage() {
      common_vendor.index.navigateTo({
        url: "/pages/account/manage"
      });
    },
    navigateToMyFollowing() {
      common_vendor.index.navigateTo({
        url: "/pages/mine/following"
      });
    },
    navigateToMessage() {
      common_vendor.index.navigateTo({
        url: "/pages/chat/list"
      });
    },
    // 导航到积分商城
    navigateToPointsMall() {
      common_vendor.index.navigateTo({
        url: "/pages/points/mall"
      });
    }
  }
};
if (!Array) {
  const _component_ProfileGuideModal = common_vendor.resolveComponent("ProfileGuideModal");
  _component_ProfileGuideModal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: !$data.isProfileComplete
  }, !$data.isProfileComplete ? {
    c: $options.getImagePath("top_bar.png"),
    d: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args))
  } : {}, {
    e: $options.processServerImageUrl($data.userProfile.portraitUrl, $options.getImagePath("avatar.png")),
    f: common_vendor.t($data.userProfile.realName || "暂无姓名"),
    g: common_vendor.t($data.userProfile.region || "暂无地区"),
    h: common_vendor.t($data.userProfile.graduateSchool || "暂无院校"),
    i: common_vendor.t($data.userProfile.major || "暂无专业"),
    j: common_vendor.t($data.userProfile.currentCompany || "暂无企业信息"),
    k: common_vendor.t($data.userProfile.positionName || "暂无职位名称"),
    l: $options.getImagePath("score_icon.png"),
    m: common_vendor.t($data.userProfile.points || 0),
    n: $data.industryTags && $data.industryTags.length > 0
  }, $data.industryTags && $data.industryTags.length > 0 ? {
    o: common_vendor.f($data.industryTags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    p: common_vendor.t($data.userProfile.personalIntroduction || "暂无自我介绍"),
    q: common_vendor.o((...args) => $options.navigateToBusinessCard && $options.navigateToBusinessCard(...args)),
    r: $options.getImagePath("shop_icon.png"),
    s: common_vendor.o((...args) => $options.navigateToPointsMall && $options.navigateToPointsMall(...args)),
    t: $options.getImagePath("personal_list1.png"),
    v: common_vendor.t($data.userProfile.profileCompleteness !== null ? $data.userProfile.profileCompleteness + "%" : "null"),
    w: common_vendor.o((...args) => $options.navigateToProfile && $options.navigateToProfile(...args)),
    x: $options.getImagePath("personal_list2.png"),
    y: common_vendor.o((...args) => $options.navigateToPublish && $options.navigateToPublish(...args)),
    z: $options.getImagePath("personal_list3.png"),
    A: common_vendor.o((...args) => $options.navigateToDocking && $options.navigateToDocking(...args)),
    B: $options.getImagePath("personal_list6.png"),
    C: common_vendor.o((...args) => $options.navigateToMyFollowing && $options.navigateToMyFollowing(...args)),
    D: $options.getImagePath("personal_list8.png"),
    E: common_vendor.o((...args) => $options.navigateToMessage && $options.navigateToMessage(...args)),
    F: $options.getImagePath("personal_list4.png"),
    G: common_vendor.o((...args) => $options.navigateToAbout && $options.navigateToAbout(...args)),
    H: $options.getImagePath("personal_list5.png"),
    I: common_vendor.o((...args) => $options.navigateToPrivacy && $options.navigateToPrivacy(...args)),
    J: $options.getImagePath("personal_list7.png"),
    K: common_vendor.o((...args) => $options.navigateToAccountManage && $options.navigateToAccountManage(...args)),
    L: $options.getImagePath("bottom_order_icon1.png"),
    M: common_vendor.o((...args) => $options.navigateToHome && $options.navigateToHome(...args)),
    N: $options.getImagePath("bottom_order_icon2.png"),
    O: common_vendor.o((...args) => $options.navigateToContacts && $options.navigateToContacts(...args)),
    P: $options.getImagePath("bottom_order_icon.png"),
    Q: common_vendor.o((...args) => $options.navigateToDemandSquare && $options.navigateToDemandSquare(...args)),
    R: $options.getImagePath("bottom_order_icon3.png"),
    S: common_vendor.o((...args) => $options.navigateToIndustry && $options.navigateToIndustry(...args)),
    T: $options.getImagePath("bottom_order_icon4_active.png"),
    U: common_vendor.o($options.closeProfileGuide),
    V: common_vendor.o($options.goToProfile),
    W: common_vendor.p({
      visible: $data.showProfileGuide
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/mine.js.map
