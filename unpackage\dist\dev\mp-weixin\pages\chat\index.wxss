/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-container.data-v-5a559478 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 连接状态提示 */
.connection-status.data-v-5a559478 {
  padding: 15rpx 20rpx;
  background-color: #f0f9ff;
  text-align: center;
  border-bottom: 1rpx solid #e0f2fe;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}
.connection-status .status-text.data-v-5a559478 {
  font-size: 24rpx;
  color: #0369a1;
}
.connection-status .loading-dots.data-v-5a559478 {
  display: flex;
  gap: 6rpx;
}
.connection-status .loading-dots .dot.data-v-5a559478 {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: #0369a1;
  animation: loading-dot-5a559478 1.4s infinite ease-in-out;
}
.connection-status .loading-dots .dot.data-v-5a559478:nth-child(1) {
  animation-delay: -0.32s;
}
.connection-status .loading-dots .dot.data-v-5a559478:nth-child(2) {
  animation-delay: -0.16s;
}
@keyframes loading-dot-5a559478 {
0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
}
40% {
    transform: scale(1);
    opacity: 1;
}
}
/* 聊天区域 */
.chat-area.data-v-5a559478 {
  flex: 1;
  height: 0;
  /* 确保flex子元素有明确高度 */
  background-color: #f5f5f5;
}

/* 刷新提示 */
.refresh-tip.data-v-5a559478 {
  padding: 30rpx 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.refresh-content.data-v-5a559478 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.loading-spinner.data-v-5a559478 {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e0e0e0;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: spin-5a559478 1s linear infinite;
}
@keyframes spin-5a559478 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.refresh-text.data-v-5a559478 {
  font-size: 26rpx;
  color: #666;
}
.message-list.data-v-5a559478 {
  padding: 0rpx 20rpx 40rpx;
}
.bottom-anchor.data-v-5a559478 {
  height: 1rpx;
  width: 100%;
}
.message-item.data-v-5a559478 {
  padding-top: 1rpx;
  margin-bottom: 20rpx;
}
.time-divider.data-v-5a559478 {
  text-align: center;
  margin: 40rpx 0;
}
.time-divider .time-text.data-v-5a559478 {
  background-color: rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}
.message-content.data-v-5a559478 {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}
.message-content.message-own.data-v-5a559478 {
  justify-content: flex-end;
}
.avatar.data-v-5a559478 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}
.avatar .avatar-img.data-v-5a559478 {
  width: 100%;
  height: 100%;
}
.message-bubble.data-v-5a559478 {
  max-width: 60%;
  background-color: white;
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: inline-block;
}
.message-bubble .voice-icon.data-v-5a559478 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}
.message-bubble.bubble-own.data-v-5a559478 {
  background-color: #4a90e2;
}
.message-bubble.bubble-own .message-text.data-v-5a559478 {
  color: white;
}
.message-bubble.no-avatar.data-v-5a559478 {
  margin-left: 100rpx;
}
.message-bubble.bubble-own.no-avatar.data-v-5a559478 {
  margin-left: 0;
  margin-right: 100rpx;
}
.message-bubble .message-text.data-v-5a559478 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

/* 输入区域 */
.input-area.data-v-5a559478 {
  background-color: white;
  border-top: 1rpx solid #e0e0e0;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  flex-shrink: 0;
  /* 防止被压缩 */
}
.input-wrapper.data-v-5a559478 {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 100rpx;
  padding: 0 15rpx;
  min-height: 80rpx;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

/* 左侧常用语按钮 */
.left-btn.data-v-5a559478 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}
.voice-btn.data-v-5a559478 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}
.voice-btn.data-v-5a559478:active {
  transform: scale(0.9);
  background: linear-gradient(135deg, #3dd470 0%, #32e6c9 100%);
}
.btn-text.data-v-5a559478 {
  font-size: 24rpx;
  font-weight: bold;
}
.message-input.data-v-5a559478 {
  flex: 1;
  height: 60rpx;
  background-color: transparent;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333;
  padding: 0 10rpx;
  line-height: 60rpx;
  box-sizing: border-box;
  min-width: 0;
  word-break: break-all;
  position: relative;
  z-index: 1;
}

/* 右侧按钮组 */
.right-buttons.data-v-5a559478 {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex-shrink: 0;
}
.right-btn.data-v-5a559478 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}
.emoji-btn.data-v-5a559478 {
  width: 54rpx;
  height: 54rpx;
}
.right-btn.data-v-5a559478:active {
  transform: scale(0.9);
  background-color: #e9ecef;
}
.emoji-text.data-v-5a559478 {
  font-size: 32rpx;
  line-height: 1;
}
.attach-text.data-v-5a559478 {
  font-size: 36rpx;
  font-weight: bold;
  color: #666;
  line-height: 1;
}
.btn-icon.data-v-5a559478 {
  font-size: 50rpx;
  color: #666;
}

/* 加号菜单样式 */
.attach-menu-overlay.data-v-5a559478 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn-5a559478 0.3s ease;
}
@keyframes fadeIn-5a559478 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.attach-menu.data-v-5a559478 {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-width: 750rpx;
  padding: 40rpx 30rpx 60rpx 30rpx;
  animation: slideUp-5a559478 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}
@keyframes slideUp-5a559478 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.attach-menu-title.data-v-5a559478 {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.attach-menu-grid.data-v-5a559478 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  margin-bottom: 40rpx;
}
.attach-menu-item.data-v-5a559478 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}
.attach-menu-item.data-v-5a559478:active {
  transform: scale(0.95);
  background-color: #e9ecef;
}
.attach-menu-icon.data-v-5a559478 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  font-size: 60rpx;
  transition: all 0.3s ease;
}
.attach-menu-icon .iconfont.data-v-5a559478 {
  width: 60rpx;
  height: 60rpx;
}
.attach-menu-item:active .attach-menu-icon.data-v-5a559478 {
  transform: scale(0.9);
}
.attach-menu-text.data-v-5a559478 {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}
.attach-menu-cancel.data-v-5a559478 {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  transition: all 0.3s ease;
}
.attach-menu-cancel.data-v-5a559478:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

/* 表情面板样式 */
.emoji-panel.data-v-5a559478 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn-5a559478 0.3s ease;
}
.emoji-panel-content.data-v-5a559478 {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-width: 750rpx;
  max-height: 60vh;
  animation: slideUp-5a559478 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.emoji-panel-header.data-v-5a559478 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.emoji-panel-title.data-v-5a559478 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.emoji-panel-close.data-v-5a559478 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.emoji-panel-close.data-v-5a559478:active {
  background-color: #e0e0e0;
}
.emoji-grid.data-v-5a559478 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20rpx;
  padding: 30rpx 40rpx 40rpx 40rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.emoji-item.data-v-5a559478 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}
.emoji-item.data-v-5a559478:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}
.emoji-text-large.data-v-5a559478 {
  font-size: 40rpx;
  line-height: 1;
}

/* 图片消息样式 */
.image-message.data-v-5a559478 {
  position: relative;
  display: inline-block;
}
.message-image.data-v-5a559478 {
  max-width: 400rpx;
  max-height: 400rpx;
  min-width: 200rpx;
  min-height: 200rpx;
  border-radius: 12rpx;
}

/* 发送状态指示器 */
.sending-indicator.data-v-5a559478 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
}
.sending-text.data-v-5a559478 {
  font-size: 20rpx;
  color: white;
}
.failed-indicator.data-v-5a559478 {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
}
.failed-text.data-v-5a559478 {
  font-size: 20rpx;
  color: white;
}

/* 图片消息中的发送状态指示器样式优化 */
.image-message .sending-indicator.data-v-5a559478,
.image-message .failed-indicator.data-v-5a559478 {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.image-message .sending-text.data-v-5a559478,
.image-message .failed-text.data-v-5a559478 {
  font-size: 22rpx;
  font-weight: 500;
}

/* 图片消息的气泡样式调整 - 完全隐藏气泡 */
.message-bubble.image-bubble.data-v-5a559478 {
  padding: 0 !important;
  background-color: transparent !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
}
.bubble-own.image-bubble.data-v-5a559478 {
  background-color: transparent !important;
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
}

/* 语音消息样式 */
.voice-message.data-v-5a559478 {
  display: flex;
  align-items: center;
  max-width: 400rpx;
  border-radius: 12rpx;
  cursor: pointer;
  position: relative;
}
.voice-message-own.data-v-5a559478 {
  color: white;
}
.voice-duration.data-v-5a559478 {
  font-size: 28rpx;
  color: #333;
}
.voice-message-own .voice-duration.data-v-5a559478 {
  color: white;
}

/* 语音录制弹窗样式 */
.voice-record-overlay.data-v-5a559478 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}
.voice-record-modal.data-v-5a559478 {
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
}
.voice-record-content.data-v-5a559478 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.voice-status.data-v-5a559478 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}
.voice-icon-container.data-v-5a559478 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}
.voice-icon-container .voice-icon.data-v-5a559478 {
  width: 80rpx;
  height: 80rpx;
  transition: opacity 0.3s ease;
}
.voice-status-text.data-v-5a559478 {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.voice-time.data-v-5a559478 {
  font-size: 48rpx;
  color: #003399;
  font-weight: bold;
}
.voice-controls.data-v-5a559478 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20rpx;
}
.voice-control-btn.data-v-5a559478 {
  padding: 24rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.start-btn.data-v-5a559478 {
  background-color: #003399;
  color: white;
}
.stop-btn.data-v-5a559478 {
  background-color: #ff4444;
  color: white;
}
.play-btn.data-v-5a559478 {
  background-color: #00aa00;
  color: white;
}
.send-btn.data-v-5a559478 {
  background-color: #003399;
  color: white;
}
.cancel-btn.data-v-5a559478 {
  background-color: #999;
  color: white;
}
.control-text.data-v-5a559478 {
  font-size: 28rpx;
}