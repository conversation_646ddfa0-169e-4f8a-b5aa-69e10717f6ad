.profile-container {
  width: 100%;
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}
.status-bar {
  background: transparent;
}
.header-section {
  background: linear-gradient(180deg, #c9daf6 0%, #f3f8fe 100%);
  padding: 60rpx 0 20rpx 0;
  position: relative;
}
.header-section image {
  position: absolute;
  width: 100%;
  bottom: 20rpx;
}
.header-content {
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}
.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #04349a;
  margin-bottom: 16rpx;
}
.header-subtitle {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
  font-weight: 900;
}
.progress-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-sizing: border-box;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.progress-bar {
  width: 100%;
  height: 16rpx;
  background: #d5e5fe;
  border-radius: 100rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}
.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #64d3ed 0%, #0540ac 100%);
  border-radius: 100rpx;
  transition: width 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
}
.progress-text {
  font-size: 24rpx;
  color: #777777;
  font-weight: 500;
}
.form-content {
  flex: 1;
  padding: 0rpx 20rpx;
  width: 100%;
  box-sizing: border-box;
  margin-top: -20rpx;
}
.info-card {
  width: 100%;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-sizing: border-box;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-icon {
  width: 46rpx;
  height: 46rpx;
  margin-right: 24rpx;
}
.icon-text {
  font-size: 32rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}
.points-badge {
  background: linear-gradient(180deg, #83d8ea 0%, #196eaf 100%);
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.points-badge.completed {
  background: linear-gradient(180deg, #ea676c 0%, #cf3734 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
}
.points-badge.earned {
  background: linear-gradient(180deg, #ea676c 0%, #cf3734 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(234, 103, 108, 0.3);
}
.form-item {
  margin-bottom: 32rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.form-label .required-star {
  color: #ff4757 !important;
  margin-right: 4rpx;
  font-weight: bold;
}
.form-required {
  color: #ff0000;
  font-size: 24rpx;
  margin-left: 12rpx;
}
.form-optional {
  color: #999;
  font-size: 24rpx;
  margin-left: 12rpx;
}
.form-input {
  width: 100%;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  margin-top: 16rpx;
}
.form-input.placeholder {
  color: #999;
}
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  margin-top: 16rpx;
  line-height: 1.5;
}
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  margin-right: 8rpx;
}
.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  margin-left: 8rpx;
}
.gender-radio-group {
  display: flex;
  margin-top: 16rpx;
  gap: 80rpx;
}
.radio-item {
  display: flex;
  align-items: center;
}
.radio-circle {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-circle.checked {
  border-color: #2196F3;
}
.radio-dot {
  width: 16rpx;
  height: 16rpx;
  background: #2196F3;
  border-radius: 50%;
}
.radio-text {
  font-size: 28rpx;
  color: #333;
}
.date-picker,
.selector-picker {
  margin-top: 16rpx;
}
.picker-input {
  width: 100%;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.picker-icon,
.picker-arrow {
  font-size: 24rpx;
  color: #999;
}
.position-input-group {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}
.position-type-picker {
  flex: 0 0 180rpx;
}
.position-type-input {
  width: 100%;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
.position-type-input .picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.position-type-input .picker-arrow {
  font-size: 20rpx;
  color: #999;
}
.position-name-input {
  flex: 1;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.position-name-input::-webkit-input-placeholder {
  color: #999;
}
.position-name-input::placeholder {
  color: #999;
}
.image-upload-container {
  width: 100%;
  margin-top: 16rpx;
  position: relative;
}
.image-upload-area {
  width: 100%;
  height: 76rpx;
  background: #f8f9fa;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  position: relative;
  box-sizing: border-box;
}
.upload-btn {
  background: #E9ECEF;
  border: none;
  border-radius: 8rpx;
  padding: 0rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 24rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-hint {
  font-size: 28rpx;
  color: #999;
  flex: 1;
}
.upload-status {
  position: absolute;
  top: 50%;
  right: 24rpx;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #52c41a;
  font-weight: bold;
}
.native-place-selector {
  margin-top: 16rpx;
}
.native-place-selector .province-picker {
  width: 100%;
}
.industry-selector {
  margin-top: 16rpx;
}
.industry-selector .picker-input .picker-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 40rpx);
}
.submit-section {
  margin-top: 40rpx;
  margin-bottom: 30rpx;
}
.submit-btn {
  width: 100%;
  height: 76rpx;
  background: linear-gradient(180deg, #023ca8 0%, #295abb 100%);
  border-radius: 44rpx;
  border: none;
  color: white;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin-top: 80rpx;
}
.submit-btn.disabled {
  background: #cccccc;
  color: #999999;
  cursor: not-allowed;
}
/* 行业选择器样式 */
.industry-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.industry-picker-content {
  width: 100%;
  max-height: 85vh;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 40rpx 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}
.industry-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}
.industry-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.industry-actions {
  display: flex;
  align-items: center;
  gap: 40rpx;
}
.cancel-btn {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
}
.confirm-btn {
  font-size: 28rpx;
  color: #007aff;
  padding: 10rpx 20rpx;
  font-weight: bold;
}
/* 树形选择器容器 */
.industry-tree-container {
  flex: 1;
  max-height: 60vh;
  padding: 20rpx 0;
}
/* 树形节点样式 */
.tree-node {
  margin-bottom: 0;
}
.tree-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}
.tree-item:active {
  background-color: #f0f0f0;
}
.tree-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.tree-item.disabled:active {
  background-color: transparent;
}
.tree-content {
  display: flex;
  align-items: center;
  flex: 1;
}
.expand-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  transition: transform 0.2s ease;
  margin-right: 20rpx;
}
.expand-icon.expanded {
  transform: rotate(0deg);
}
.tree-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.no-children-hint {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}
/* 复选框样式 */
.tree-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 20rpx;
}
.tree-checkbox.checked {
  border-color: #007aff;
  background-color: #007aff;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}
/* 一级分类样式 */
.level1-node {
  border-bottom: 1rpx solid #e5e5e5;
}
.level1-item {
  background-color: #fafafa;
}
.level1-item .tree-label {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
/* 二级分类样式 */
.tree-children {
  background-color: #fff;
}
.level2-children {
  padding-left: 40rpx;
}
.level2-item {
  background-color: #f9f9f9;
}
.level2-item .tree-label {
  font-size: 28rpx;
  color: #555;
}
/* 三级分类样式 */
.level3-children {
  padding-left: 80rpx;
}
.level3-item {
  background-color: #fff;
}
.level3-item .tree-label {
  font-size: 26rpx;
  color: #666;
}
.level3-item .expand-icon {
  display: none;
  /* 三级分类不需要展开图标 */
}
/* 流向分类样式 */
.stream-category {
  margin-bottom: 20rpx;
}
.stream-title {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx 40rpx;
  background-color: #f0f0f0;
  border-left: 4rpx solid #007aff;
  margin-bottom: 10rpx;
}
.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
.picker-input.input-error {
  border-color: #ff4757 !important;
  background-color: #fff5f5 !important;
}
