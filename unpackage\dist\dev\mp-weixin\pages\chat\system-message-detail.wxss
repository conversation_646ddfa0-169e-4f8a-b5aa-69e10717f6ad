.message-detail-container.data-v-4b6873e3 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
/* 加载状态 */
.loading-container.data-v-4b6873e3 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text.data-v-4b6873e3 {
  font-size: 28rpx;
  color: #666;
}
/* 错误状态 */
.error-container.data-v-4b6873e3 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  gap: 30rpx;
}
.error-text.data-v-4b6873e3 {
  font-size: 28rpx;
  color: #999;
}
.retry-btn.data-v-4b6873e3 {
  background-color: #003399;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
/* 消息内容 */
.message-content.data-v-4b6873e3 {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}
/* 消息头部 */
.message-header.data-v-4b6873e3 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.avatar-container.data-v-4b6873e3 {
  margin-right: 24rpx;
}
.avatar-img.data-v-4b6873e3 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f0f0;
}
.header-info.data-v-4b6873e3 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.message-title.data-v-4b6873e3 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.message-time.data-v-4b6873e3 {
  font-size: 24rpx;
  color: #999;
}
/* 消息正文 */
.message-body.data-v-4b6873e3 {
  margin-bottom: 30rpx;
}
.content-text.data-v-4b6873e3 {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}
/* 相关数据 */
.related-data.data-v-4b6873e3 {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}
.related-title.data-v-4b6873e3 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.related-content.data-v-4b6873e3 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.related-item.data-v-4b6873e3 {
  display: flex;
  align-items: center;
}
.related-label.data-v-4b6873e3 {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
  min-width: 120rpx;
}
.related-value.data-v-4b6873e3 {
  font-size: 26rpx;
  color: #333;
}
/* 操作按钮 */
.action-buttons.data-v-4b6873e3 {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.confirm-btn.data-v-4b6873e3 {
  background-color: #003399;
  color: white;
  border-radius: 50rpx;
  padding: 6rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}
