.privacy-policy-container {
  width: 100%;
  min-height: 100vh;
  background: #fff;
}
.content-section {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  max-width: 100%;
}
/* 富文本内容样式优化 */
.rich-text-content {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}
/* 富文本内部图片样式限制 */
.rich-text-content img {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  box-sizing: border-box !important;
  margin: 10rpx 0 !important;
}
/* 加载状态样式 */
.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
/* 无内容状态样式 */
.no-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.no-content-text {
  font-size: 28rpx;
  color: #999;
}
