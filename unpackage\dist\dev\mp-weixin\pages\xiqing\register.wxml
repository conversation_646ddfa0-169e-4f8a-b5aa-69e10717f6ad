<view class="register-container"><view class="header-section"><view class="header-image-container"><image src="{{a}}" mode="widthFix" class="header-image" binderror="{{b}}"></image></view></view><view wx:if="{{c}}" class="loading-container"><text class="loading-text">正在加载报名表单...</text></view><view wx:else class="form-card" style="margin-top:-60rpx"><view class="card-header"><image src="{{d}}" class="card-icon"></image><text class="card-title">路演报名</text></view><view wx:for="{{e}}" wx:for-item="field" wx:key="an" class="form-item"><text class="form-label"><text wx:if="{{field.a}}" class="required-star">*</text> {{field.b}}</text><view wx:if="{{field.c}}" class="input-container"><block wx:if="{{r0}}"><input class="{{['form-input', field.d && 'input-error']}}" placeholder="{{field.e}}" type="{{field.f}}" bindblur="{{field.g}}" value="{{field.h}}" bindinput="{{field.i}}"/></block></view><view wx:elif="{{field.j}}" class="input-container"><block wx:if="{{r0}}"><textarea class="{{['form-textarea', field.k && 'input-error']}}" placeholder="{{field.l}}" maxlength="{{field.m}}" bindblur="{{field.n}}" value="{{field.o}}" bindinput="{{field.p}}"></textarea></block></view><view wx:elif="{{field.q}}" class="input-container"><picker mode="selector" range="{{field.w}}" bindchange="{{field.x}}"><view class="{{['picker-input', field.v && 'input-error']}}"><text wx:if="{{field.r}}" class="picker-text">{{field.s}}</text><text wx:else class="picker-text placeholder">{{field.t}}</text><text class="picker-arrow">▼</text></view></picker></view><view wx:elif="{{field.y}}" class="input-container"><view class="radio-container"><view wx:for="{{field.z}}" wx:for-item="option" wx:key="d" class="radio-item" bindtap="{{option.e}}"><view class="{{['radio-circle', option.b && 'checked']}}"><view wx:if="{{option.a}}" class="radio-dot"></view></view><text class="radio-label">{{option.c}}</text></view></view></view><view wx:elif="{{field.A}}" class="input-container"><view class="radio-container"><view wx:for="{{field.B}}" wx:for-item="option" wx:key="d" class="radio-item" bindtap="{{option.e}}"><view class="{{['radio-circle', option.b && 'checked']}}"><view wx:if="{{option.a}}" class="radio-dot"></view></view><text class="radio-label">{{option.c}}</text></view><view class="radio-item radio-other-item"><view style="display:flex;align-items:center;width:100%"><view class="{{['radio-circle', field.D && 'checked']}}" bindtap="{{field.E}}"><view wx:if="{{field.C}}" class="radio-dot"></view></view><text class="radio-label">其他</text></view><input wx:if="{{field.F}}" class="other-input" placeholder="请输入其他内容" bindinput="{{field.G}}" value="{{field.H}}"/></view></view></view><view wx:elif="{{field.I}}" class="input-container"><view class="checkbox-container"><view wx:for="{{field.J}}" wx:for-item="option" wx:key="d" class="checkbox-item" bindtap="{{option.e}}"><view class="{{['checkbox-box', option.b && 'checked']}}"><text wx:if="{{option.a}}" class="checkbox-check">✓</text></view><text class="checkbox-label">{{option.c}}</text></view></view></view><view wx:elif="{{field.K}}" class="input-container"><view class="checkbox-container"><view wx:for="{{field.L}}" wx:for-item="option" wx:key="d" class="checkbox-item" bindtap="{{option.e}}"><view class="{{['checkbox-box', option.b && 'checked']}}"><text wx:if="{{option.a}}" class="checkbox-check">✓</text></view><text class="checkbox-label">{{option.c}}</text></view><view class="checkbox-item checkbox-other-item"><view style="display:flex;align-items:center;width:100%"><view class="{{['checkbox-box', field.N && 'checked']}}" bindtap="{{field.O}}"><text wx:if="{{field.M}}" class="checkbox-check">✓</text></view><text class="checkbox-label">其他</text></view><input wx:if="{{field.P}}" class="other-input" placeholder="请输入其他内容" value="{{field.Q}}" bindinput="{{field.R}}"/></view></view></view><view wx:elif="{{field.S}}" class="input-container select-other-container"><picker mode="selector" range="{{field.X}}" bindchange="{{field.Y}}"><view class="{{['picker-input', field.W && 'input-error']}}"><text wx:if="{{field.T}}" class="picker-text">{{field.U}}</text><text wx:else class="picker-text placeholder">{{field.V}}</text><text class="picker-arrow">▼</text></view></picker><input wx:if="{{field.Z}}" class="form-input other-input-full" placeholder="请输入其他内容" value="{{field.aa}}" bindinput="{{field.ab}}"/></view><view wx:elif="{{field.ac}}" class="input-container"><picker mode="date" bindchange="{{field.ah}}"><view class="{{['picker-input', field.ag && 'input-error']}}"><text wx:if="{{field.ad}}" class="picker-text">{{field.ae}}</text><text wx:else class="picker-text placeholder">{{field.af}}</text><text class="picker-arrow">📅</text></view></picker></view><view wx:elif="{{field.ai}}" class="input-container"><view class="image-upload-container"><view class="image-upload-area" bindtap="{{field.am}}"><button class="upload-btn">{{field.aj}}</button><text class="upload-hint">{{field.ak}}</text><text wx:if="{{field.al}}" class="upload-status">✓</text></view></view></view></view><view class="submit-section"><view class="submit-btn" bindtap="{{f}}"><text class="submit-text">提交报名</text></view></view></view><view wx:if="{{g}}" class="empty-container"><text class="empty-text">暂无报名表单配置</text></view></view>