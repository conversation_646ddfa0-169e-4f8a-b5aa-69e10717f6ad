.more-videos-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #d3e2ff, #fff);
  padding-bottom: 120rpx;
  overflow-x: hidden;
  box-sizing: border-box;
}
/* 内容区域 */
.content-area {
  position: relative;
  z-index: 0;
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}
/* 视频区域 */
.section-block {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}
.videos-block {
  margin-bottom: 30rpx;
}
.videos-content {
  position: relative;
  z-index: 20;
}
.video-item {
  width: 100%;
  margin-bottom: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.video-item:last-child {
  margin-bottom: 0;
}
.video-wrapper {
  position: relative;
  width: 100%;
  height: 400rpx;
  background: #000;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}
.play-btn-custom {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}
.play-icon {
  font-size: 48rpx;
  color: #003399;
  margin-left: 8rpx;
}
.video-info {
  padding: 30rpx;
  background: #fff;
}
.video-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
  line-height: 1.4;
}
.video-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
  line-height: 1.5;
}
.video-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}
/* 空状态样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 30rpx;
  position: relative;
  z-index: 20;
}
.videos-empty {
  padding: 100rpx 30rpx;
}
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  text-align: center;
}
/* 加载状态样式 */
.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
