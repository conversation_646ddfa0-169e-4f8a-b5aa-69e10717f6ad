"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      // 禁用分享混入的自动检测功能，因为这是分享页面本身
      _disableShareDetection: true,
      activityData: {
        title: "",
        description: "",
        location: "",
        startTime: "",
        endTime: "",
        coverImage: "",
        eventId: ""
      },
      qrcodeSize: 240,
      // 小程序码大小（进一步增大）
      sharePath: "",
      // 分享路径
      shareCanvasWidth: 375,
      // 分享图片宽度
      shareCanvasHeight: 800,
      // 分享图片高度（增加高度以容纳封面图）
      realQRCodePath: null,
      // 真实小程序码的本地路径
      qrCodeLoading: false,
      // 小程序码加载状态
      qrCodeCache: {}
      // 小程序码缓存
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/share/activity-share.vue:115", "📤 分享页面加载参数:", options);
    if (options.data) {
      try {
        this.activityData = JSON.parse(decodeURIComponent(options.data));
        this.sharePath = `/pages/competition/guidance-detail?type=${options.eventType || "guidance"}&eventId=${this.activityData.eventId}`;
        this.getRealQRCode();
        this.$nextTick(() => {
          this.generateQRCode();
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/share/activity-share.vue:133", "📤 解析活动数据失败:", error);
        common_vendor.index.showToast({
          title: "数据解析失败",
          icon: "none"
        });
      }
    }
  },
  // 设置页面标题
  onReady() {
    common_vendor.index.setNavigationBarTitle({
      title: "活动分享"
    });
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return this.activityData.title || "精彩活动分享";
    },
    // 自定义分享路径
    getSharePath() {
      return this.sharePath || "/pages/index/home";
    },
    // 自定义分享图片（使用活动封面图）
    getShareImageUrl() {
      return this.processServerImageUrl(
        this.activityData.coverImage,
        this.getImagePath("share-logo.png")
      );
    },
    // 自定义分享内容
    getShareContent() {
      return `活动分享 - ${this.activityData.title || "精彩活动"}`;
    },
    // 获取真实小程序码
    async getRealQRCode() {
      try {
        common_vendor.index.__f__("log", "at pages/share/activity-share.vue:180", "🔲 开始获取真实小程序码...");
        common_vendor.index.__f__("log", "at pages/share/activity-share.vue:181", "🔲 分享路径:", this.sharePath);
        common_vendor.index.__f__("log", "at pages/share/activity-share.vue:182", "🔲 活动ID:", this.activityData.eventId);
        const cacheKey = `qrcode_${this.activityData.eventId}`;
        if (this.qrCodeCache[cacheKey]) {
          common_vendor.index.__f__("log", "at pages/share/activity-share.vue:187", "🔲 使用缓存的小程序码");
          this.realQRCodePath = this.qrCodeCache[cacheKey];
          return;
        }
        this.qrCodeLoading = true;
        const response = await utils_request.request.post("/miniapp/event/getCustomQRCode", {
          path: this.sharePath
          // 传递完整的路径，包含参数
        });
        common_vendor.index.__f__("log", "at pages/share/activity-share.vue:199", "🔲 自定义小程序码接口响应:", response);
        if (response && response.data && response.data.code === 200) {
          const qrCodeBase64 = response.data.data.qrcode;
          common_vendor.index.__f__("log", "at pages/share/activity-share.vue:204", "🔲 后端返回小程序码base64:", qrCodeBase64 ? "已获取" : "未获取");
          if (qrCodeBase64) {
            common_vendor.index.__f__("log", "at pages/share/activity-share.vue:208", "🔲 直接使用base64小程序码");
            this.realQRCodePath = qrCodeBase64;
            this.qrCodeCache[cacheKey] = qrCodeBase64;
            return;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/share/activity-share.vue:218", "🔲 获取真实小程序码失败:", error);
        common_vendor.index.__f__("log", "at pages/share/activity-share.vue:219", "🔲 将使用模拟的二维码图案作为备用方案");
        this.realQRCodePath = null;
      } finally {
        this.qrCodeLoading = false;
      }
    },
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr)
        return "";
      const date = new Date(dateTimeStr);
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${month}月${day}日 ${hours}:${minutes}`;
    },
    // 格式化日期时间范围
    formatDateTimeRange(startTime, endTime) {
      if (!startTime)
        return "";
      const start = this.formatDateTime(startTime);
      const end = this.formatDateTime(endTime);
      if (end) {
        return `${start} ~ ${end}`;
      }
      return start;
    },
    // 生成小程序码
    generateQRCode() {
      const ctx = common_vendor.index.createCanvasContext("qrcode", this);
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(0, 0, this.qrcodeSize, this.qrcodeSize);
      ctx.setStrokeStyle("#e0e0e0");
      ctx.setLineWidth(2);
      ctx.strokeRect(0, 0, this.qrcodeSize, this.qrcodeSize);
      this.drawSimpleQRPattern(ctx);
      const centerSize = 30;
      const centerX = (this.qrcodeSize - centerSize) / 2;
      const centerY = (this.qrcodeSize - centerSize) / 2;
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(centerX, centerY, centerSize, centerSize);
      ctx.setStrokeStyle("#2a5298");
      ctx.setLineWidth(2);
      ctx.strokeRect(centerX, centerY, centerSize, centerSize);
      ctx.setFillStyle("#2a5298");
      ctx.setFontSize(12);
      ctx.setTextAlign("center");
      ctx.fillText("海棠", this.qrcodeSize / 2, this.qrcodeSize / 2 + 4);
      ctx.draw();
    },
    // 绘制简单的二维码图案
    drawSimpleQRPattern(ctx) {
      const blockSize = 4;
      const margin = 10;
      const patternSize = this.qrcodeSize - margin * 2;
      const blocksPerRow = Math.floor(patternSize / blockSize);
      ctx.setFillStyle("#000000");
      for (let i = 0; i < blocksPerRow; i++) {
        for (let j = 0; j < blocksPerRow; j++) {
          const seed = (this.activityData.eventId || "").toString();
          const hash = this.simpleHash(seed + i + j);
          if (hash % 3 === 0) {
            const x = margin + i * blockSize;
            const y = margin + j * blockSize;
            ctx.fillRect(x, y, blockSize - 1, blockSize - 1);
          }
        }
      }
      this.drawCornerPattern(ctx, margin, margin);
      this.drawCornerPattern(ctx, this.qrcodeSize - margin - 28, margin);
      this.drawCornerPattern(ctx, margin, this.qrcodeSize - margin - 28);
    },
    // 绘制定位角图案
    drawCornerPattern(ctx, x, y) {
      ctx.setFillStyle("#000000");
      ctx.fillRect(x, y, 28, 28);
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(x + 4, y + 4, 20, 20);
      ctx.setFillStyle("#000000");
      ctx.fillRect(x + 8, y + 8, 12, 12);
    },
    // 简单哈希函数
    simpleHash(str) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash;
      }
      return Math.abs(hash);
    },
    // 预览二维码
    previewQRCode() {
      if (this.realQRCodePath) {
        common_vendor.index.previewImage({
          urls: [this.realQRCodePath],
          current: this.realQRCodePath
        });
      } else {
        common_vendor.index.canvasToTempFilePath({
          canvasId: "qrcode",
          success: (res) => {
            common_vendor.index.previewImage({
              urls: [res.tempFilePath],
              current: res.tempFilePath
            });
          },
          fail: (error) => {
            common_vendor.index.__f__("error", "at pages/share/activity-share.vue:359", "生成预览图片失败:", error);
            common_vendor.index.showToast({
              title: "预览失败",
              icon: "none"
            });
          }
        }, this);
      }
    },
    // 保存到相册
    async saveToAlbum() {
      try {
        common_vendor.index.showLoading({ title: "生成中..." });
        const authResult = await common_vendor.index.authorize({
          scope: "scope.writePhotosAlbum"
        });
        const shareImagePath = await this.generateShareImage();
        await common_vendor.index.saveImageToPhotosAlbum({
          filePath: shareImagePath
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/share/activity-share.vue:395", "📤 保存到相册失败:", error);
        if (error.errMsg && error.errMsg.includes("auth deny")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "需要授权保存图片到相册",
            confirmText: "去设置",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      }
    },
    // 生成分享图片
    async generateShareImage() {
      return new Promise(async (resolve, reject) => {
        try {
          let coverImagePath = null;
          let qrCodePath = null;
          if (this.activityData.coverImage) {
            const coverImageUrl2 = this.processServerImageUrl(this.activityData.coverImage);
            try {
              const downloadResult = await new Promise((resolve2, reject2) => {
                const timeout = setTimeout(() => {
                  reject2(new Error("下载超时"));
                }, 15e3);
                common_vendor.index.downloadFile({
                  url: coverImageUrl2,
                  success: (res) => {
                    clearTimeout(timeout);
                    resolve2(res);
                  },
                  fail: (err) => {
                    clearTimeout(timeout);
                    reject2(err);
                  }
                });
              });
              if (downloadResult.statusCode === 200 && downloadResult.tempFilePath) {
                const fs = common_vendor.index.getFileSystemManager();
                try {
                  await new Promise((resolve2, reject2) => {
                    fs.stat({
                      path: downloadResult.tempFilePath,
                      success: resolve2,
                      fail: reject2
                    });
                  });
                  await new Promise((resolve2, reject2) => {
                    common_vendor.index.getImageInfo({
                      src: downloadResult.tempFilePath,
                      success: resolve2,
                      fail: reject2
                    });
                  });
                  coverImagePath = downloadResult.tempFilePath;
                } catch (verifyError) {
                }
              }
            } catch (error) {
              try {
                const imageInfo = await new Promise((resolve2, reject2) => {
                  common_vendor.index.getImageInfo({
                    src: coverImageUrl2,
                    success: resolve2,
                    fail: reject2
                  });
                });
                coverImagePath = imageInfo.path;
              } catch (backupError) {
              }
            }
          }
          if (this.realQRCodePath) {
            if (this.realQRCodePath.startsWith("data:image")) {
              try {
                const base64Data = this.realQRCodePath.split(",")[1];
                const fs = common_vendor.index.getFileSystemManager();
                const tempPath = `${common_vendor.index.env.USER_DATA_PATH}/temp_qrcode_${Date.now()}.png`;
                await new Promise((resolve2, reject2) => {
                  fs.writeFile({
                    filePath: tempPath,
                    data: base64Data,
                    encoding: "base64",
                    success: resolve2,
                    fail: reject2
                  });
                });
                qrCodePath = tempPath;
              } catch (error) {
              }
            } else {
              qrCodePath = this.realQRCodePath;
            }
          }
          const ctx = common_vendor.index.createCanvasContext("shareCanvas", this);
          const { shareCanvasWidth: width, shareCanvasHeight: height } = this;
          const bgGradient = ctx.createLinearGradient(0, 0, 0, height);
          bgGradient.addColorStop(0, "#f5f5f5");
          bgGradient.addColorStop(1, "#f5f5f5");
          ctx.setFillStyle(bgGradient);
          ctx.fillRect(0, 0, width, height);
          const cardPadding = 0;
          const cardWidth = width - cardPadding * 2;
          const cardHeight = height - cardPadding * 2;
          const borderRadius = 12;
          this.drawRoundedRect(ctx, cardPadding, cardPadding, cardWidth, cardHeight, borderRadius, "#ffffff");
          let currentY = cardPadding;
          const coverHeight = 200;
          const coverPadding = 0;
          const coverWidth = cardWidth - coverPadding * 2;
          const coverX = cardPadding + coverPadding;
          const coverRadius = 0;
          const coverImageUrl = this.processServerImageUrl(this.activityData.coverImage, this.getImagePath("haitang-block1.png"));
          if (coverImagePath) {
            try {
              ctx.drawImage(coverImagePath, coverX, currentY, coverWidth, coverHeight);
              ctx.setStrokeStyle("#e0e0e0");
              ctx.setLineWidth(2);
              this.createRoundedPath(ctx, coverX, currentY, coverWidth, coverHeight, coverRadius);
              ctx.stroke();
            } catch (error) {
              this.drawRoundedRect(ctx, coverX, currentY, coverWidth, coverHeight, coverRadius, "#667eea");
              ctx.setFillStyle("#ffffff");
              ctx.setFontSize(16);
              ctx.setTextAlign("center");
              ctx.fillText("活动封面", coverX + coverWidth / 2, currentY + coverHeight / 2);
            }
          } else {
            this.drawRoundedRect(ctx, coverX, currentY, coverWidth, coverHeight, coverRadius, "#667eea");
            ctx.setFillStyle("#ffffff");
            ctx.setFontSize(16);
            ctx.setTextAlign("center");
            ctx.fillText("活动封面", coverX + coverWidth / 2, currentY + coverHeight / 2);
          }
          currentY += coverHeight + 35;
          const title = this.activityData.title || "活动分享";
          ctx.setFillStyle("#1a1a1a");
          ctx.setFontSize(20);
          ctx.setTextAlign("left");
          const titleLines = this.wrapText(ctx, title, cardWidth - 60);
          titleLines.forEach((line, index) => {
            ctx.fillText(line, 30, currentY + index * 32);
          });
          currentY += titleLines.length * 32 + 20;
          const timeText = this.formatDateTimeRange(this.activityData.startTime, this.activityData.endTime);
          ctx.setFillStyle("#667eea");
          ctx.setFontSize(16);
          ctx.fillText("🕐", 30, currentY);
          ctx.setFillStyle("#555555");
          ctx.setFontSize(15);
          ctx.fillText(timeText, 55, currentY);
          currentY += 30;
          if (this.activityData.location) {
            ctx.setFillStyle("#667eea");
            ctx.setFontSize(16);
            ctx.fillText("📍", 30, currentY);
            ctx.setFillStyle("#555555");
            ctx.setFontSize(15);
            ctx.fillText(this.activityData.location, 55, currentY);
            currentY += 30;
          }
          if (this.activityData.description) {
            ctx.setFillStyle("#777777");
            ctx.setFontSize(13);
            const description = this.activityData.description;
            const maxWidth = cardWidth - 60;
            const lines = this.wrapText(ctx, description, maxWidth);
            const maxDescLines = 2;
            const displayLines = lines.slice(0, maxDescLines);
            displayLines.forEach((line, index) => {
              ctx.fillText(line, 30, currentY + index * 20);
            });
            currentY += displayLines.length * 20 + 25;
          }
          const lineY = currentY;
          ctx.setStrokeStyle("#f0f0f0");
          ctx.setLineWidth(1);
          ctx.beginPath();
          ctx.moveTo(30, lineY);
          ctx.lineTo(cardWidth - 30, lineY);
          ctx.stroke();
          currentY += 25;
          const qrSize = 100;
          const qrX = cardWidth - qrSize - 30;
          const qrY = currentY;
          this.drawRoundedRect(ctx, qrX - 4, qrY - 4, qrSize + 8, qrSize + 8, 6, "#ffffff");
          if (qrCodePath) {
            try {
              ctx.drawImage(qrCodePath, qrX, qrY, qrSize, qrSize);
            } catch (error) {
              this.drawSimpleQRPatternOnCanvas(ctx, qrX, qrY, qrSize);
            }
          } else {
            this.drawSimpleQRPatternOnCanvas(ctx, qrX, qrY, qrSize);
          }
          ctx.setFillStyle("#333333");
          ctx.setFontSize(14);
          ctx.setTextAlign("left");
          ctx.fillText("长按识别小程序码", 30, currentY + 25);
          ctx.setFillStyle("#888888");
          ctx.setFontSize(12);
          ctx.fillText("查看活动详情", 30, currentY + 45);
          ctx.draw(false, () => {
            setTimeout(() => {
              common_vendor.index.canvasToTempFilePath({
                canvasId: "shareCanvas",
                success: (res) => {
                  resolve(res.tempFilePath);
                },
                fail: (error) => {
                  reject(error);
                }
              });
            }, 1e3);
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/share/activity-share.vue:696", "生成分享图片失败:", error);
          reject(error);
        }
      });
    },
    // 文本换行处理
    wrapText(ctx, text, maxWidth) {
      const words = text.split("");
      const lines = [];
      let currentLine = "";
      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + words[i];
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;
        if (testWidth > maxWidth && i > 0) {
          lines.push(currentLine);
          currentLine = words[i];
        } else {
          currentLine = testLine;
        }
      }
      lines.push(currentLine);
      return lines;
    },
    // 绘制圆角矩形
    drawRoundedRect(ctx, x, y, width, height, radius, fillStyle) {
      ctx.setFillStyle(fillStyle);
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
    },
    // 创建圆角路径（用于剪切）
    createRoundedPath(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
    },
    // 在指定位置绘制二维码图案 - 现代化设计
    drawSimpleQRPatternOnCanvas(ctx, x, y, size) {
      const blockSize = size / 10;
      const radius = blockSize * 0.2;
      this.drawRoundedRect(ctx, x, y, blockSize * 3, blockSize * 3, radius * 2, "#1a1a1a");
      this.drawRoundedRect(ctx, x + blockSize, y + blockSize, blockSize, blockSize, radius, "#ffffff");
      this.drawRoundedRect(ctx, x + size - blockSize * 3, y, blockSize * 3, blockSize * 3, radius * 2, "#1a1a1a");
      this.drawRoundedRect(ctx, x + size - blockSize * 2, y + blockSize, blockSize, blockSize, radius, "#ffffff");
      this.drawRoundedRect(ctx, x, y + size - blockSize * 3, blockSize * 3, blockSize * 3, radius * 2, "#1a1a1a");
      this.drawRoundedRect(ctx, x + blockSize, y + size - blockSize * 2, blockSize, blockSize, radius, "#ffffff");
      const positions = [
        [4, 2],
        [5, 2],
        [6, 2],
        [2, 4],
        [4, 4],
        [6, 4],
        [8, 4],
        [2, 5],
        [5, 5],
        [7, 5],
        [3, 6],
        [4, 6],
        [8, 6],
        [2, 7],
        [6, 7],
        [7, 7],
        [4, 8],
        [5, 8],
        [6, 8],
        [8, 8]
      ];
      positions.forEach(([col, row]) => {
        this.drawRoundedRect(ctx, x + col * blockSize, y + row * blockSize, blockSize, blockSize, radius, "#1a1a1a");
      });
      const centerSize = blockSize * 2.5;
      const centerX = x + (size - centerSize) / 2;
      const centerY = y + (size - centerSize) / 2;
      this.drawRoundedRect(ctx, centerX, centerY, centerSize, centerSize, radius * 3, "#ffffff");
      ctx.setStrokeStyle("#667eea");
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.moveTo(centerX + radius * 3, centerY);
      ctx.lineTo(centerX + centerSize - radius * 3, centerY);
      ctx.quadraticCurveTo(centerX + centerSize, centerY, centerX + centerSize, centerY + radius * 3);
      ctx.lineTo(centerX + centerSize, centerY + centerSize - radius * 3);
      ctx.quadraticCurveTo(centerX + centerSize, centerY + centerSize, centerX + centerSize - radius * 3, centerY + centerSize);
      ctx.lineTo(centerX + radius * 3, centerY + centerSize);
      ctx.quadraticCurveTo(centerX, centerY + centerSize, centerX, centerY + centerSize - radius * 3);
      ctx.lineTo(centerX, centerY + radius * 3);
      ctx.quadraticCurveTo(centerX, centerY, centerX + radius * 3, centerY);
      ctx.closePath();
      ctx.stroke();
      ctx.setFillStyle("#667eea");
      ctx.setFontSize(12);
      ctx.setTextAlign("center");
      ctx.fillText("海棠", centerX + centerSize / 2, centerY + centerSize / 2 + 4);
    },
    // 分享给好友
    shareToFriend() {
      common_vendor.index.showModal({
        title: "分享提示",
        content: '请点击右上角的"..."按钮，选择"转发"来分享给好友',
        showCancel: false,
        confirmText: "知道了"
      });
    },
    // 显示海报选项
    showPosterOptions() {
      this.saveToAlbum();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.processServerImageUrl($data.activityData.coverImage, $options.getImagePath("haitang-block1.png")),
    b: common_vendor.t($data.activityData.title),
    c: common_vendor.t($options.formatDateTimeRange($data.activityData.startTime, $data.activityData.endTime)),
    d: $data.activityData.location
  }, $data.activityData.location ? {
    e: common_vendor.t($data.activityData.location)
  } : {}, {
    f: $data.activityData.description
  }, $data.activityData.description ? {
    g: common_vendor.t($data.activityData.description)
  } : {}, {
    h: $data.realQRCodePath
  }, $data.realQRCodePath ? {
    i: $data.realQRCodePath
  } : $data.qrCodeLoading ? {} : {
    k: $data.qrcodeSize + "px",
    l: $data.qrcodeSize + "px"
  }, {
    j: $data.qrCodeLoading,
    m: common_vendor.o((...args) => $options.previewQRCode && $options.previewQRCode(...args)),
    n: common_vendor.o((...args) => $options.showPosterOptions && $options.showPosterOptions(...args)),
    o: common_vendor.o((...args) => $options.shareToFriend && $options.shareToFriend(...args)),
    p: $data.shareCanvasWidth + "px",
    q: $data.shareCanvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d52e2873"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/share/activity-share.js.map
