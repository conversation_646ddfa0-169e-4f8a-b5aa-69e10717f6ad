"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      richTextContent: "",
      // 富文本内容
      processedRichTextContent: "",
      // 处理后的富文本内容
      loading: true,
      showContactPopup: false,
      // 是否显示联系人弹窗
      contactInfo: {
        // 联系人信息
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:91", "🎭 西青金种子路演页面加载");
    common_vendor.index.setNavigationBarTitle({
      title: "西青金种子路演"
    });
    this.loadRoadshowContent();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 西青金种子路演";
    },
    // 自定义分享内容
    getShareContent() {
      return "西青金种子路演分享";
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载路演内容
    async loadRoadshowContent() {
      try {
        common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:124", "🎭 开始获取西青金种子路演内容...");
        common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:125", "🎭 API地址: GET /miniapp/xiqing/activity/app/getEnabledContent");
        this.loading = true;
        const response = await utils_request.request.get("/miniapp/xiqing/activity/app/getEnabledContent");
        common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:130", "🎭 路演内容API响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const contentData = response.data.data;
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:134", "🎭 路演内容数据:", contentData);
          if (contentData && typeof contentData === "string") {
            this.richTextContent = contentData;
            this.processedRichTextContent = this.processRichTextContent(contentData);
          } else if (contentData && contentData.content) {
            this.richTextContent = contentData.content;
            this.processedRichTextContent = this.processRichTextContent(contentData.content);
          } else {
            common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:146", "🎭 没有找到内容数据，使用默认内容");
            this.generateDefaultContent();
          }
        } else {
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:150", "🎭 ❌ 路演内容数据获取失败！");
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:151", "🎭 响应详情:", response);
          this.generateDefaultContent();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/xiqing/roadshow.vue:155", "🎭 获取路演内容失败:", error);
        this.generateDefaultContent();
        common_vendor.index.showToast({
          title: "路演内容加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理富文本内容，确保图片样式正确
    processRichTextContent(content) {
      if (!content) {
        return "";
      }
      common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:174", "🎭 原始HTML内容:", content);
      let processedContent = content.replace(
        /<img([^>]*?)>/gi,
        '<img$1 style="max-width: 100% !important; width: auto !important; height: auto !important; display: block !important; margin: 10rpx 0 !important;">'
      );
      processedContent = processedContent.replace(
        /<img([^>]*?)style\s*=\s*["']([^"']*?)["']([^>]*?)>/gi,
        '<img$1style="$2; max-width: 100% !important; width: auto !important; height: auto !important; display: block !important; margin: 10rpx 0 !important;"$3>'
      );
      common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:188", "🎭 处理后的HTML内容:", processedContent);
      return processedContent;
    },
    // 生成默认内容
    generateDefaultContent() {
      const defaultContent = `
				<div style="padding: 30px; line-height: 1.8; color: #333; font-size: 16px;">
					<h2 style="color: #003399; margin-bottom: 20px; font-size: 24px; font-weight: 600;">西青金种子路演</h2>
					<p style="margin-bottom: 20px; text-indent: 2em;">欢迎参加西青金种子路演活动，这里是创新创业者展示项目、寻找投资的重要平台。</p>

					<h3 style="color: #4A90E2; margin: 25px 0 15px 0; font-size: 20px; font-weight: 500;">🎯 活动特色</h3>
					<ul style="margin-left: 30px; margin-bottom: 20px;">
						<li style="margin-bottom: 8px;">✅ 专业的投资机构参与</li>
						<li style="margin-bottom: 8px;">✅ 权威的专家评审团队</li>
						<li style="margin-bottom: 8px;">✅ 丰富的创业资源对接</li>
						<li style="margin-bottom: 8px;">✅ 全方位的项目展示平台</li>
						<li style="margin-bottom: 8px;">✅ 优质的后续孵化服务</li>
					</ul>

					<h3 style="color: #4A90E2; margin: 25px 0 15px 0; font-size: 20px; font-weight: 500;">📋 参与流程</h3>
					<p style="margin-bottom: 15px;">1️⃣ <strong>项目报名</strong>：提交项目申请材料</p>
					<p style="margin-bottom: 15px;">2️⃣ <strong>初步筛选</strong>：专家团队项目评估</p>
					<p style="margin-bottom: 15px;">3️⃣ <strong>路演准备</strong>：项目优化和路演培训</p>
					<p style="margin-bottom: 20px;">4️⃣ <strong>正式路演</strong>：现场展示和投资对接</p>

					<div style="background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); padding: 20px; border-radius: 12px; margin-top: 25px;">
						<p style="margin: 0; color: #E65100; font-weight: 500; text-align: center;">🌟 西青金种子路演，助力创业梦想起航！</p>
					</div>
				</div>
			`;
      this.richTextContent = defaultContent;
      this.processedRichTextContent = this.processRichTextContent(defaultContent);
    },
    // 路演报名
    roadshowSignup() {
      common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:226", "🎭 路演报名 - 跳转到报名页面");
      common_vendor.index.navigateTo({
        url: "/pages/xiqing/register"
      });
    },
    // 报名咨询
    async signupConsult() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:234", "🎭 报名咨询");
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:243", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:260", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:266", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "金种子路演报名咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/xiqing/roadshow.vue:279", "🎭 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        success: () => {
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:314", "拨打电话成功:", phoneNumber);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/xiqing/roadshow.vue:317", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    },
    // 长按二维码扫码
    scanQRCode() {
      if (!this.contactInfo.qrCodeUrl) {
        common_vendor.index.showToast({
          title: "暂无二维码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.previewImage({
        urls: [this.contactInfo.qrCodeUrl],
        current: this.contactInfo.qrCodeUrl,
        success: () => {
          common_vendor.index.__f__("log", "at pages/xiqing/roadshow.vue:341", "预览二维码成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/xiqing/roadshow.vue:344", "预览二维码失败:", err);
          common_vendor.index.showToast({
            title: "预览失败",
            icon: "none"
          });
        }
      });
    },
    // 拨打电话
    makeCall() {
      if (!this.contactInfo.contactPhone) {
        common_vendor.index.showToast({
          title: "暂无联系电话",
          icon: "none"
        });
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: this.contactInfo.contactPhone,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/xiqing/roadshow.vue:366", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.processedRichTextContent
  }, $data.processedRichTextContent ? {
    b: $data.processedRichTextContent
  } : $data.loading ? {} : {}, {
    c: $data.loading,
    d: common_vendor.o((...args) => $options.roadshowSignup && $options.roadshowSignup(...args)),
    e: common_vendor.o((...args) => $options.signupConsult && $options.signupConsult(...args)),
    f: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    g: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    h: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    i: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    j: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    k: $data.contactInfo.qrCodeUrl,
    l: common_vendor.o((...args) => $options.scanQRCode && $options.scanQRCode(...args))
  } : {}, {
    m: common_vendor.o(() => {
    }),
    n: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/xiqing/roadshow.js.map
