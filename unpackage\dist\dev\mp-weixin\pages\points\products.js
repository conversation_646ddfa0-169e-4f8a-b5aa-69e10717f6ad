"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      productType: "",
      // 商品类型：consulting 或 gifts
      pageTitle: "商品",
      goodsList: [],
      // 真实商品数据
      loading: false
    };
  },
  computed: {
    // 根据商品类型返回对应的商品列表
    currentProducts() {
      return this.goodsList;
    }
  },
  onLoad(options) {
    this.productType = options.type || "gifts";
    if (this.productType === "consulting") {
      this.pageTitle = "咨询服务";
    } else if (this.productType === "gifts") {
      this.pageTitle = "文创礼品";
    }
    common_vendor.index.setNavigationBarTitle({
      title: "积分商城-" + this.pageTitle
    });
    this.loadGoodsList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 处理服务器图片URL
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/points/products.vue:104", "🎯 商品图片加载失败:", e);
    },
    // 获取商品列表
    async loadGoodsList() {
      var _a;
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/points/products.vue:112", "🎯 获取商品列表...", { productType: this.productType });
        const params = {};
        if (this.productType === "consulting") {
          params.goodsCategory = "consultation_service";
        } else if (this.productType === "gifts") {
          params.goodsCategory = "cultural_gift";
        }
        const response = await utils_request.request.get("/miniapp/points/goods/app/list", params);
        if (response && response.data && response.data.code === 200) {
          this.goodsList = response.data.data || [];
          this.goodsList = this.goodsList.map((item) => ({
            ...item,
            // 确保 productImageUrl 字段存在
            productImageUrl: item.productImageUrl || item.goodsImage || ""
          }));
        } else {
          this.goodsList = [];
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "获取商品列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/products.vue:144", "🎯 获取商品列表失败:", error);
        this.goodsList = [];
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 查看商品详情
    viewProductDetail(product) {
      common_vendor.index.navigateTo({
        url: `/pages/points/product-detail?id=${product.goodsId}&type=${this.productType}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.loading
  }, !$data.loading ? {
    b: common_vendor.f($options.currentProducts, (product, index, i0) => {
      return common_vendor.e({
        a: product.productImageUrl
      }, product.productImageUrl ? {
        b: $options.processServerImageUrl(product.productImageUrl),
        c: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), product.goodsId)
      } : {}, {
        d: common_vendor.t(product.goodsName),
        e: common_vendor.t(product.remark || "暂无描述"),
        f: common_vendor.t(product.pointsRequired),
        g: product.goodsId,
        h: common_vendor.o(($event) => $options.viewProductDetail(product), product.goodsId)
      });
    })
  } : {}, {
    c: $data.loading
  }, $data.loading ? {} : {}, {
    d: !$data.loading && $options.currentProducts.length === 0
  }, !$data.loading && $options.currentProducts.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/products.js.map
