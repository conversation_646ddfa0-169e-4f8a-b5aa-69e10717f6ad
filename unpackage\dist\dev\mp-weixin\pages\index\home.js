"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const ProfileGuideModal = () => "../../components/ProfileGuideModal.js";
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  components: {
    ProfileGuideModal
  },
  data() {
    return {
      // 个人资料引导弹窗
      showProfileGuide: false,
      // 弹幕控制
      danmuTimer: null,
      danmuCycle: 0,
      // 弹幕循环计数器，用于强制重新渲染
      /**
       * 弹幕配置参数（通过POST /miniapp/barrage/app/getConfig接口获取）
       *
       * 🚀 速度配置：
       * - speed: 弹幕滚动持续时间(秒) - 数值越小滚动越快
       *   例如：speed=4表示4秒内从右滚到左，speed=8表示8秒内滚动完成
       *
       * 📏 密度配置：
       * - horizontalDensity: 弹幕发送间隔时间(秒) - 数值越小弹幕越密集
       *   例如：horizontalDensity=2表示每2秒发送一条，horizontalDensity=6表示每6秒发送一条
       * - verticalDensity: 弹幕显示行数 - 可选值：1(单行)、2(双行)、3(三行)
       *
       * 📐 布局配置：
       * - lineHeight: 弹幕行间距(rpx) - 控制弹幕行之间的垂直距离
       * - startTop: 第一行位置(rpx) - 第一行弹幕距离顶部的距离
       * - bufferTime: 循环等待时间(秒) - 一轮弹幕结束后等待多久开始下一轮
       */
      danmuConfig: {
        // 🚀 弹幕速度配置
        speed: 8,
        // 默认8秒：弹幕滚动持续时间(数值越小越快)
        // 📏 弹幕密度配置
        horizontalDensity: 6,
        // 默认6秒：弹幕发送间隔(数值越小越密集)
        verticalDensity: 2,
        // 默认2行：弹幕显示行数(1/2行，排列顺序：下-上)
        // 📐 弹幕布局配置
        lineHeight: 50,
        // 默认90rpx：弹幕行间距
        startTop: 30,
        // 默认20rpx：第一行弹幕位置
        bufferTime: 8
        // 默认8秒：循环等待时间
      },
      // 弹幕输入
      showDanmuModal: false,
      danmuInputText: "",
      danmuSending: false,
      // 弹幕发送状态
      // 科技之星数据
      currentStarIndex: 0,
      // 当前科技之星索引
      starList: [],
      // 从接口获取数据
      // 轮播图数据
      bannerList: [],
      // 精彩活动数据
      activityList: [],
      // 从接口获取数据
      // 加入我们数据
      jobList: [],
      // 从接口获取数据
      // 通知相关数据
      noticeList: [],
      // 通知列表
      currentNoticeIndex: 0,
      // 当前显示的通知索引
      noticeText: "正在加载通知信息...",
      // 当前显示的通知文本（用于无通知时显示）
      // 用户信息
      userInfo: {},
      // 多条用户信息弹幕 - 初始化为空数组，避免渲染时序问题
      danmuList: [],
      // 页面加载标记
      isFirstLoad: true,
      // 功能模块数据
      moduleList: [],
      // 功能模块列表
      moduleLoading: false
      // 功能模块加载状态
    };
  },
  computed: {
    // 计算滑块偏移量
    indicatorOffset() {
      const maxIndex = this.starList.length - 1;
      if (maxIndex === 0)
        return "0rpx";
      const trackWidth = 600;
      const thumbWidth = 90;
      const edgeMargin = 0;
      const moveableDistance = trackWidth - thumbWidth - edgeMargin * 2;
      const offsetPx = edgeMargin + this.currentStarIndex / maxIndex * moveableDistance;
      common_vendor.index.__f__("log", "at pages/index/home.vue:392", `滑块位置调试: 当前索引=${this.currentStarIndex}, 轨道宽度=${trackWidth}rpx, 计算位置=${offsetPx}rpx`);
      return offsetPx + "rpx";
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/index/home.vue:397", "📱 页面开始加载...", options);
    this.initData();
    this.getModuleList();
    this.getNoticeList();
    this.getTechStarList();
    this.getActivityList();
    this.getJobList();
    common_vendor.index.__f__("log", "at pages/index/home.vue:410", "📱 页面加载完成");
    if (options && options.autoBarrage === "true") {
      common_vendor.index.__f__("log", "at pages/index/home.vue:414", "🎯 检测到autoBarrage参数，将自动显示弹幕输入框");
      setTimeout(() => {
        this.showDanmuInput();
      }, 500);
    }
    this.isFirstLoad = false;
  },
  onReady() {
    common_vendor.index.__f__("log", "at pages/index/home.vue:426", "📱 页面渲染完成");
    common_vendor.index.__f__("log", "at pages/index/home.vue:429", "🎬 延迟1000ms启动弹幕动画...");
    setTimeout(() => {
      this.startDanmu();
    }, 1e3);
    setTimeout(() => {
      this.checkShowProfileGuide();
    }, 2e3);
  },
  onShow() {
    common_vendor.index.__f__("log", "at pages/index/home.vue:440", "📱 页面显示");
    if (!this.isFirstLoad) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:443", "📱 从其他页面返回 - 刷新科技之星数据");
      this.getTechStarList();
    }
  },
  onUnload() {
    if (this.danmuTimer) {
      clearTimeout(this.danmuTimer);
      this.danmuTimer = null;
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 校友创新创业平台";
    },
    // 自定义分享内容
    getShareContent() {
      return "首页分享";
    },
    // 获取功能模块列表
    async getModuleList() {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:473", "🔧 开始获取功能模块列表...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:474", "🔧 API地址: GET /miniapp/homemodule/app/getModuleList");
        this.moduleLoading = true;
        const response = await utils_request.request.get("/miniapp/homemodule/app/getModuleList");
        common_vendor.index.__f__("log", "at pages/index/home.vue:479", "🔧 功能模块API响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const moduleData = response.data.data;
          common_vendor.index.__f__("log", "at pages/index/home.vue:483", "🔧 功能模块数据获取成功:", moduleData);
          if (Array.isArray(moduleData) && moduleData.length > 0) {
            this.moduleList = moduleData.map((item, index) => ({
              ...item,
              // 确保有必要的字段
              id: item.id || `module_${index}`,
              title: item.moduleName || `功能${index + 1}`,
              icon: item.moduleIcon || null,
              jumpUrl: item.moduleUrl || null,
              moduleCode: item.moduleCode || null,
              sortOrder: item.sortOrder || index,
              status: item.status || "enabled"
            }));
            this.moduleList.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
            common_vendor.index.__f__("log", "at pages/index/home.vue:502", "🔧 处理后的功能模块列表:", this.moduleList);
          } else {
            common_vendor.index.__f__("log", "at pages/index/home.vue:504", "🔧 ⚠️ 功能模块数据为空，使用默认配置");
            this.moduleList = [];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:508", "🔧 ❌ 功能模块数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/home.vue:509", "🔧 错误信息:", ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "未知错误");
          this.moduleList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:513", "🔧 获取功能模块列表失败:", error);
        this.moduleList = [];
      } finally {
        this.moduleLoading = false;
      }
    },
    // 初始化数据
    async initData() {
      common_vendor.index.__f__("log", "at pages/index/home.vue:522", "🚀 开始初始化数据");
      this.loadBannerList();
      common_vendor.index.__f__("log", "at pages/index/home.vue:528", "📊 准备获取弹幕配置...");
      await this.getDanmuConfig();
      common_vendor.index.__f__("log", "at pages/index/home.vue:530", "📊 弹幕配置获取完成");
      common_vendor.index.__f__("log", "at pages/index/home.vue:532", "✅ 数据初始化完成（弹幕将在页面渲染完成后启动）");
    },
    // 获取通知列表
    async getNoticeList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:538", "📢 开始获取通知列表...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:539", "📢 API地址: /miniapp/notice/app/getEnabledList");
        const response = await utils_request.request.post("/miniapp/notice/app/getEnabledList");
        if (response.data.code === 200) {
          this.noticeList = response.data.data;
          common_vendor.index.__f__("log", "at pages/index/home.vue:544", "📢 通知列表获取成功，共", this.noticeList.length, "条通知");
          common_vendor.index.__f__("log", "at pages/index/home.vue:545", "📢 通知详情:", this.noticeList);
          if (this.noticeList.length > 0) {
            this.currentNoticeIndex = 0;
            this.noticeText = this.noticeList[0].title || "暂无通知标题";
            common_vendor.index.__f__("log", "at pages/index/home.vue:552", "📢 设置初始通知:", this.noticeText);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:555", "📢 ❌ 条件检查失败！");
          this.noticeList = [];
          this.noticeText = "暂无通知信息";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:560", "📢 获取通知列表失败:", error);
        this.noticeList = [];
        this.noticeText = "通知加载失败，请稍后重试";
      }
    },
    // 通知swiper切换事件
    onNoticeChange(e) {
      this.currentNoticeIndex = e.detail.current;
      const currentNotice = this.noticeList[this.currentNoticeIndex];
      common_vendor.index.__f__("log", "at pages/index/home.vue:571", `📢 通知切换到第${this.currentNoticeIndex + 1}条:`, (currentNotice == null ? void 0 : currentNotice.title) || "暂无通知标题");
    },
    // 加载轮播图数据
    loadBannerList() {
      common_vendor.index.__f__("log", "at pages/index/home.vue:576", "开始加载轮播图数据");
      utils_request.request.post("/miniapp/banner/app/getEnabledList").then((result) => {
        common_vendor.index.__f__("log", "at pages/index/home.vue:580", "轮播图数据响应:", result);
        if (result && result.success && result.data && result.data.code === 200) {
          const bannerData = result.data.data;
          if (Array.isArray(bannerData) && bannerData.length > 0) {
            this.bannerList = bannerData.map((item) => ({
              id: item.bannerId,
              image: utils_imageUtils.processServerImageUrl(item.imageUrl || item.image || item.bannerUrl),
              title: item.title || item.name || "",
              link: item.linkUrl || item.url || "",
              sort: item.sortOrder || 0
            }));
            common_vendor.index.__f__("log", "at pages/index/home.vue:595", "轮播图数据加载成功:", this.bannerList);
          } else {
            common_vendor.index.__f__("log", "at pages/index/home.vue:597", "轮播图数据为空或格式不正确");
            this.bannerList = [];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:601", "轮播图数据加载失败:", result);
          this.bannerList = [];
          if (result && result.message) {
            common_vendor.index.showToast({
              title: result.message,
              icon: "none",
              duration: 2e3
            });
          }
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/home.vue:615", "轮播图数据请求失败:", error);
        this.bannerList = [];
        common_vendor.index.showToast({
          title: "轮播图加载失败",
          icon: "none",
          duration: 2e3
        });
      });
    },
    // 获取科技之星列表
    async getTechStarList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:629", "⭐ 开始获取科技之星列表...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:630", "⭐ API地址: GET /miniapp/techstar/enabled");
        const response = await utils_request.request.get("/miniapp/techstar/enabled");
        if (response && response.success && response.data && response.data.code === 200) {
          let starData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/home.vue:635", "⭐ 原始科技之星数据:", starData);
          if (Array.isArray(starData) && starData.length > 5) {
            starData = starData.slice(0, 5);
            common_vendor.index.__f__("log", "at pages/index/home.vue:640", "⭐ 数据超过5条，已截取最新5条");
          }
          this.starList = starData.map((item) => ({
            id: item.id || item.starId,
            name: item.name || item.realName || "暂无姓名",
            description1: item.description1 || "暂无介绍",
            description2: item.description2 || "暂无介绍",
            avatar: utils_imageUtils.processServerImageUrl(item.coverUrl, utils_imageUtils.getImagePath("avatar.png")),
            // 保留原始数据用于详情页
            rawData: item
          }));
          common_vendor.index.__f__("log", "at pages/index/home.vue:654", "⭐ 科技之星列表获取成功，共", this.starList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/home.vue:655", "⭐ 处理后的科技之星数据:", this.starList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:657", "⭐ ❌ 科技之星数据获取失败！");
          this.starList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:661", "⭐ 获取科技之星列表失败:", error);
        this.starList = [];
      }
    },
    // 获取精彩活动列表
    async getActivityList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:669", "🎉 开始获取精彩活动列表...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:670", "🎉 API地址: POST /miniapp/activity/app/getEnabledList");
        const response = await utils_request.request.post("/miniapp/activity/app/getEnabledList");
        if (response && response.success && response.data && response.data.code === 200) {
          let activityData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/home.vue:675", "🎉 原始精彩活动数据:", activityData);
          if (Array.isArray(activityData) && activityData.length > 3) {
            activityData = activityData.slice(0, 3);
            common_vendor.index.__f__("log", "at pages/index/home.vue:680", "🎉 数据超过3条，已截取最新3条");
          }
          this.activityList = activityData.map((item) => ({
            id: item.id || item.activityId,
            title: item.title || item.name || "暂无标题",
            desc: item.description || item.content || item.summary || "暂无描述",
            time: item.createTime || item.publishTime || item.activityTime || "暂无时间",
            image: utils_imageUtils.processServerImageUrl(item.coverImage, utils_imageUtils.getImagePath("default-activity.png")),
            articleUrl: item.articleUrl
          }));
          common_vendor.index.__f__("log", "at pages/index/home.vue:693", "🎉 精彩活动列表获取成功，共", this.activityList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/home.vue:694", "🎉 处理后的精彩活动数据:", this.activityList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:696", "🎉 ❌ 精彩活动数据获取失败！");
          this.activityList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:700", "🎉 获取精彩活动列表失败:", error);
        this.activityList = [];
      }
    },
    // 获取职位列表
    async getJobList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:708", "💼 开始获取职位列表...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:709", "💼 API地址: GET /miniapp/job/enabled");
        const response = await utils_request.request.get("/miniapp/job/enabled");
        if (response && response.success && response.data && response.data.code === 200) {
          let jobData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/home.vue:714", "💼 原始职位数据:", jobData);
          if (Array.isArray(jobData)) {
            if (jobData.length > 3) {
              jobData = jobData.slice(0, 3);
              common_vendor.index.__f__("log", "at pages/index/home.vue:720", "💼 数据超过3条，已截取最新3条");
            } else {
              common_vendor.index.__f__("log", "at pages/index/home.vue:722", "💼 数据小于等于3条，显示所有", jobData.length, "条数据");
            }
          }
          this.jobList = jobData.map((item) => ({
            id: item.id || item.jobId,
            title: item.title || item.jobTitle || item.name || "暂无职位",
            salary: item.salary || item.salaryRange || "面议",
            company: item.company || item.companyName || "暂无公司信息",
            tags: item.jobTags ? item.jobTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0) : ["暂无标签信息"],
            date: item.createTime || item.publishTime || item.postTime || "暂无时间",
            location: item.location || item.workLocation || item.city || "暂无地点",
            detailUrl: item.detailUrl || item.jobUrl,
            // 职位详情链接
            companyScale: item.companyScale || "暂无规模",
            // 保留原始数据用于详情页
            rawData: item
          }));
          common_vendor.index.__f__("log", "at pages/index/home.vue:741", "💼 职位列表获取成功，共", this.jobList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/home.vue:742", "💼 处理后的职位数据:", this.jobList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:744", "💼 ❌ 职位数据获取失败！");
          this.jobList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:748", "💼 获取职位列表失败:", error);
        this.jobList = [];
      }
    },
    // 轮播图点击事件
    onBannerClick(banner) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:756", "点击轮播图:", banner);
      if (banner.link) {
        if (banner.link.startsWith("http")) {
          this.openInWebview(banner.link, banner.title || "详情");
        } else if (banner.link.startsWith("/pages/")) {
          common_vendor.index.navigateTo({
            url: banner.link
          });
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:769", "未知链接类型:", banner.link);
          common_vendor.index.showToast({
            title: "链接格式不正确",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.showToast({
          title: banner.title || "轮播图",
          icon: "none"
        });
      }
    },
    // 在webview中打开链接
    openInWebview(url, title = "详情") {
      if (!url || !url.startsWith("http")) {
        common_vendor.index.showToast({
          title: "链接格式不正确",
          icon: "none"
        });
        return;
      }
      const encodedUrl = encodeURIComponent(url);
      const encodedTitle = encodeURIComponent(title);
      common_vendor.index.navigateTo({
        url: `/pages/webview/webview?url=${encodedUrl}&title=${encodedTitle}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/home.vue:803", "跳转webview失败:", err);
          this.fallbackToCopyLink(url);
        }
      });
    },
    // 降级处理：复制链接
    fallbackToCopyLink(url) {
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          common_vendor.index.showModal({
            title: "提示",
            content: "链接已复制到剪贴板，请在浏览器中打开",
            showCancel: false,
            confirmText: "知道了"
          });
        },
        fail: () => {
          common_vendor.index.showModal({
            title: "外部链接",
            content: url,
            confirmText: "复制链接",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.setClipboardData({
                  data: url,
                  success: () => {
                    common_vendor.index.showToast({
                      title: "链接已复制",
                      icon: "success"
                    });
                  }
                });
              }
            }
          });
        }
      });
    },
    // 轮播图图片加载错误处理
    onBannerImageError(e) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:848", "轮播图图片加载失败:", e);
      common_vendor.index.showToast({
        title: "图片加载失败",
        icon: "none",
        duration: 1500
      });
    },
    // 启动弹幕动画
    async startDanmu() {
      common_vendor.index.__f__("log", "at pages/index/home.vue:858", "🎬 启动弹幕动画...");
      if (this.danmuTimer) {
        clearTimeout(this.danmuTimer);
      }
      await this.resetDanmuList();
      if (!this.danmuList || this.danmuList.length === 0) {
        common_vendor.index.__f__("log", "at pages/index/home.vue:870", "🎬 弹幕列表为空，10秒后重试获取");
        this.danmuTimer = setTimeout(() => {
          this.startDanmu();
        }, 1e4);
        return;
      }
      this.danmuCycle++;
      common_vendor.index.__f__("log", "at pages/index/home.vue:879", `🎬 弹幕循环计数器更新为: ${this.danmuCycle}`);
      common_vendor.index.__f__("log", "at pages/index/home.vue:882", `🎬 开始设置${this.danmuList.length}条弹幕参数，配置:`, this.danmuConfig);
      this.danmuList.forEach((danmu, index) => {
        danmu.delay = index * this.danmuConfig.horizontalDensity;
        danmu.duration = this.danmuConfig.speed;
        const level = this.getDanmuLevel(index);
        danmu.top = this.danmuConfig.startTop + level * this.danmuConfig.lineHeight;
        common_vendor.index.__f__("log", "at pages/index/home.vue:892", `🎯 弹幕${index}: 用户=${danmu.user}, 延迟=${danmu.delay}s, 层级=${level}, top=${danmu.top}rpx`);
      });
      this.validateDanmuLevels();
      const lastStartTime = (this.danmuList.length - 1) * this.danmuConfig.horizontalDensity;
      const duration = this.danmuConfig.speed;
      const bufferTime = this.danmuConfig.bufferTime;
      const cycleTime = (lastStartTime + duration + bufferTime) * 1e3;
      this.danmuTimer = setTimeout(() => {
        this.startDanmu();
      }, cycleTime);
    },
    // 获取弹幕层级，根据竖向密度配置分配层级
    // 排列顺序：下-上（1行=下面，2行=下+上）
    getDanmuLevel(index) {
      const verticalDensity = this.danmuConfig.verticalDensity;
      let levelPattern;
      switch (verticalDensity) {
        case 1:
          levelPattern = [2];
          break;
        case 2:
          levelPattern = [2, 0];
          break;
        case 3:
        default:
          levelPattern = [1, 2, 0];
          break;
      }
      const level = levelPattern[index % levelPattern.length];
      common_vendor.index.__f__("log", "at pages/index/home.vue:936", `🎯 弹幕${index}分配到层级${level}，verticalDensity=${verticalDensity}`);
      return level;
    },
    // 验证弹幕层级分配是否正确
    validateDanmuLevels() {
      const verticalDensity = this.danmuConfig.verticalDensity;
      let hasInvalidLevel = false;
      this.danmuList.forEach((danmu, index) => {
        const level = this.getDanmuLevel(index);
        let validLevels = [];
        switch (verticalDensity) {
          case 1:
            validLevels = [2];
            break;
          case 2:
            validLevels = [0, 2];
            break;
          case 3:
            validLevels = [0, 1, 2];
            break;
        }
        if (!validLevels.includes(level)) {
          common_vendor.index.__f__("error", "at pages/index/home.vue:963", `❌ 弹幕${index}层级${level}无效！verticalDensity=${verticalDensity}，有效层级:${validLevels}`);
          hasInvalidLevel = true;
        }
      });
      if (!hasInvalidLevel) {
        common_vendor.index.__f__("log", "at pages/index/home.vue:969", "✅ 弹幕层级分配验证通过");
      }
      return !hasInvalidLevel;
    },
    // 获取弹幕列表
    async getDanmuList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:978", "开始获取弹幕列表...");
        const result = await utils_request.request.post("/miniapp/barrage/app/getApprovedList");
        common_vendor.index.__f__("log", "at pages/index/home.vue:981", "弹幕列表API响应:", result);
        if (result && result.success && result.data && result.data.code === 200) {
          const danmuData = result.data.data || [];
          const formattedDanmuList = danmuData.map((item) => ({
            user: item.nickname || item.userNickName || "匿名用户",
            time: item.createTime || item.publishTime || (/* @__PURE__ */ new Date()).toISOString(),
            tag: item.content || item.message || "",
            avatar: item.userAvatarUrl || utils_imageUtils.getImagePath("avatar.png"),
            duration: 10,
            delay: 0,
            top: 0
          }));
          common_vendor.index.__f__("log", "at pages/index/home.vue:998", "弹幕列表获取成功，共", formattedDanmuList.length, "条");
          return formattedDanmuList;
        } else {
          common_vendor.index.__f__("warn", "at pages/index/home.vue:1003", "弹幕列表API返回异常:", result);
          return this.getDefaultDanmuList();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:1008", "获取弹幕列表失败:", error);
        return this.getDefaultDanmuList();
      }
    },
    // 获取默认弹幕列表
    getDefaultDanmuList() {
      return [];
    },
    // 获取弹幕配置
    async getDanmuConfig() {
      try {
        common_vendor.index.__f__("log", "at pages/index/home.vue:1023", "🔧 开始调用弹幕配置接口...");
        common_vendor.index.__f__("log", "at pages/index/home.vue:1024", "🔧 接口地址: POST /miniapp/barrage/app/getConfig");
        const result = await utils_request.request.post("/miniapp/barrage/app/getConfig");
        common_vendor.index.__f__("log", "at pages/index/home.vue:1027", "🔧 弹幕配置API响应:", result);
        if (result && result.success && result.data && result.data.code === 200) {
          const configData = result.data.data || {};
          common_vendor.index.__f__("log", "at pages/index/home.vue:1031", "🔧 获取到的原始配置数据:", configData);
          const mappedConfig = {
            // 后端字段映射
            speed: configData.speed,
            // 后端speed -> 前端speed
            horizontalDensity: configData.interval,
            // 后端interval -> 前端horizontalDensity
            verticalDensity: configData.rows,
            // 后端rows -> 前端verticalDensity
            // 布局配置（后端暂未提供，使用默认值）
            lineHeight: configData.lineHeight,
            startTop: configData.startTop,
            bufferTime: configData.bufferTime
          };
          common_vendor.index.__f__("log", "at pages/index/home.vue:1046", "🔧 字段映射结果:", mappedConfig);
          this.danmuConfig = {
            // 弹幕速度配置
            speed: mappedConfig.speed || this.danmuConfig.speed,
            // 弹幕密度配置
            horizontalDensity: mappedConfig.horizontalDensity || this.danmuConfig.horizontalDensity,
            verticalDensity: mappedConfig.verticalDensity || this.danmuConfig.verticalDensity,
            // 弹幕布局配置
            lineHeight: mappedConfig.lineHeight || this.danmuConfig.lineHeight,
            startTop: mappedConfig.startTop || this.danmuConfig.startTop,
            bufferTime: mappedConfig.bufferTime || this.danmuConfig.bufferTime
          };
          common_vendor.index.__f__("log", "at pages/index/home.vue:1063", "弹幕配置更新成功:", this.danmuConfig);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1066", "📊 弹幕配置详情:");
          common_vendor.index.__f__("log", "at pages/index/home.vue:1067", `🔧 后端原始数据: speed=${configData.speed}, interval=${configData.interval}, rows=${configData.rows}`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1068", `🚀 弹幕速度: ${this.danmuConfig.speed}秒滚动时间 (数值越小滚动越快)`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1069", `📏 横向密度: 每${this.danmuConfig.horizontalDensity}秒发送一条 (数值越小越密集)`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1070", `📐 竖向密度: ${this.danmuConfig.verticalDensity}行显示`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1071", `📍 行间距: ${this.danmuConfig.lineHeight}rpx`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1072", `⬆️ 起始位置: ${this.danmuConfig.startTop}rpx`);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1073", `⏱️ 循环间隔: ${this.danmuConfig.bufferTime}秒等待时间`);
        } else {
          common_vendor.index.__f__("warn", "at pages/index/home.vue:1076", "获取弹幕配置失败，使用默认配置:", this.danmuConfig);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:1080", "获取弹幕配置失败:", error);
        common_vendor.index.__f__("log", "at pages/index/home.vue:1081", "使用默认弹幕配置:", this.danmuConfig);
      }
    },
    // 更新弹幕配置（供动态调整使用）
    updateDanmuConfig(newConfig) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1087", "更新弹幕配置:", newConfig);
      if (newConfig.verticalDensity && ![1, 2, 3].includes(newConfig.verticalDensity)) {
        common_vendor.index.__f__("warn", "at pages/index/home.vue:1091", "竖向密度参数无效，必须是1、2或3，当前值:", newConfig.verticalDensity);
        newConfig.verticalDensity = 2;
      }
      this.danmuConfig = {
        ...this.danmuConfig,
        ...newConfig
      };
      common_vendor.index.__f__("log", "at pages/index/home.vue:1101", "弹幕配置已更新:", this.danmuConfig);
      this.startDanmu();
    },
    // 获取当前弹幕配置信息（用于调试和监控）
    getCurrentDanmuConfig() {
      return {
        // 速度配置
        speed: this.danmuConfig.speed,
        speedDescription: `弹幕滚动时间: ${this.danmuConfig.speed}秒（数值越小滚动越快）`,
        // 密度配置
        horizontalDensity: this.danmuConfig.horizontalDensity,
        horizontalDensityDescription: `弹幕发送间隔: 每${this.danmuConfig.horizontalDensity}秒一条（数值越小越密集）`,
        verticalDensity: this.danmuConfig.verticalDensity,
        verticalDensityDescription: `弹幕显示行数: ${this.danmuConfig.verticalDensity}行`,
        // 布局配置
        lineHeight: this.danmuConfig.lineHeight,
        startTop: this.danmuConfig.startTop,
        bufferTime: this.danmuConfig.bufferTime,
        // 配置总结
        summary: `速度${this.danmuConfig.speed}秒，每${this.danmuConfig.horizontalDensity}秒发送，${this.danmuConfig.verticalDensity}行显示`
      };
    },
    // 重置弹幕列表
    async resetDanmuList() {
      const newDanmuList = await this.getDanmuList();
      if (Array.isArray(newDanmuList) && newDanmuList.length > 0) {
        common_vendor.index.__f__("log", "at pages/index/home.vue:1138", `🔄 重置弹幕列表，获取到${newDanmuList.length}条数据`);
        common_vendor.index.__f__("log", "at pages/index/home.vue:1139", "🔄 弹幕数据顺序检查:", newDanmuList.map((item, index) => `${index}: ${item.user}`));
        newDanmuList.forEach((danmu, index) => {
          danmu.delay = danmu.delay || 0;
          danmu.duration = danmu.duration || this.danmuConfig.speed || 10;
          danmu.top = danmu.top || this.danmuConfig.startTop || 20;
        });
        this.danmuList = newDanmuList;
      } else {
        common_vendor.index.__f__("log", "at pages/index/home.vue:1151", "🔄 弹幕数据为空或无效，清空当前列表");
        this.danmuList = [];
      }
    },
    // 刷新弹幕列表（发送成功后调用）
    async refreshDanmuList() {
      try {
        const newDanmuList = await this.getDanmuList();
        this.danmuList = newDanmuList;
        setTimeout(() => {
          this.startDanmu();
        }, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:1172", "刷新弹幕列表失败:", error);
      }
    },
    // 重置弹幕
    resetDanmu() {
      if (this.danmuTimer) {
        clearTimeout(this.danmuTimer);
      }
    },
    // 页面卸载时清除定时器
    onUnload() {
      clearTimeout(this.danmuTimer);
    },
    // 功能模块图片加载失败处理
    onModuleImageError(module2, index) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1191", `🔧 功能模块图片加载失败: ${module2.title}，使用默认图片`);
      this.$set(this.moduleList, index, {
        ...module2,
        icon: null
        // 设置为null，让processServerImageUrl使用默认图片
      });
    },
    // 根据模块编码进行跳转
    navigateByModuleCode(moduleCode) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1202", "🔧 根据模块编码跳转:", moduleCode);
      switch (moduleCode) {
        case "roadshow":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1206", "🔧 跳转到创赛路演页面");
          common_vendor.index.navigateTo({
            url: "/pages/index/competition"
          });
          break;
        case "activity_signup":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1214", "🔧 跳转到活动报名页面");
          common_vendor.index.navigateTo({
            url: "/pages/competition/guidance?type=activity"
          });
          break;
        case "park_entry":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1222", "🔧 跳转到园区入驻页面");
          common_vendor.index.navigateTo({
            url: "/pages/garden/index"
          });
          break;
        case "project_investment":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1230", "🔧 跳转到项目投资页面");
          common_vendor.index.navigateTo({
            url: "/pages/index/project-investment"
          });
          break;
        case "join_us":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1238", "🔧 跳转到关于我们页面");
          common_vendor.index.navigateTo({
            url: "/pages/index/about-us"
          });
          break;
        case "points_mall":
          common_vendor.index.__f__("log", "at pages/index/home.vue:1246", "🔧 跳转到积分商城页面");
          common_vendor.index.navigateTo({
            url: "/pages/points/mall"
          });
          break;
        default:
          common_vendor.index.__f__("log", "at pages/index/home.vue:1254", "🔧 未知的模块编码:", moduleCode);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1255", "🔧 模块数据:", module);
          common_vendor.index.showToast({
            title: module || "功能开发中",
            icon: "none"
          });
          break;
      }
    },
    // 检查是否需要显示个人资料完善引导弹窗（异步版本）
    async checkShowProfileGuide() {
      try {
        const shouldShow = await utils_profileCheck.shouldShowProfileGuideAsync();
        if (shouldShow) {
          this.showProfileGuide = true;
          utils_profileCheck.markProfileGuideShown();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:1273", "📝 检查引导弹窗显示状态失败:", error);
      }
    },
    // 关闭引导弹窗
    closeProfileGuide() {
      this.showProfileGuide = false;
    },
    // 前往个人资料页面
    goToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/index/profile?from=home"
      });
    },
    // 导航到我的页面
    navigateToMine() {
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 导航到需求广场页面
    navigateToDemandSquare() {
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    },
    // 导航到人脉资源页面
    navigateToContacts() {
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts-new"
      });
    },
    // 导航到产业资源页面
    navigateToIndustry() {
      common_vendor.index.reLaunch({
        url: "/pages/industry/index"
      });
    },
    // 通用导航方法 - 处理"查看更多"按钮的跳转
    navigateTo(page) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1319", "🔗 导航到页面:", page);
      switch (page) {
        case "moreStar":
          common_vendor.index.navigateTo({
            url: "/pages/index/more-star"
          });
          break;
        case "moreActivities":
          common_vendor.index.navigateTo({
            url: "/pages/index/more-activities"
          });
          break;
        case "moreJobs":
          common_vendor.index.navigateTo({
            url: "/pages/index/more-jobs"
          });
          break;
        default:
          common_vendor.index.__f__("log", "at pages/index/home.vue:1344", "🔗 未知的页面标识:", page);
          common_vendor.index.showToast({
            title: "页面开发中",
            icon: "none"
          });
          break;
      }
    },
    // 查看活动详情
    viewActivityDetail(activity) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1355", "点击精彩活动:", activity);
      if (activity.articleUrl) {
        if (activity.articleUrl.startsWith("http")) {
          this.openInWebview(activity.articleUrl, activity.title || "活动详情");
        } else if (activity.articleUrl.startsWith("/pages/")) {
          common_vendor.index.navigateTo({
            url: activity.articleUrl
          });
        } else {
          common_vendor.index.__f__("log", "at pages/index/home.vue:1369", "未知链接类型:", activity.articleUrl);
          common_vendor.index.showToast({
            title: "链接格式不正确",
            icon: "none"
          });
        }
      } else {
        common_vendor.index.showToast({
          title: "暂无详情链接",
          icon: "none"
        });
      }
    },
    // 查看职位详情
    viewJobDetail(job) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1386", "点击职位:", job);
      const jobData = encodeURIComponent(JSON.stringify(job.rawData || job));
      common_vendor.index.navigateTo({
        url: `/pages/index/job-detail?jobData=${jobData}`
      });
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1399", "图片加载失败:", e);
      common_vendor.index.showToast({
        title: "图片加载失败",
        icon: "none"
      });
    },
    // 科技之星轮播图切换
    onStarSwiperChange(e) {
      this.currentStarIndex = e.detail.current;
    },
    // 查看科技之星详情
    viewStarDetail(star) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1413", "点击科技之星:", star);
      const starData = encodeURIComponent(JSON.stringify(star.rawData || star));
      common_vendor.index.navigateTo({
        url: `/pages/index/star-detail?starData=${starData}`
      });
    },
    // 查看科技之星详情
    viewStarDetail(star) {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1426", "点击科技之星:", star);
      const starData = encodeURIComponent(JSON.stringify(star.rawData || star));
      common_vendor.index.navigateTo({
        url: `/pages/index/star-detail?starData=${starData}`
      });
    },
    // 显示弹幕输入弹窗
    showDanmuInput() {
      this.showDanmuModal = true;
      this.danmuInputText = "";
    },
    // 隐藏弹幕输入弹窗
    hideDanmuInput() {
      this.showDanmuModal = false;
    },
    // 发送弹幕
    async sendDanmu() {
      var _a;
      if (!this.danmuInputText.trim()) {
        common_vendor.index.showToast({
          title: "弹幕内容不能为空",
          icon: "none"
        });
        return;
      }
      if (this.danmuSending) {
        return;
      }
      this.danmuSending = true;
      common_vendor.index.showLoading({
        title: "获取用户信息..."
      });
      try {
        const userProfileResult = await utils_request.request.get("/miniapp/user/getProfileDetail");
        common_vendor.index.__f__("log", "at pages/index/home.vue:1474", "获取用户资料响应:", userProfileResult);
        let realName = "匿名用户";
        let portraitUrl = "";
        let userId = 0;
        if (userProfileResult && userProfileResult.data && userProfileResult.data.code === 200) {
          const profileData = userProfileResult.data.data || {};
          const basicInfo = profileData.basicInfo || {};
          common_vendor.index.__f__("log", "at pages/index/home.vue:1484", "API返回的完整数据:", userProfileResult.data);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1485", "profileData:", profileData);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1486", "basicInfo:", basicInfo);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1487", "basicInfo.realName:", basicInfo.realName);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1488", "basicInfo.portraitUrl:", basicInfo.portraitUrl);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1489", "basicInfo.userId:", basicInfo.userId);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1490", "profileData.userId:", profileData.userId);
          realName = basicInfo.realName || "匿名用户";
          portraitUrl = utils_imageUtils.processServerImageUrl(basicInfo.portraitUrl || "");
          userId = basicInfo.userId || profileData.userId || 0;
          common_vendor.index.__f__("log", "at pages/index/home.vue:1499", "用户信息获取成功:", { realName, portraitUrl, userId });
        } else {
          common_vendor.index.__f__("warn", "at pages/index/home.vue:1501", "获取用户资料失败，使用默认信息");
          common_vendor.index.__f__("log", "at pages/index/home.vue:1502", "API调用失败，返回结果:", userProfileResult);
        }
        if (userId === 0) {
          const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
          common_vendor.index.__f__("log", "at pages/index/home.vue:1508", "本地存储的userInfo:", userInfo);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1509", "userInfo.userId:", userInfo.userId);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1510", "userInfo.id:", userInfo.id);
          common_vendor.index.__f__("log", "at pages/index/home.vue:1511", "userInfo.user_id:", userInfo.user_id);
          userId = userInfo.userId || userInfo.id || userInfo.user_id || 0;
          if (realName === "匿名用户") {
            realName = userInfo.realName || "匿名用户";
          }
          if (!portraitUrl) {
            portraitUrl = utils_imageUtils.processServerImageUrl(userInfo.portraitUrl || "");
          }
          common_vendor.index.__f__("log", "at pages/index/home.vue:1524", "从本地存储补充信息:", { realName, portraitUrl, userId });
        }
        const barrageData = {
          content: this.danmuInputText.trim(),
          userNickName: realName,
          userAvatarUrl: portraitUrl,
          userId
        };
        common_vendor.index.__f__("log", "at pages/index/home.vue:1535", "最终发送弹幕数据:", barrageData);
        common_vendor.index.__f__("log", "at pages/index/home.vue:1536", "realName来源检查:", realName);
        common_vendor.index.__f__("log", "at pages/index/home.vue:1537", "portraitUrl来源检查:", portraitUrl);
        common_vendor.index.showLoading({
          title: "发送中..."
        });
        const result = await utils_request.request.post("/miniapp/barrage/app/publish", barrageData);
        common_vendor.index.__f__("log", "at pages/index/home.vue:1546", "弹幕发送响应:", result);
        common_vendor.index.hideLoading();
        this.danmuSending = false;
        if (result && result.success && result.data && result.data.code === 200) {
          common_vendor.index.showToast({
            title: "弹幕发送成功！",
            icon: "success"
          });
          this.danmuInputText = "";
          this.hideDanmuInput();
          this.refreshDanmuList();
        } else {
          const errorMsg = ((_a = result == null ? void 0 : result.data) == null ? void 0 : _a.msg) || (result == null ? void 0 : result.message) || "弹幕发送失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/home.vue:1577", "弹幕发送过程失败:", error);
        common_vendor.index.hideLoading();
        this.danmuSending = false;
        common_vendor.index.showToast({
          title: "网络错误，发送失败",
          icon: "none"
        });
      }
    },
    // 加载弹幕列表（可选功能，如果后端提供获取弹幕列表的接口）
    loadDanmuList() {
      common_vendor.index.__f__("log", "at pages/index/home.vue:1593", "开始加载弹幕列表");
    }
  }
};
if (!Array) {
  const _component_ProfileGuideModal = common_vendor.resolveComponent("ProfileGuideModal");
  _component_ProfileGuideModal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("top_img.png"),
    b: $options.getImagePath("tips_btn.png"),
    c: common_vendor.o((...args) => $options.showDanmuInput && $options.showDanmuInput(...args)),
    d: common_vendor.f($data.danmuList, (danmu, index, i0) => {
      return {
        a: danmu.avatar,
        b: common_vendor.t(danmu.user),
        c: common_vendor.t(danmu.time),
        d: common_vendor.t(danmu.tag),
        e: $data.danmuCycle + "_" + index,
        f: (danmu.duration || 10) + "s",
        g: (danmu.delay || 0) + "s",
        h: (danmu.top || 20) + "rpx"
      };
    }),
    e: $data.showDanmuModal
  }, $data.showDanmuModal ? {
    f: common_vendor.o((...args) => $options.hideDanmuInput && $options.hideDanmuInput(...args)),
    g: $data.danmuInputText,
    h: common_vendor.o(($event) => $data.danmuInputText = $event.detail.value),
    i: common_vendor.t($data.danmuInputText.length),
    j: common_vendor.o((...args) => $options.hideDanmuInput && $options.hideDanmuInput(...args)),
    k: common_vendor.t($data.danmuSending ? "发送中..." : "发送"),
    l: $data.danmuSending ? 1 : "",
    m: common_vendor.o((...args) => $options.sendDanmu && $options.sendDanmu(...args)),
    n: !$data.danmuInputText.trim() || $data.danmuSending,
    o: common_vendor.o(() => {
    }),
    p: common_vendor.o((...args) => $options.hideDanmuInput && $options.hideDanmuInput(...args))
  } : {}, {
    q: $data.bannerList.length > 0
  }, $data.bannerList.length > 0 ? {
    r: common_vendor.f($data.bannerList, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.o(($event) => $options.onBannerClick(item), index),
        c: common_vendor.o((...args) => $options.onBannerImageError && $options.onBannerImageError(...args), index),
        d: index
      };
    })
  } : {}, {
    s: $options.getImagePath("notice_icon.png"),
    t: $data.noticeList.length > 0
  }, $data.noticeList.length > 0 ? {
    v: common_vendor.f($data.noticeList, (notice, index, i0) => {
      return {
        a: common_vendor.t(notice.title || "暂无通知标题"),
        b: index
      };
    }),
    w: common_vendor.o((...args) => $options.onNoticeChange && $options.onNoticeChange(...args))
  } : {
    x: common_vendor.t($data.noticeText)
  }, {
    y: common_vendor.f($data.moduleList, (module2, index, i0) => {
      return {
        a: $options.processServerImageUrl(module2.icon),
        b: common_vendor.o(($event) => $options.onModuleImageError(module2, index), module2.id || index),
        c: common_vendor.n(`module-${index}`),
        d: common_vendor.t(module2.title),
        e: module2.id || index,
        f: common_vendor.o(($event) => $options.navigateByModuleCode(module2.moduleCode), module2.id || index)
      };
    }),
    z: $data.moduleLoading && $data.moduleList.length === 0
  }, $data.moduleLoading && $data.moduleList.length === 0 ? {} : {}, {
    A: $options.getImagePath("list_bg.png"),
    B: common_vendor.o(($event) => $options.navigateTo("moreStar")),
    C: $data.starList && $data.starList.length > 0
  }, $data.starList && $data.starList.length > 0 ? {
    D: common_vendor.f($data.starList, (star, index, i0) => {
      return {
        a: star.avatar,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), index),
        c: common_vendor.t(star.name),
        d: common_vendor.t(star.description1),
        e: common_vendor.t(star.description1),
        f: common_vendor.o(($event) => $options.viewStarDetail(star), index),
        g: index
      };
    }),
    E: common_vendor.o((...args) => $options.onStarSwiperChange && $options.onStarSwiperChange(...args)),
    F: "translateX(" + $options.indicatorOffset + ")"
  } : {
    G: $options.getImagePath("icon7.png")
  }, {
    H: $options.getImagePath("list_bg.png"),
    I: common_vendor.o(($event) => $options.navigateTo("moreActivities")),
    J: $data.activityList && $data.activityList.length > 0
  }, $data.activityList && $data.activityList.length > 0 ? {
    K: common_vendor.f($data.activityList, (activity, index, i0) => {
      return {
        a: activity.image,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), activity.id || index),
        c: common_vendor.t(activity.title),
        d: common_vendor.t(activity.desc),
        e: common_vendor.t(activity.time),
        f: activity.id || index,
        g: common_vendor.o(($event) => $options.viewActivityDetail(activity), activity.id || index)
      };
    })
  } : {
    L: $options.getImagePath("icon8.png")
  }, {
    M: $options.getImagePath("list_bg.png"),
    N: common_vendor.o(($event) => $options.navigateTo("moreJobs")),
    O: $data.jobList && $data.jobList.length > 0
  }, $data.jobList && $data.jobList.length > 0 ? {
    P: common_vendor.f($data.jobList, (job, index, i0) => {
      return {
        a: common_vendor.t(job.title),
        b: common_vendor.t(job.salary),
        c: common_vendor.t(job.company),
        d: common_vendor.f(job.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        e: common_vendor.t(job.date),
        f: common_vendor.t(job.location),
        g: job.id || index,
        h: common_vendor.o(($event) => $options.viewJobDetail(job), job.id || index)
      };
    })
  } : {
    Q: $options.getImagePath("order_icon5.png")
  }, {
    R: $options.getImagePath("bottom_order_icon1_active.png"),
    S: $options.getImagePath("bottom_order_icon2.png"),
    T: common_vendor.o((...args) => $options.navigateToContacts && $options.navigateToContacts(...args)),
    U: $options.getImagePath("bottom_order_icon.png"),
    V: common_vendor.o((...args) => $options.navigateToDemandSquare && $options.navigateToDemandSquare(...args)),
    W: $options.getImagePath("bottom_order_icon3.png"),
    X: common_vendor.o((...args) => $options.navigateToIndustry && $options.navigateToIndustry(...args)),
    Y: $options.getImagePath("bottom_order_icon4.png"),
    Z: common_vendor.o((...args) => $options.navigateToMine && $options.navigateToMine(...args)),
    aa: common_vendor.o($options.closeProfileGuide),
    ab: common_vendor.o($options.goToProfile),
    ac: common_vendor.p({
      visible: $data.showProfileGuide
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/home.js.map
