"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      industryName: "产业图谱",
      industrySubtitle: "",
      industryIcon: "",
      industryId: null,
      // 产业ID
      activeStream: "upstream",
      // 当前激活的流向
      totalEnterpriseCount: 0,
      // 总企业数量
      hasStreamType: false,
      // 是否有上中下游分类
      // 产业细分数据 - 初始为空，从接口获取
      industryData: {
        upstream: [],
        midstream: [],
        downstream: []
      },
      // 企业信息解锁弹框
      showUnlockDialog: false,
      unlockDialogImage: null,
      // 弹框背景图
      currentSubsection: null,
      // 当前点击的三级分类
      isIndustryUnlocked: false,
      // 产业是否已解锁
      // 联系人信息弹窗
      showContactPopup: false,
      contactInfo: {
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  async onLoad(options) {
    if (options.industryName) {
      this.industryName = decodeURIComponent(options.industryName);
    }
    if (options.industryId) {
      this.industryId = options.industryId;
    }
    const hasPermission = await this.checkAccessPermission();
    if (!hasPermission) {
      common_vendor.index.__f__("log", "at pages/industry/map.vue:328", "🔐 产业图谱 - 权限检查失败，停止加载页面内容");
      return;
    }
    this.checkIndustryUnlockStatus();
    this.initIndustryData();
    this.getUnlockDialogImage();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 获取企业信息解锁弹框背景图
    async getUnlockDialogImage() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:350", "🖼️ 开始获取企业信息解锁弹框背景图...");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:351", "🖼️ API地址: POST /miniapp/topimage/app/getEnabledListByPage");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:352", "🖼️ 请求参数: pageCode = CompanyInfoUnlockDialog");
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", "CompanyInfoUnlockDialog");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:355", "🖼️ 企业信息解锁弹框背景图完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const imageData = response.data.data;
          if (imageData && imageData.length > 0) {
            this.unlockDialogImage = utils_imageUtils.processServerImageUrl(imageData[0].imageUrl);
            common_vendor.index.__f__("log", "at pages/industry/map.vue:362", "🖼️ 企业信息解锁弹框背景图获取成功:", this.unlockDialogImage);
          } else {
            common_vendor.index.__f__("log", "at pages/industry/map.vue:364", "🖼️ 企业信息解锁弹框背景图数据为空");
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/map.vue:367", "🖼️ 企业信息解锁弹框背景图获取失败:", response);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:370", "🖼️ 获取企业信息解锁弹框背景图异常:", error);
      }
    },
    // 检查产业解锁状态
    async checkIndustryUnlockStatus() {
      try {
        if (!this.industryId) {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:378", "🔓 产业ID为空，跳过解锁状态检查");
          return;
        }
        common_vendor.index.__f__("log", "at pages/industry/map.vue:382", "🔓 开始检查产业解锁状态...");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:383", "🔓 API地址: GET /miniapp/industry/unlock/check/" + this.industryId);
        common_vendor.index.__f__("log", "at pages/industry/map.vue:384", "🔓 产业ID:", this.industryId);
        const response = await utils_request.request.get(`/miniapp/industry/unlock/check/${this.industryId}`);
        common_vendor.index.__f__("log", "at pages/industry/map.vue:387", "🔓 产业解锁状态完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          this.isIndustryUnlocked = response.data.unlocked;
          common_vendor.index.__f__("log", "at pages/industry/map.vue:391", "🔓 产业解锁状态检查成功，解锁状态:", this.isIndustryUnlocked);
        } else {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:393", "🔓 产业解锁状态检查失败:", response);
          this.isIndustryUnlocked = false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:397", "🔓 检查产业解锁状态异常:", error);
      }
    },
    // 检查用户是否可以访问产业资源
    async checkAccessPermission() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:404", "🔐 产业图谱 - 检查用户产业资源访问权限...");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:405", "🔐 API地址: GET /miniapp/unified-enterprise/user/status");
        const response = await utils_request.request.get("/miniapp/unified-enterprise/user/status");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:408", "🔐 产业图谱 - 访问权限检查响应:", response);
        if (response && response.data && response.data.code === 200) {
          const statusData = response.data;
          const status = statusData.status;
          const needCompleteInfo = statusData.needCompleteInfo;
          common_vendor.index.__f__("log", "at pages/industry/map.vue:415", "🔐 产业图谱 - 用户审核状态:", {
            status,
            needCompleteInfo
          });
          if (status === "approved") {
            common_vendor.index.__f__("log", "at pages/industry/map.vue:423", "🔐 产业图谱 - 审核通过，允许访问");
            return true;
          } else if (status === "pending") {
            common_vendor.index.showModal({
              title: "提示",
              content: "您的企业信息正在审核中，请耐心等待审核结果",
              showCancel: false,
              confirmText: "我知道了",
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
            return false;
          } else if (status === "rejected") {
            common_vendor.index.showModal({
              title: "提示",
              content: "您的企业信息审核未通过，请重新完善企业信息",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/industry/company-profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          } else if (status === "info_incomplete") {
            common_vendor.index.showModal({
              title: "提示",
              content: "请先完善个人信息，才能访问产业图谱",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/index/profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          } else if (status === "not_submitted") {
            common_vendor.index.showModal({
              title: "提示",
              content: "请先完善企业信息，才能访问产业图谱",
              showCancel: false,
              confirmText: "去完善",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.redirectTo({
                    url: "/pages/industry/company-profile"
                  });
                } else {
                  common_vendor.index.navigateBack();
                }
              }
            });
            return false;
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/map.vue:494", "🔐 产业图谱 - 检查访问权限失败:", response);
          return true;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:499", "🔐 产业图谱 - 检查访问权限异常:", error);
        return true;
      }
      return true;
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 滚动到指定流向模块
    scrollToStream(streamType) {
      this.activeStream = streamType;
      const sectionId = `${streamType}-section`;
      common_vendor.index.pageScrollTo({
        selector: `#${sectionId}`,
        duration: 500,
        offsetTop: -50
        // 偏移量，避免被顶部遮挡
      });
    },
    // 初始化产业数据
    async initIndustryData() {
      this.setIndustryBasicInfo();
      await this.getIndustryTreeData();
    },
    // 设置产业基本信息
    setIndustryBasicInfo() {
      switch (this.industryName) {
        case "新能源":
          this.industryIcon = utils_imageUtils.getImagePath("industry-new-energy.png");
          this.industrySubtitle = "NEW AND RENEWABLE SOURCES OF ENERGY";
          break;
        case "新材料":
          this.industryIcon = utils_imageUtils.getImagePath("industry-new-materials.png");
          this.industrySubtitle = "NEW MATERIALS";
          break;
        case "医疗大健康":
          this.industryIcon = utils_imageUtils.getImagePath("industry-medical.png");
          this.industrySubtitle = "MEDICAL AND HEALTH";
          break;
        default:
          this.industryIcon = utils_imageUtils.getImagePath("industry-default.png");
          this.industrySubtitle = "INDUSTRY";
          break;
      }
    },
    // 从接口获取行业树数据
    async getIndustryTreeData() {
      try {
        const secondLevelResponse = await utils_request.request.get("/miniapp/industry/tree/enterprises", {
          parentId: this.industryId
        });
        common_vendor.index.__f__("log", "at pages/industry/map.vue:566", "二级数据响应:", secondLevelResponse);
        if (secondLevelResponse.success && secondLevelResponse.data && secondLevelResponse.data.code === 200) {
          await this.processIndustryData(secondLevelResponse.data.data);
        } else {
          common_vendor.index.__f__("warn", "at pages/industry/map.vue:572", "获取行业数据失败，显示空状态");
          this.industryData = {
            upstream: [],
            midstream: [],
            downstream: []
          };
          this.totalEnterpriseCount = 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:581", "获取行业数据失败:", error);
        this.industryData = {
          upstream: [],
          midstream: [],
          downstream: []
        };
        this.totalEnterpriseCount = 0;
        common_vendor.index.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 处理行业数据，按上中下游分类
    async processIndustryData(responseData) {
      const processedData = {
        upstream: [],
        midstream: [],
        downstream: []
      };
      let totalCount = 0;
      let hasStreamType = false;
      if (responseData && responseData.length > 0) {
        const firstLevelData = responseData[0];
        const secondLevelData = firstLevelData.children || [];
        common_vendor.index.__f__("log", "at pages/industry/map.vue:617", "一级产业数据:", firstLevelData);
        common_vendor.index.__f__("log", "at pages/industry/map.vue:618", "二级分类数据:", secondLevelData);
        for (const item of secondLevelData) {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:622", "检查二级分类:", item.nodeInfo.nodeName, "streamType:", item.nodeInfo.streamType);
          if (item.nodeInfo.streamType && ["upstream", "midstream", "downstream"].includes(item.nodeInfo.streamType)) {
            hasStreamType = true;
            break;
          }
        }
        common_vendor.index.__f__("log", "at pages/industry/map.vue:629", "是否有上中下游分类:", hasStreamType);
        this.hasStreamType = hasStreamType;
        for (const item of secondLevelData) {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:633", "🏭 处理二级分类:", item.nodeInfo.nodeName, "企业数量:", item.statistics.totalCount);
          const thirdLevelData = item.children || [];
          const sectionData = {
            id: item.nodeInfo.nodeId,
            title: item.nodeInfo.nodeName,
            count: item.statistics.totalCount || 0,
            expanded: false,
            subsections: thirdLevelData.map((subItem) => ({
              id: subItem.nodeInfo.nodeId,
              name: subItem.nodeInfo.nodeName,
              count: subItem.statistics.totalCount || 0,
              enterprises: subItem.enterprises || [],
              // 保存企业信息
              hasEnterprises: subItem.enterprises && subItem.enterprises.length > 0
              // 是否有企业
            }))
          };
          if (hasStreamType) {
            const streamType = item.nodeInfo.streamType;
            if (streamType && ["upstream", "midstream", "downstream"].includes(streamType)) {
              processedData[streamType].push(sectionData);
            } else {
              common_vendor.index.__f__("warn", "at pages/industry/map.vue:658", "未知的streamType:", streamType, "节点名称:", item.nodeInfo.nodeName);
            }
          } else {
            processedData.upstream.push(sectionData);
            common_vendor.index.__f__("log", "at pages/industry/map.vue:663", "🏭 添加到upstream分类（无上中下游）:", item.nodeInfo.nodeName);
          }
          totalCount += item.statistics.totalCount || 0;
        }
      }
      common_vendor.index.__f__("log", "at pages/industry/map.vue:671", "处理后的产业数据:", processedData);
      common_vendor.index.__f__("log", "at pages/industry/map.vue:672", "总企业数量:", totalCount);
      common_vendor.index.__f__("log", "at pages/industry/map.vue:673", "是否有上中下游分类:", this.hasStreamType);
      this.industryData = processedData;
      this.totalEnterpriseCount = totalCount;
    },
    // 切换章节展开状态
    toggleSection(category, sectionIndex) {
      this.industryData[category][sectionIndex].expanded = !this.industryData[category][sectionIndex].expanded;
    },
    // 跳转到企业列表（点击三级分类）
    goToCompanyList(subsection) {
      common_vendor.index.__f__("log", "at pages/industry/map.vue:692", "点击三级分类:", subsection);
      if (!subsection.hasEnterprises) {
        common_vendor.index.showToast({
          title: "该分类暂无企业信息",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      this.currentSubsection = subsection;
      if (this.isIndustryUnlocked) {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:710", "🔓 产业已解锁，直接跳转到企业列表");
        this.navigateToCompanyList(subsection);
      } else {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:714", "🔒 产业未解锁，显示解锁弹框");
        this.showUnlockDialog = true;
      }
    },
    // 跳转到企业列表的通用方法
    navigateToCompanyList(subsection) {
      common_vendor.index.__f__("log", "at pages/industry/map.vue:721", "跳转到企业列表，企业数据:", subsection.enterprises);
      const enterprisesData = encodeURIComponent(JSON.stringify(subsection.enterprises));
      common_vendor.index.navigateTo({
        url: `/pages/industry/list?regionName=${encodeURIComponent(subsection.name + "地区")}&industryId=${subsection.id}&subsectionName=${encodeURIComponent(subsection.name)}&enterprisesData=${enterprisesData}`
      });
    },
    // 关闭企业信息解锁弹框
    closeUnlockDialog() {
      this.showUnlockDialog = false;
      this.currentSubsection = null;
    },
    // 立即解锁 - 调用解锁API
    async unlockCompanyInfo() {
      var _a;
      try {
        if (!this.industryId) {
          common_vendor.index.showToast({
            title: "产业ID不存在",
            icon: "none"
          });
          return;
        }
        common_vendor.index.__f__("log", "at pages/industry/map.vue:745", "🔓 开始解锁产业资源...");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:746", "🔓 API地址: POST /miniapp/industry/unlock/unlock/" + this.industryId);
        common_vendor.index.showLoading({
          title: "解锁中..."
        });
        const response = await utils_request.request.post(`/miniapp/industry/unlock/unlock/${this.industryId}`);
        common_vendor.index.__f__("log", "at pages/industry/map.vue:754", "🔓 产业解锁完整响应:", JSON.stringify(response, null, 2));
        common_vendor.index.hideLoading();
        if (response && response.success && response.data && response.data.code === 200) {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:759", "🔓 产业解锁成功");
          this.isIndustryUnlocked = true;
          common_vendor.index.showToast({
            title: "解锁成功",
            icon: "success"
          });
          if (this.currentSubsection) {
            common_vendor.index.__f__("log", "at pages/industry/map.vue:772", "🚀 准备跳转到企业列表，企业数据:", this.currentSubsection.enterprises);
            const enterprisesData = encodeURIComponent(JSON.stringify(this.currentSubsection.enterprises));
            const url = `/pages/industry/list?regionName=${encodeURIComponent(this.currentSubsection.name + "地区")}&industryId=${this.currentSubsection.id}&subsectionName=${encodeURIComponent(this.currentSubsection.name)}&enterprisesData=${enterprisesData}`;
            common_vendor.index.__f__("log", "at pages/industry/map.vue:775", "🚀 跳转URL:", url);
            common_vendor.index.navigateTo({
              url,
              success: function() {
                common_vendor.index.__f__("log", "at pages/industry/map.vue:780", "🚀 跳转成功");
              },
              fail: function(err) {
                common_vendor.index.__f__("error", "at pages/industry/map.vue:783", "🚀 跳转失败:", err);
              }
            });
          } else {
            common_vendor.index.__f__("error", "at pages/industry/map.vue:787", "🚀 currentSubsection 为空，无法跳转");
          }
          this.closeUnlockDialog();
        } else {
          common_vendor.index.__f__("log", "at pages/industry/map.vue:793", "🔓 产业解锁失败:", response);
          common_vendor.index.showToast({
            title: ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "解锁失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:800", "🔓 解锁产业资源异常:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "解锁失败，请重试",
          icon: "none"
        });
      }
    },
    // 我有需求 - 跳转到资源场景需求发布页面（与主页面一致）
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/industry/map.vue:811", "🎯 我有需求 - 跳转到资源场景需求发布页面");
      try {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:815", "🎯 获取分类列表，查找资源场景分类ID...");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/industry/map.vue:818", "🎯 分类列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const categories = response.data.data || [];
          const scenarioCategory = categories.find(
            (cat) => cat.categoryCode === "scenario" || cat.categoryName === "资源场景" || cat.name === "资源场景"
          );
          if (scenarioCategory) {
            const categoryId = scenarioCategory.categoryId || scenarioCategory.id;
            const categoryName = scenarioCategory.categoryName || scenarioCategory.name || "资源场景";
            common_vendor.index.__f__("log", "at pages/industry/map.vue:834", "🎯 找到资源场景分类:", { categoryId, categoryName });
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}&categoryCode=scenario`
            });
          } else {
            common_vendor.index.__f__("log", "at pages/industry/map.vue:841", "🎯 ❌ 未找到资源场景分类，使用默认方式");
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/map.vue:848", "🎯 获取分类列表失败:", response);
          common_vendor.index.navigateTo({
            url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/map.vue:855", "🎯 获取分类列表异常:", error);
        common_vendor.index.navigateTo({
          url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
        });
      }
    },
    // 我有疑问 - 与需求广场功能一致
    async haveQuestion() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/industry/map.vue:865", "📋 问题咨询");
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/industry/map.vue:874", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/industry/map.vue:891", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/industry/map.vue:897", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "产业图谱咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/map.vue:910", "📋 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/map.vue:945", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: common_vendor.t($data.industryName),
    c: common_vendor.t($data.totalEnterpriseCount),
    d: common_vendor.t($data.industrySubtitle),
    e: $data.hasStreamType
  }, $data.hasStreamType ? {
    f: $data.activeStream === "upstream" ? 1 : "",
    g: common_vendor.o(($event) => $options.scrollToStream("upstream")),
    h: $data.activeStream === "midstream" ? 1 : "",
    i: common_vendor.o(($event) => $options.scrollToStream("midstream")),
    j: $data.activeStream === "downstream" ? 1 : "",
    k: common_vendor.o(($event) => $options.scrollToStream("downstream"))
  } : {}, {
    l: $data.hasStreamType
  }, $data.hasStreamType ? common_vendor.e({
    m: $data.industryData.upstream.length > 0
  }, $data.industryData.upstream.length > 0 ? {
    n: common_vendor.f($data.industryData.upstream, (section, sectionIndex, i0) => {
      return common_vendor.e({
        a: common_vendor.t(section.title),
        b: common_vendor.t(section.count),
        c: common_vendor.t(section.expanded ? "▲" : "▼"),
        d: common_vendor.o(($event) => $options.toggleSection("upstream", sectionIndex), "upstream-" + sectionIndex),
        e: section.expanded
      }, section.expanded ? {
        f: common_vendor.f(section.subsections, (subsection, subIndex, i1) => {
          return {
            a: common_vendor.t(subsection.name),
            b: subIndex,
            c: common_vendor.o(($event) => $options.goToCompanyList(subsection), subIndex)
          };
        })
      } : {}, {
        g: "upstream-" + sectionIndex
      });
    })
  } : {}, {
    o: $data.industryData.midstream.length > 0
  }, $data.industryData.midstream.length > 0 ? {
    p: common_vendor.f($data.industryData.midstream, (section, sectionIndex, i0) => {
      return common_vendor.e({
        a: common_vendor.t(section.title),
        b: common_vendor.t(section.count),
        c: common_vendor.t(section.expanded ? "▲" : "▼"),
        d: common_vendor.o(($event) => $options.toggleSection("midstream", sectionIndex), "midstream-" + sectionIndex),
        e: section.expanded
      }, section.expanded ? {
        f: common_vendor.f(section.subsections, (subsection, subIndex, i1) => {
          return {
            a: common_vendor.t(subsection.name),
            b: subIndex,
            c: common_vendor.o(($event) => $options.goToCompanyList(subsection), subIndex)
          };
        })
      } : {}, {
        g: "midstream-" + sectionIndex
      });
    })
  } : {}, {
    q: $data.industryData.downstream.length > 0
  }, $data.industryData.downstream.length > 0 ? {
    r: common_vendor.f($data.industryData.downstream, (section, sectionIndex, i0) => {
      return common_vendor.e({
        a: common_vendor.t(section.title),
        b: common_vendor.t(section.count),
        c: common_vendor.t(section.expanded ? "▲" : "▼"),
        d: common_vendor.o(($event) => $options.toggleSection("downstream", sectionIndex), "downstream-" + sectionIndex),
        e: section.expanded
      }, section.expanded ? {
        f: common_vendor.f(section.subsections, (subsection, subIndex, i1) => {
          return {
            a: common_vendor.t(subsection.name),
            b: subIndex,
            c: common_vendor.o(($event) => $options.goToCompanyList(subsection), subIndex)
          };
        })
      } : {}, {
        g: "downstream-" + sectionIndex
      });
    })
  } : {}) : common_vendor.e({
    s: $data.industryData.upstream.length > 0
  }, $data.industryData.upstream.length > 0 ? {
    t: common_vendor.f($data.industryData.upstream, (section, sectionIndex, i0) => {
      return common_vendor.e({
        a: common_vendor.t(section.title),
        b: common_vendor.t(section.count),
        c: common_vendor.t(section.expanded ? "▲" : "▼"),
        d: common_vendor.o(($event) => $options.toggleSection("upstream", sectionIndex), "single-" + sectionIndex),
        e: section.expanded
      }, section.expanded ? {
        f: common_vendor.f(section.subsections, (subsection, subIndex, i1) => {
          return {
            a: common_vendor.t(subsection.name),
            b: subIndex,
            c: common_vendor.o(($event) => $options.goToCompanyList(subsection), subIndex)
          };
        })
      } : {}, {
        g: "single-" + sectionIndex
      });
    })
  } : {}), {
    v: common_vendor.o((...args) => $options.haveQuestion && $options.haveQuestion(...args)),
    w: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    x: $data.showUnlockDialog
  }, $data.showUnlockDialog ? common_vendor.e({
    y: $options.getImagePath("cha.png"),
    z: common_vendor.o((...args) => $options.closeUnlockDialog && $options.closeUnlockDialog(...args)),
    A: $data.unlockDialogImage
  }, $data.unlockDialogImage ? {
    B: $data.unlockDialogImage
  } : {}, {
    C: common_vendor.o((...args) => $options.unlockCompanyInfo && $options.unlockCompanyInfo(...args)),
    D: common_vendor.o(() => {
    }),
    E: common_vendor.o((...args) => $options.closeUnlockDialog && $options.closeUnlockDialog(...args))
  }) : {}, {
    F: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    G: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    H: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    I: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    J: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    K: $data.contactInfo.qrCodeUrl
  } : {}, {
    L: common_vendor.o(() => {
    }),
    M: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/map.js.map
