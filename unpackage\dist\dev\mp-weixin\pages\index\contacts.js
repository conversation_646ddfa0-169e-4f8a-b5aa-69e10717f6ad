"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      searchKeyword: "",
      // 筛选选项索引
      selectedGradeIndex: 0,
      selectedRegionIndex: 0,
      selectedIndustryIndex: 0,
      // 筛选选项数据
      gradeOptions: [],
      regionOptions: [],
      industryOptions: [],
      contactsList: [],
      loading: false
    };
  },
  computed: {
    // 当前选中的年级标签
    currentGradeLabel() {
      var _a;
      return ((_a = this.gradeOptions[this.selectedGradeIndex]) == null ? void 0 : _a.label) || "年级";
    },
    // 当前选中的地区标签
    currentRegionLabel() {
      var _a;
      return ((_a = this.regionOptions[this.selectedRegionIndex]) == null ? void 0 : _a.label) || "地区";
    },
    // 当前选中的行业标签
    currentIndustryLabel() {
      var _a;
      return ((_a = this.industryOptions[this.selectedIndustryIndex]) == null ? void 0 : _a.label) || "行业";
    }
  },
  async onLoad() {
    const isProfileComplete = await utils_profileCheck.checkProfileCompleteAsync();
    if (!isProfileComplete) {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:157", "👥 个人资料未完善，无权限访问人脉资源");
      common_vendor.index.showModal({
        title: "提示",
        content: "未完善相关资料，暂无权限使用",
        showCancel: false,
        confirmText: "去完善",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.reLaunch({
              url: "/pages/index/profile"
            });
          }
        }
      });
      return;
    }
    this.initFilterData();
  },
  async onShow() {
    const isProfileComplete = await utils_profileCheck.checkProfileCompleteAsync();
    if (!isProfileComplete) {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:185", "👥 页面显示时检测到个人资料未完善，无权限访问人脉资源");
      common_vendor.index.showModal({
        title: "提示",
        content: "未完善相关资料，暂无权限使用",
        showCancel: false,
        confirmText: "去完善",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.reLaunch({
              url: "/pages/index/profile"
            });
          }
        }
      });
      return;
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 人脉资源";
    },
    // 自定义分享内容
    getShareContent() {
      return "人脉资源分享";
    },
    // 初始化筛选数据
    async initFilterData() {
      this.initGradeOptions();
      this.initRegionOptions();
      await this.loadIndustryOptions();
      this.loadContactsList();
    },
    // 初始化年级选项（从1980到今年）
    initGradeOptions() {
      const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
      const gradeOptions = [{ label: "年级", value: "" }];
      for (let year = currentYear; year >= 1980; year--) {
        gradeOptions.push({
          label: `${year}级`,
          value: year.toString()
        });
      }
      this.gradeOptions = gradeOptions;
    },
    // 初始化地区选项（省份数据）
    initRegionOptions() {
      const provinces = [
        { name: "北京" },
        { name: "天津" },
        { name: "河北" },
        { name: "山西" },
        { name: "内蒙古" },
        { name: "辽宁" },
        { name: "吉林" },
        { name: "黑龙江" },
        { name: "上海" },
        { name: "江苏" },
        { name: "浙江" },
        { name: "安徽" },
        { name: "福建" },
        { name: "江西" },
        { name: "山东" },
        { name: "河南" },
        { name: "湖北" },
        { name: "湖南" },
        { name: "广东" },
        { name: "广西" },
        { name: "海南" },
        { name: "重庆" },
        { name: "四川" },
        { name: "贵州" },
        { name: "云南" },
        { name: "西藏" },
        { name: "陕西" },
        { name: "甘肃" },
        { name: "青海" },
        { name: "宁夏" },
        { name: "新疆" },
        { name: "台湾" },
        { name: "香港" },
        { name: "澳门" }
      ];
      const regionOptions = [{ label: "地区", value: "" }];
      provinces.forEach((province) => {
        regionOptions.push({
          label: province.name,
          value: province.name
        });
      });
      this.regionOptions = regionOptions;
    },
    // 加载行业选项（通过API获取一级行业数据）
    async loadIndustryOptions() {
      try {
        common_vendor.index.__f__("log", "at pages/index/contacts.vue:279", "🏢 开始获取行业数据...");
        const response = await utils_request.request.get("/miniapp/industry/level/1");
        common_vendor.index.__f__("log", "at pages/index/contacts.vue:281", "🏢 行业数据API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const industryData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:285", "🏢 原始行业数据:", industryData);
          const industryOptions = [{ label: "行业", value: "" }];
          industryData.forEach((industry) => {
            industryOptions.push({
              label: industry.nodeName,
              value: industry.id
            });
          });
          this.industryOptions = industryOptions;
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:297", "🏢 行业选项构建完成:", this.industryOptions);
        } else {
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:299", "🏢 ❌ 行业数据获取失败！");
          this.industryOptions = [{ label: "行业", value: "" }];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts.vue:304", "🏢 获取行业数据失败:", error);
        this.industryOptions = [{ label: "行业", value: "" }];
      }
    },
    handleSearch() {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:311", "搜索关键词:", this.searchKeyword);
      this.loadContactsList();
    },
    // 年级筛选改变事件
    onGradeChange(e) {
      this.selectedGradeIndex = e.detail.value;
      const selectedOption = this.gradeOptions[this.selectedGradeIndex];
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:318", "选择年级筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadContactsList();
    },
    // 地区筛选改变事件
    onRegionChange(e) {
      this.selectedRegionIndex = e.detail.value;
      const selectedOption = this.regionOptions[this.selectedRegionIndex];
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:326", "选择地区筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadContactsList();
    },
    // 行业筛选改变事件
    onIndustryChange(e) {
      this.selectedIndustryIndex = e.detail.value;
      const selectedOption = this.industryOptions[this.selectedIndustryIndex];
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:334", "选择行业筛选:", selectedOption.label, "Value:", selectedOption.value);
      this.loadContactsList();
    },
    // 加载联系人列表
    async loadContactsList() {
      var _a, _b, _c;
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/index/contacts.vue:342", "👥 开始获取人脉资源列表...");
        const gradeFilter = ((_a = this.gradeOptions[this.selectedGradeIndex]) == null ? void 0 : _a.value) || "";
        const regionFilter = ((_b = this.regionOptions[this.selectedRegionIndex]) == null ? void 0 : _b.value) || "";
        const industryFilter = ((_c = this.industryOptions[this.selectedIndustryIndex]) == null ? void 0 : _c.value) || "";
        const params = {};
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.searchValue = this.searchKeyword.trim();
        }
        if (gradeFilter) {
          params.graduationYear = gradeFilter;
        }
        if (regionFilter) {
          params.region = regionFilter;
        }
        if (industryFilter) {
          params.industryField = industryFilter;
        }
        common_vendor.index.__f__("log", "at pages/index/contacts.vue:372", "👥 筛选条件:", params);
        const response = await utils_request.request.get("/miniapp/user/list", params);
        common_vendor.index.__f__("log", "at pages/index/contacts.vue:376", "👥 用户列表API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const userData = response.data.rows || [];
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:380", "👥 原始用户数据:", userData);
          this.contactsList = userData.map((user) => ({
            id: user.userId,
            name: user.realName || user.nickName || "未知用户",
            grade: user.graduateSchool || "未知学校",
            // 显示学校名称
            region: user.college || "未知学院",
            // 显示学院名称
            industry: user.major || "未知专业",
            // 显示专业名称
            avatar: utils_imageUtils.processServerImageUrl(user.portraitUrl || "", utils_imageUtils.getImagePath("avatar.png")),
            // 保存原始数据，用于详情页面
            rawData: user
          }));
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:394", "👥 处理后的联系人列表:", this.contactsList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/contacts.vue:396", "👥 ❌ 用户列表获取失败！");
          this.contactsList = [];
          if (response && response.data && response.data.msg) {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/contacts.vue:407", "👥 获取用户列表失败:", error);
        this.contactsList = [];
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 根据行业ID获取行业名称
    getIndustryName(industryField) {
      if (!industryField)
        return "";
      const industryIds = industryField.split(",");
      const industryNames = [];
      industryIds.forEach((id) => {
        const industry = this.industryOptions.find((option) => option.value === id);
        if (industry && industry.label !== "行业") {
          industryNames.push(industry.label);
        }
      });
      return industryNames.join("、") || "未知行业";
    },
    viewContactDetail(contact) {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:436", "👥 查看联系人详情:", contact);
      const userData = contact.rawData;
      common_vendor.index.navigateTo({
        url: `/pages/index/business-card-detail?userData=${encodeURIComponent(JSON.stringify(userData))}`
      });
    },
    navigateToHome() {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:448", "点击首页");
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    navigateToMine() {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:454", "点击我的");
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    },
    // 导航到需求广场页面
    navigateToDemandSquare() {
      common_vendor.index.__f__("log", "at pages/index/contacts.vue:461", "点击需求广场");
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.t($options.currentGradeLabel),
    f: $data.selectedGradeIndex,
    g: $data.gradeOptions,
    h: common_vendor.o((...args) => $options.onGradeChange && $options.onGradeChange(...args)),
    i: common_vendor.t($options.currentRegionLabel),
    j: $data.selectedRegionIndex,
    k: $data.regionOptions,
    l: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    m: common_vendor.t($options.currentIndustryLabel),
    n: $data.selectedIndustryIndex,
    o: $data.industryOptions,
    p: common_vendor.o((...args) => $options.onIndustryChange && $options.onIndustryChange(...args)),
    q: $data.loading
  }, $data.loading ? {} : $data.contactsList.length === 0 ? {} : {
    s: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return {
        a: contact.avatar || $options.getImagePath("avatar.png"),
        b: common_vendor.t(contact.name),
        c: common_vendor.t(contact.grade),
        d: common_vendor.t(contact.region),
        e: common_vendor.t(contact.industry),
        f: index,
        g: common_vendor.o(($event) => $options.viewContactDetail(contact), index)
      };
    })
  }, {
    r: $data.contactsList.length === 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/contacts.js.map
