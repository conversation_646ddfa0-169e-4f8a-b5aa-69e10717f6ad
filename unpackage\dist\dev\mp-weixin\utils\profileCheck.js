"use strict";
const common_vendor = require("../common/vendor.js");
async function checkProfileCompleteAsync() {
  try {
    const request = require("./request.js");
    const response = await request.request.get("/miniapp/unified-enterprise/user/status");
    if (response && response.data && response.data.code === 200) {
      const statusData = response.data;
      const status = statusData.status;
      const needCompleteInfo = statusData.needCompleteInfo;
      let isProfileComplete = true;
      if (status === "info_incomplete") {
        isProfileComplete = false;
      } else if (status === "not_submitted" && needCompleteInfo === true) {
        isProfileComplete = false;
      }
      common_vendor.index.__f__("log", "at utils/profileCheck.js:34", "📝 个人资料完善状态检查:", {
        status,
        needCompleteInfo,
        isProfileComplete
      });
      return isProfileComplete;
    } else {
      common_vendor.index.__f__("error", "at utils/profileCheck.js:42", "📝 检查个人资料完善状态失败:", response);
      return false;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:46", "📝 检查个人资料完善状态异常:", error);
    return false;
  }
}
async function shouldShowProfileGuideAsync() {
  try {
    const isInfoComplete = await checkProfileCompleteAsync();
    common_vendor.index.__f__("log", "at utils/profileCheck.js:124", "📝 shouldShowProfileGuideAsync - isInfoComplete:", isInfoComplete);
    if (isInfoComplete) {
      common_vendor.index.__f__("log", "at utils/profileCheck.js:127", "📝 个人资料已完善，不显示引导弹窗");
      return false;
    }
    const sessionShown = common_vendor.index.getStorageSync("profileGuideSessionShown");
    common_vendor.index.__f__("log", "at utils/profileCheck.js:133", "📝 本次会话是否已显示:", sessionShown);
    if (sessionShown === true) {
      common_vendor.index.__f__("log", "at utils/profileCheck.js:136", "📝 本次会话已显示过引导弹窗，不再显示");
      return false;
    }
    common_vendor.index.__f__("log", "at utils/profileCheck.js:140", "📝 需要显示引导弹窗");
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:143", "📝 检查引导弹窗显示状态失败:", error);
    return false;
  }
}
function markProfileGuideShown() {
  try {
    common_vendor.index.setStorageSync("profileGuideSessionShown", true);
    common_vendor.index.__f__("log", "at utils/profileCheck.js:154", "📝 标记引导弹窗已显示（本次会话）");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:156", "📝 标记引导弹窗显示状态失败:", error);
  }
}
function resetProfileGuideSession() {
  try {
    common_vendor.index.removeStorageSync("profileGuideSessionShown");
    common_vendor.index.removeStorageSync("profileGuideLastShown");
    common_vendor.index.__f__("log", "at utils/profileCheck.js:168", "📝 重置引导弹窗会话状态");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:170", "📝 重置引导弹窗会话状态失败:", error);
  }
}
function updateProfileCompleteStatus(isComplete) {
  try {
    common_vendor.index.setStorageSync("isInfoComplete", isComplete);
    common_vendor.index.__f__("log", "at utils/profileCheck.js:194", "💾 更新个人资料完善状态为:", isComplete);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:196", "💾 更新个人资料完善状态失败:", error);
  }
}
async function checkEnterpriseComplete() {
  try {
    const request = require("./request.js");
    const response = await request.request.get("/miniapp/unified-enterprise/user/status");
    if (response && response.data && response.data.code === 200) {
      const statusData = response.data;
      const status = statusData.status;
      const isEnterpriseComplete = status === "approved" || status === "pending";
      common_vendor.index.__f__("log", "at utils/profileCheck.js:221", "🏢 企业信息完善状态检查:", {
        status,
        isEnterpriseComplete
      });
      return isEnterpriseComplete;
    } else {
      common_vendor.index.__f__("error", "at utils/profileCheck.js:228", "🏢 企业信息状态检查失败:", response);
      return false;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:232", "🏢 检查企业信息完善状态失败:", error);
    return false;
  }
}
function showProfileCompleteDialog(message = "未完善相关资料，暂无权限使用") {
  return new Promise((resolve) => {
    common_vendor.index.showModal({
      title: "提示",
      content: message,
      showCancel: false,
      confirmText: "去完善",
      success: (res) => {
        if (res.confirm) {
          common_vendor.index.navigateTo({
            url: "/pages/index/profile?from=permission_check"
          });
          resolve(true);
        } else {
          resolve(false);
        }
      }
    });
  });
}
function showEnterpriseCompleteDialog(message = "需要完善企业信息才能使用此功能") {
  return new Promise((resolve) => {
    common_vendor.index.showModal({
      title: "提示",
      content: message,
      showCancel: false,
      confirmText: "去完善",
      success: (res) => {
        if (res.confirm) {
          common_vendor.index.navigateTo({
            url: "/pages/industry/company-profile"
          });
          resolve(true);
        } else {
          resolve(false);
        }
      }
    });
  });
}
async function checkFunctionPermission(needEnterprise = false) {
  try {
    const profileComplete = await checkProfileCompleteAsync();
    if (!profileComplete) {
      await showProfileCompleteDialog();
      return false;
    }
    if (needEnterprise) {
      const enterpriseComplete = await checkEnterpriseComplete();
      if (!enterpriseComplete) {
        await showEnterpriseCompleteDialog();
        return false;
      }
    }
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/profileCheck.js:316", "🔐 权限检查失败:", error);
    return false;
  }
}
exports.checkFunctionPermission = checkFunctionPermission;
exports.checkProfileCompleteAsync = checkProfileCompleteAsync;
exports.markProfileGuideShown = markProfileGuideShown;
exports.resetProfileGuideSession = resetProfileGuideSession;
exports.shouldShowProfileGuideAsync = shouldShowProfileGuideAsync;
exports.updateProfileCompleteStatus = updateProfileCompleteStatus;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/profileCheck.js.map
