
.activity-share-container.data-v-d52e2873 {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 30rpx 30rpx 0rpx; /* 移除顶部padding，让封面图贴顶 */
}
.share-content.data-v-d52e2873 {
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow:  0 8rpx 16rpx rgba(200, 213, 242,1);
	margin: 0 0 40rpx 0; /* 完全移除上边距，让封面图贴顶 */
}

/* 封面区域 */
.cover-section.data-v-d52e2873 {
	position: relative;
	height: 400rpx;
	overflow: hidden;
	border-radius: 20rpx 20rpx 0 0; /* 只给顶部圆角，与卡片保持一致 */
}
.cover-image.data-v-d52e2873 {
	width: 100%;
	height: 100%;
	border-radius: 20rpx 20rpx 0 0; /* 封面图也只给顶部圆角 */
}
.cover-overlay.data-v-d52e2873 {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
	padding: 60rpx 30rpx 30rpx;
	color: #ffffff;
}
.cover-title.data-v-d52e2873 {
	font-size: 36rpx;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 20rpx;
	display: block;
}
.cover-info.data-v-d52e2873 {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
.cover-time.data-v-d52e2873,
.cover-location.data-v-d52e2873 {
	font-size: 28rpx;
	opacity: 0.9;
}

/* 详细信息区域 */
.detail-section.data-v-d52e2873 {
	padding: 40rpx 30rpx;
}
.detail-title.data-v-d52e2873 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 30rpx;
	line-height: 1.4;
}
.info-item.data-v-d52e2873 {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}
.info-icon.data-v-d52e2873 {
	font-size: 28rpx;
	margin-right: 15rpx;
	margin-top: 2rpx;
}
.info-text.data-v-d52e2873 {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
	flex: 1;
}
.description.data-v-d52e2873 {
	margin-top: 30rpx;
	padding-top: 30rpx;
}
.description-text.data-v-d52e2873 {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.6;
}

/* 小程序码区域 */
.qrcode-section.data-v-d52e2873 {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	background: #fff;
	border-top: 1rpx solid #e9ecef;
}
.qrcode-container.data-v-d52e2873 {
	margin-right: 30rpx;
	width: 160rpx; /* 增大小程序码容器尺寸 */
	height: 160rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.qrcode-image.data-v-d52e2873 {
	width: 140rpx; /* 增大小程序码图片尺寸 */
	height: 140rpx;
	border-radius: 10rpx;
	cursor: pointer;
}
.qrcode-loading.data-v-d52e2873 {
	width: 160rpx; /* 增大加载状态容器尺寸 */
	height: 160rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f9fa;
	border: 0.5rpx solid #e0e0e0; /* 缩窄边框 */
	border-radius: 10rpx;
}
.loading-text.data-v-d52e2873 {
	font-size: 24rpx;
	color: #999;
}
.qrcode-canvas.data-v-d52e2873 {
	border: 0.5rpx solid #e0e0e0; /* 缩窄边框 */
	border-radius: 10rpx;
	cursor: pointer;
}
.qrcode-text.data-v-d52e2873 {
	flex: 1;
}
.qrcode-title.data-v-d52e2873 {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	display: block;
	margin-bottom: 10rpx;
}
.qrcode-subtitle.data-v-d52e2873 {
	font-size: 24rpx;
	color: #999999;
}

/* 操作按钮 */
.action-buttons.data-v-d52e2873 {
	display: flex;
	gap: 20rpx;
}
.action-btn.data-v-d52e2873 {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}
.save-btn.data-v-d52e2873 {
	background: #ffffff;
	color: #023caa;
	border: 2rpx solid #023caa;
}
.share-btn.data-v-d52e2873 {
	background: #023caa;
	color: #ffffff;
}
.action-btn.data-v-d52e2873:active {
	opacity: 0.8;
}

/* 隐藏的canvas */
.hidden-canvas.data-v-d52e2873 {
	position: fixed;
	top: -9999px;
	left: -9999px;
	opacity: 0;
	pointer-events: none;
}
