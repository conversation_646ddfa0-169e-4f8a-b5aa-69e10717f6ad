.products-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}
/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #1e4ba8 0%, #0d2d5f 100%);
  padding-top: var(--status-bar-height);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1000;
}
.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.navbar-left,
.navbar-right {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon,
.scan-icon {
  width: 40rpx;
  height: 40rpx;
}
.navbar-center {
  flex: 1;
  text-align: center;
}
.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}
/* 商品列表 */
.products-content {
  padding: 30rpx;
}
.products-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}
.product-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 230rpx;
}
.product-image {
  width: 100%;
  height: 100%;
}
.product-image-placeholder {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}
.placeholder-text {
  font-size: 24rpx;
  color: #999;
}
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.discount-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: #ff6b35;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.product-info {
  padding: 24rpx;
}
.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.product-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.4;
}
.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-price {
  font-size: 28rpx;
  color: #db7476;
  font-weight: 700;
}
.exchange-btn {
  background: linear-gradient(180deg, #e5d77e 0%, #e1b98c 100%);
  padding: 4rpx 32rpx;
  border-radius: 25rpx;
}
.btn-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 600;
}
