"use strict";
const common_vendor = require("../common/vendor.js");
const config_index = require("../config/index.js");
class Request {
  constructor() {
    this.baseURL = config_index.config.baseURL;
  }
  // 处理401未登录状态
  handle401Unauthorized(requestUrl) {
    common_vendor.index.removeStorageSync("token");
    common_vendor.index.removeStorageSync("userInfo");
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      const currentOptions = currentPage.options;
      if (currentRoute !== "pages/login/index") {
        let fullPath = "/" + currentRoute;
        if (currentOptions && Object.keys(currentOptions).length > 0) {
          const queryString = Object.keys(currentOptions).map((key) => `${key}=${encodeURIComponent(currentOptions[key])}`).join("&");
          fullPath += "?" + queryString;
        }
        common_vendor.index.setStorageSync("redirectPath", fullPath);
      }
    }
    common_vendor.index.showToast({
      title: "登录已过期，请重新登录",
      icon: "none",
      duration: 2e3
    });
    setTimeout(() => {
      common_vendor.index.reLaunch({
        url: "/pages/login/index"
      });
    }, 2e3);
  }
  // 通用请求方法
  request(options = {}) {
    return new Promise((resolve, reject) => {
      const token = common_vendor.index.getStorageSync("token");
      const defaultHeaders = {
        "Content-Type": "application/json"
      };
      if (token) {
        defaultHeaders["Authorization"] = "Bearer " + token;
      }
      const headers = {
        ...defaultHeaders,
        ...options.header || {}
      };
      const url = options.url.startsWith("http") ? options.url : this.baseURL + options.url;
      common_vendor.index.request({
        url,
        method: options.method || "GET",
        data: options.data || {},
        header: headers,
        timeout: options.timeout || 1e4,
        success: (res) => {
          var _a, _b;
          if (res.statusCode === 200) {
            if (res.data && res.data.code && res.data.code !== 200) {
              if (res.data.code === 401) {
                this.handle401Unauthorized(options.url);
                reject(new Error("用户未登录"));
                return;
              }
              resolve({
                success: false,
                message: res.data.msg || res.data.message || "请求失败",
                statusCode: res.data.code,
                data: res.data
              });
            } else {
              resolve({
                success: true,
                data: res.data,
                statusCode: res.statusCode
              });
            }
          } else if (res.statusCode === 401) {
            common_vendor.index.removeStorageSync("token");
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.showToast({
              title: "登录已过期，请重新登录",
              icon: "none"
            });
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/login/index"
              });
            }, 1500);
            reject(new Error("登录已过期"));
          } else {
            resolve({
              success: false,
              message: ((_a = res.data) == null ? void 0 : _a.msg) || ((_b = res.data) == null ? void 0 : _b.message) || `请求失败 (${res.statusCode})`,
              statusCode: res.statusCode,
              data: res.data
            });
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
  // GET请求
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "GET",
      data,
      ...options
    });
  }
  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "POST",
      data,
      ...options
    });
  }
  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "PUT",
      data,
      ...options
    });
  }
  // DELETE请求
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "DELETE",
      data,
      ...options
    });
  }
  // 文件上传
  upload(url, filePath, name = "file", formData = {}, options = {}) {
    return new Promise((resolve, reject) => {
      const token = common_vendor.index.getStorageSync("token");
      const headers = {};
      if (token) {
        headers["Authorization"] = "Bearer " + token;
      }
      Object.assign(headers, options.header || {});
      const fullUrl = url.startsWith("http") ? url : this.baseURL + url;
      common_vendor.index.uploadFile({
        url: fullUrl,
        filePath,
        name,
        formData,
        header: headers,
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (res.statusCode === 200 && (data.code === 200 || data.code === void 0)) {
              resolve({
                success: true,
                data,
                url: data.data || data.url || data.fileName
              });
            } else {
              resolve({
                success: false,
                message: data.msg || data.message || "上传失败"
              });
            }
          } catch (parseError) {
            if (res.statusCode === 200) {
              resolve({
                success: true,
                data: res.data,
                url: res.data
              });
            } else {
              resolve({
                success: false,
                message: "上传失败"
              });
            }
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
}
const request = new Request();
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
