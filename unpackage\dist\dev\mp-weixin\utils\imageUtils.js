"use strict";
const config_index = require("../config/index.js");
function getImagePath(imageName) {
  if (imageName.startsWith("http") || imageName.startsWith("/static/") || imageName.startsWith("data:")) {
    return imageName;
  }
  return config_index.config.IMAGE_PREFIX + imageName;
}
function processServerImageUrl(imageUrl, fallbackImage = "") {
  if (!imageUrl || imageUrl.trim() === "") {
    return fallbackImage;
  }
  if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
    return imageUrl;
  }
  if (imageUrl.startsWith("data:")) {
    return imageUrl;
  }
  let processedUrl = imageUrl;
  if (!processedUrl.startsWith("/")) {
    processedUrl = "/" + processedUrl;
  }
  return config_index.config.baseURL + processedUrl;
}
function processHtmlImageUrls(htmlContent) {
  if (!htmlContent || typeof htmlContent !== "string") {
    return htmlContent;
  }
  return htmlContent.replace(/<img([^>]*?)src=["']([^"']*?)["']([^>]*?)>/gi, (match, before, src, after) => {
    const processedSrc = processServerImageUrl(src);
    const style = "max-width: 100%; height: auto; display: block; margin: 10rpx 0;";
    if (before.includes("style=") || after.includes("style=")) {
      return `<img${before}src="${processedSrc}"${after}>`;
    } else {
      return `<img${before}src="${processedSrc}"${after} style="${style}">`;
    }
  });
}
({
  // 底部导航图标
  HOME_ICON: getImagePath("bottom_order_icon1.png"),
  CONTACTS_ICON: getImagePath("bottom_order_icon2.png"),
  DEMAND_ICON: getImagePath("bottom_order_icon3.png"),
  MINE_ICON: getImagePath("bottom_order_icon4.png"),
  // 通用图标
  AVATAR_DEFAULT: getImagePath("avatar.png"),
  SHARE_LOGO: getImagePath("share-logo.png"),
  NOTICE_ICON: getImagePath("notice_icon.png"),
  // 登录页图标
  LOGIN_ICON1: getImagePath("login_icon1.png"),
  LOGIN_ICON2: getImagePath("login_icon2.png"),
  LOGIN_ICON3: getImagePath("login_icon3.png"),
  // 介绍页图标
  INTRO_ICON1: getImagePath("intro-icon1.png"),
  INTRO_ICON2: getImagePath("intro-icon2.png"),
  // 标题图标
  TITLE_ICON1: getImagePath("title_icon1.png"),
  TITLE_ICON2: getImagePath("title_icon2.png"),
  TITLE_ICON3: getImagePath("title_icon3.png"),
  TITLE_ICON4: getImagePath("title_icon4.png"),
  TITLE_ICON5: getImagePath("title_icon5.png"),
  TITLE_ICON6: getImagePath("title_icon6.png"),
  TITLE_ICON7: getImagePath("title_icon7.png"),
  TITLE_ICON8: getImagePath("title_icon8.png"),
  TITLE_ICON9: getImagePath("title_icon9.png"),
  TITLE_ICON10: getImagePath("title_icon10.png"),
  TITLE_ICON11: getImagePath("title_icon11.png"),
  TITLE_ICON12: getImagePath("title_icon12.png"),
  TITLE_ICON13: getImagePath("title_icon13.png"),
  TITLE_ICON14: getImagePath("title_icon14.png"),
  TITLE_ICON15: getImagePath("title_icon15.png"),
  TITLE_ICON16: getImagePath("title_icon16.png"),
  TITLE_ICON17: getImagePath("title_icon17.png"),
  TITLE_ICON18: getImagePath("title_icon18.png"),
  TITLE_ICON19: getImagePath("title_icon19.png"),
  TITLE_ICON20: getImagePath("title_icon20.png")
});
exports.getImagePath = getImagePath;
exports.processHtmlImageUrls = processHtmlImageUrls;
exports.processServerImageUrl = processServerImageUrl;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/imageUtils.js.map
