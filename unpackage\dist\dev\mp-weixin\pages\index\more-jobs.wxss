.more-jobs-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #d3e2ff, #fff);
  padding-bottom: 120rpx;
  overflow-x: hidden;
  box-sizing: border-box;
}
/* 内容区域 - 纯色背景 */
.content-area {
  position: relative;
  z-index: 0;
  width: 100%;
  box-sizing: border-box;
}
/* 加入我们区域 */
.section-block {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.7);
  width: 100%;
  box-sizing: border-box;
  padding-top: 30rpx;
}
.list_bg {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.job-block {
  margin-bottom: 30rpx;
}
/* 标题区域 */
.section-header {
  position: relative;
  z-index: 20;
  padding: 30rpx 30rpx 20rpx 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #003399;
}
.job-content {
  position: relative;
  z-index: 20;
}
.job-item {
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f2f2f5;
  box-sizing: border-box;
}
.job-item:last-child {
  margin-bottom: 0;
}
.job-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
  width: 100%;
}
.job-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #000000;
  flex: 1;
  margin-right: 20rpx;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  min-width: 0;
}
.job-salary {
  font-size: 28rpx;
  color: #003399;
  font-weight: bold;
  flex-shrink: 0;
  white-space: nowrap;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}
.job-company {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.3;
}
.job-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
  width: 100%;
}
.job-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #d7e6ff;
  padding: 6rpx 16rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
  border-radius: 6rpx;
  white-space: nowrap;
  max-width: 150rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}
.job-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.job-date,
.job-location {
  font-size: 24rpx;
  color: #999;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.3;
}
.job-location {
  text-align: right;
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* 空状态样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 30rpx;
  position: relative;
  z-index: 20;
}
.job-empty {
  padding: 100rpx 30rpx;
}
.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  text-align: center;
}
