"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      webUrl: "",
      pageTitle: "加载中...",
      statusBarHeight: 0,
      webviewHeight: 0,
      loading: true,
      progress: 0,
      showError: false
    };
  },
  onLoad(options) {
    if (options.url) {
      this.webUrl = decodeURIComponent(options.url);
    }
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title);
    }
    this.getSystemInfo();
    common_vendor.index.setNavigationBarTitle({
      title: this.pageTitle
    });
    this.startProgressSimulation();
    common_vendor.index.__f__("log", "at pages/webview/webview.vue:95", "WebView加载URL:", this.webUrl);
  },
  onUnload() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 获取系统信息
    getSystemInfo() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 0;
      const navBarHeight = 44;
      const progressBarHeight = this.loading ? 2 : 0;
      this.webviewHeight = systemInfo.windowHeight - navBarHeight - progressBarHeight;
    },
    // 开始进度模拟
    startProgressSimulation() {
      this.progress = 0;
      this.loading = true;
      const progressTimer = setInterval(() => {
        if (this.progress < 90) {
          this.progress += Math.random() * 15 + 5;
          if (this.progress > 90) {
            this.progress = 90;
          }
        } else {
          clearInterval(progressTimer);
        }
      }, 200);
      this.progressTimer = progressTimer;
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1,
        fail: () => {
          common_vendor.index.reLaunch({
            url: "/pages/index/home"
          });
        }
      });
    },
    // 刷新页面
    refresh() {
      this.loading = true;
      this.progress = 0;
      this.showError = false;
      const originalUrl = this.webUrl;
      this.webUrl = "";
      this.$nextTick(() => {
        this.webUrl = originalUrl;
      });
    },
    // 在浏览器中打开
    openInBrowser() {
      common_vendor.index.showActionSheet({
        itemList: ["复制链接", "分享给朋友"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyUrl();
          } else if (res.tapIndex === 1) {
            this.shareArticle();
          }
        }
      });
    },
    // 分享文章
    shareArticle() {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: this.webUrl,
        title: this.pageTitle,
        summary: "来自天大海棠的精彩内容",
        imageUrl: utils_imageUtils.getImagePath("logo.png"),
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: () => {
          this.copyUrl();
        }
      });
    },
    // 复制链接
    copyUrl() {
      common_vendor.index.setClipboardData({
        data: this.webUrl,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    },
    // 重试加载
    retry() {
      this.refresh();
    },
    // WebView加载完成
    onLoad(e) {
      common_vendor.index.__f__("log", "at pages/webview/webview.vue:233", "WebView加载完成:", e);
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }
      this.progress = 100;
      this.showError = false;
      setTimeout(() => {
        this.loading = false;
        this.getSystemInfo();
      }, 300);
    },
    // WebView加载错误
    onError(e) {
      common_vendor.index.__f__("error", "at pages/webview/webview.vue:253", "WebView加载错误:", e);
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
      }
      this.loading = false;
      this.showError = true;
      common_vendor.index.showToast({
        title: "页面加载失败",
        icon: "none"
      });
    },
    // 接收WebView消息
    onMessage(e) {
      common_vendor.index.__f__("log", "at pages/webview/webview.vue:271", "收到WebView消息:", e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.pageTitle),
    c: common_vendor.o((...args) => $options.refresh && $options.refresh(...args)),
    d: common_vendor.o((...args) => $options.openInBrowser && $options.openInBrowser(...args)),
    e: $data.statusBarHeight + "px",
    f: $data.loading
  }, $data.loading ? {
    g: $data.progress + "%"
  } : {}, {
    h: $data.loading && $data.progress < 50
  }, $data.loading && $data.progress < 50 ? {} : {}, {
    i: $data.webUrl,
    j: common_vendor.o((...args) => $options.onMessage && $options.onMessage(...args)),
    k: common_vendor.o((...args) => $options.onLoad && $options.onLoad(...args)),
    l: common_vendor.o((...args) => $options.onError && $options.onError(...args)),
    m: $data.webviewHeight + "px",
    n: $data.showError
  }, $data.showError ? {
    o: common_vendor.o((...args) => $options.retry && $options.retry(...args)),
    p: common_vendor.o((...args) => $options.copyUrl && $options.copyUrl(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-deb32cb9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/webview/webview.js.map
