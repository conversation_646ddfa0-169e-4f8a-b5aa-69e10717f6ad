
.modal-overlay.data-v-c7f45689 {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}
.modal-container.data-v-c7f45689 {
	width: 600rpx;
	position: relative;
	border-radius: 20rpx;
}
.modal-bg.data-v-c7f45689 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1;
}
.close-btn.data-v-c7f45689 {
	position: absolute;
	top: 0rpx;
	right: 0rpx;
	width: 60rpx;
	height: 60rpx;
	background: #023caa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
	border: 1px solid #fff;
}
.close-icon.data-v-c7f45689 {
	width: 40rpx;
	height: 40rpx;
}
.content-area1.data-v-c7f45689 {
	position: relative;
	z-index: 5;
	padding: 200rpx 70rpx 40rpx 40rpx;
	height: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}
.title.data-v-c7f45689 {
	text-align: center;
	margin-bottom: 30rpx;
}
.description.data-v-c7f45689 {
	margin-bottom: 30rpx;
}
.desc-text.data-v-c7f45689 {
	font-size: 28rpx;
	color: #000;
	font-weight: 900;
}
.benefits-list.data-v-c7f45689 {
	flex: 1;
	margin-bottom: 20rpx;
}
.benefit-item.data-v-c7f45689 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.benefit-number.data-v-c7f45689 {
	width: 30rpx;
	height: 30rpx;
	background: #1a4fa0;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 22rpx;
	font-weight: bold;
	margin-right: 10rpx;
	flex-shrink: 0;
	border: 1px solid #fff;
}
.benefit-text.data-v-c7f45689 {
	font-size: 26rpx;
	color: #333;
	line-height: 1.4;
}
.users-area.data-v-c7f45689 {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}
.user-avatars.data-v-c7f45689 {
	width: 50%;
}
.avatar-item.data-v-c7f45689 {
	margin-right: 10rpx;
}
.avatar-placeholder.data-v-c7f45689 {
	width: 60rpx;
	height: 60rpx;
	background: #ccc;
	border-radius: 50%;
	border: 3rpx solid #fff;
}
.more-indicator.data-v-c7f45689 {
	display: flex;
	align-items: center;
	margin-left: 10rpx;
}
.dot.data-v-c7f45689 {
	color: #999;
	font-size: 20rpx;
	margin: 0 2rpx;
}
.users-count.data-v-c7f45689 {
	font-size: 24rpx;
	color: #333;
}
.action-btn1.data-v-c7f45689 {
	background: #023caa;
	height: 80rpx;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 20rpx rgba(26, 79, 160, 0.3);
}
.btn-text.data-v-c7f45689 {
	color: #fff;
	font-size: 30rpx;
	font-weight: bold;
}
