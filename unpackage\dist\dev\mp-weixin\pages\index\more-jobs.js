"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      jobList: []
      // 职位列表
    };
  },
  onLoad() {
    this.getJobList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 获取职位列表 - 使用与首页相同的API
    async getJobList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:58", "💼 开始获取职位列表...");
        common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:59", "💼 API地址: GET /miniapp/job/enabled");
        const response = await utils_request.request.get("/miniapp/job/enabled");
        if (response && response.success && response.data && response.data.code === 200) {
          let jobData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:64", "💼 原始职位数据:", jobData);
          this.jobList = jobData.map((item) => ({
            id: item.id || item.jobId,
            title: item.title || item.jobTitle || item.name || "暂无职位",
            salary: item.salary || item.salaryRange || "面议",
            company: item.company || item.companyName || "暂无公司信息",
            tags: item.jobTags ? item.jobTags.split(",").map((tag) => tag.trim()).filter((tag) => tag.length > 0) : ["暂无标签信息"],
            date: item.createTime || item.publishTime || item.postTime || "暂无时间",
            location: item.location || item.workLocation || item.city || "暂无地点",
            detailUrl: item.detailUrl || item.jobUrl,
            // 职位详情链接
            companyScale: item.companyScale || "暂无规模",
            // 保留原始数据用于详情页
            rawData: item
          }));
          common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:81", "💼 职位列表获取成功，共", this.jobList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:82", "💼 处理后的职位数据:", this.jobList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:84", "💼 ❌ 职位数据获取失败！");
          this.jobList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-jobs.vue:88", "💼 获取职位列表失败:", error);
        this.jobList = [];
      }
    },
    // 查看职位详情
    viewJobDetail(job) {
      common_vendor.index.__f__("log", "at pages/index/more-jobs.vue:96", "查看职位详情:", job);
      const jobData = encodeURIComponent(JSON.stringify(job.rawData || job));
      common_vendor.index.navigateTo({
        url: `/pages/index/job-detail?jobData=${jobData}`
      });
    },
    // 在webview中打开链接
    openInWebview(url, title) {
      const encodedUrl = encodeURIComponent(url);
      const encodedTitle = encodeURIComponent(title);
      common_vendor.index.navigateTo({
        url: `/pages/webview/webview?url=${encodedUrl}&title=${encodedTitle}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/more-jobs.vue:117", "跳转webview失败:", err);
          common_vendor.index.showToast({
            title: "打开链接失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.jobList && $data.jobList.length > 0
  }, $data.jobList && $data.jobList.length > 0 ? {
    b: common_vendor.f($data.jobList, (job, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(job.title),
        b: common_vendor.t(job.salary),
        c: common_vendor.t(job.company),
        d: job.tags && job.tags.length > 0
      }, job.tags && job.tags.length > 0 ? {
        e: common_vendor.f(job.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        f: common_vendor.t(job.date),
        g: common_vendor.t(job.location),
        h: job.id || index,
        i: common_vendor.o(($event) => $options.viewJobDetail(job), job.id || index)
      });
    })
  } : {
    c: $options.getImagePath("order_icon5.png")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/more-jobs.js.map
