
.industry-container.data-v-71433351 {
	min-height: 100vh;
	background-color: #f5f6fa;
	padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}

/* 搜索和筛选区域 */
.search-filter-section.data-v-71433351 {
	padding: 40rpx 30rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.search-container.data-v-71433351 {
	flex: 1;
	display: flex;
	align-items: center;
	border-radius: 8rpx;
	padding: 0;
}
.search-input.data-v-71433351 {
	flex: 1;
	height: 60rpx;
	font-size: 28rpx;
	color: #333;
	background-color: #f6f7f9;
	padding: 0 20rpx;
	border: 1px solid #dfe0e2;
}
.search-btn.data-v-71433351 {
	background-color: #72a5ff;
	color: #fff;
	display: flex;
	align-items: center;
	border-radius: 6rpx;
	padding: 0 20rpx;
	font-size: 26rpx;
	margin-left: 20rpx;
	height: 60rpx;
}
.filter-btn.data-v-71433351 {
	background-color: #ffb83c;
	color: #fff;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	border-radius: 6rpx;
	gap: 8rpx;
	height: 60rpx;
}
.filter-icon.data-v-71433351 {
	width: 28rpx;
	height: 28rpx;
}
.filter-text.data-v-71433351 {
	font-size: 26rpx;
}

/* 卡片区域 */
.cards-section.data-v-71433351 {
	display: flex;
	padding: 0 30rpx;
	gap: 40rpx;
}
.card.data-v-71433351 {
	flex: 1;
	height: 200rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	position: relative;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	box-shadow: 0 8rpx 16rpx rgba(200, 213, 242,1);
}
.hot-industry.data-v-71433351 {
	background: linear-gradient(180deg, #fddadc 0%, #ffffff 100%);
}
.city-area.data-v-71433351 {
	background: linear-gradient(180deg, #d8fef9 0%, #ffffff 100%);
}
.card-header.data-v-71433351 {
	margin-bottom: 20rpx;
}
.card-title-en.data-v-71433351 {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1;
}
.card-title-zh.data-v-71433351 {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-top: 20rpx;
}
.card-content.data-v-71433351 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.card-desc-hot.data-v-71433351 {
	font-size: 28rpx;
	color: #dd5e5f;
}
.card-desc-area.data-v-71433351 {
	font-size: 28rpx;
	color: #21a19e;
}
.card-arrow.data-v-71433351 {
	width: 32rpx;
	height: 32rpx;
}

/* 区块通用样式 */
.section-block.data-v-71433351 {
	margin: 30rpx 30rpx;
	border-radius: 20rpx;
	background-color: #fff;
	position: relative;
	box-shadow: 0 8rpx 16rpx rgba(200, 213, 242,1);
}
.list_bg.data-v-71433351 {
	width: 100%;
	position: absolute;
	top: -4rpx;
	left: 0;
	z-index: 16;
	border-radius: 20rpx;
}
.section-header.data-v-71433351 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	position: relative;
}
.section-title.data-v-71433351 {
	font-size: 32rpx;
	font-weight: 900;
	color: #003399;
	margin-left: 20rpx;
	z-index: 100;
}
.more-link.data-v-71433351 {
	font-size: 28rpx;
	color: #003399;
	margin-right: 10rpx;
	z-index: 100;
}

/* 产业列表 */
.industry-list.data-v-71433351 {
	padding: 30rpx;
	position: relative;
	z-index: 20;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.industry-item.data-v-71433351 {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.industry-item.data-v-71433351:last-child {
	border-bottom: none;
}
.industry-icon.data-v-71433351 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	margin-right: 24rpx;
}
.industry-info.data-v-71433351 {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.industry-name.data-v-71433351 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}
.industry-count.data-v-71433351 {
	font-size: 26rpx;
	color: #999;
}
.industry-arrow.data-v-71433351 {
	width: 32rpx;
	height: 32rpx;
}

/* 底部导航栏 */
.tab-bar.data-v-71433351 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	background-color: #fff;
	display: flex;
	justify-content: space-around;
	align-items: center;
	box-shadow: 0 -6rpx 16rpx rgba(200, 213, 242,1);
	z-index: 100;
	padding-bottom: 20rpx;
}
.tab-item.data-v-71433351 {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 20%;
	position: relative;
}
.tab-icon.data-v-71433351 {
	width: 44rpx;
	height: 44rpx;
}
.tab-text.data-v-71433351 {
	font-size: 22rpx;
	color: #444;
}
.tab-item.active .tab-text.data-v-71433351 {
	color: #003399;
}
.tab-center-icon.data-v-71433351 {
	width: 88rpx;
	height: 88rpx;
	background-color: #003399;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 6rpx;
	position: relative;
	top: -48rpx;
}
.tab-center-image.data-v-71433351 {
	width: 60rpx;
	height: 60rpx;
}
.tab-center-text.data-v-71433351 {
	font-size: 22rpx;
	color: #444;
	position: absolute;
	bottom: 12rpx;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
}

/* 遮罩层 */
.filter-mask.data-v-71433351 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

/* 筛选面板样式 */
.filter-panel.data-v-71433351 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #f8f9fa;
	z-index: 1000;
	animation: slideDown-71433351 0.3s ease-out;
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 搜索关键词样式 */
.search-keywords.data-v-71433351 {
	padding: 20rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.search-keywords .keywords-label.data-v-71433351 {
	font-size: 28rpx;
	color: #023caa;
	display: block;
	font-weight: 900;
}
.search-keywords .keyword-tag.data-v-71433351 {
	background-color: #eaeaea;
	border-radius: 100rpx;
	padding: 12rpx 26rpx;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.search-keywords .keyword-tag .keyword-text.data-v-71433351 {
	font-size: 24rpx;
	color: #444444;
}
@keyframes slideDown-71433351 {
from {
		transform: translateY(-100%);
}
to {
		transform: translateY(0);
}
}
.filter-body.data-v-71433351 {
	display: flex;
	height: 550rpx;
	background-color: #fff;
}
.filter-sidebar.data-v-71433351 {
	width: 160rpx;
	background-color: #eaeaea;
}
.filter-category.data-v-71433351 {
	padding: 20rpx;
	position: relative;
	cursor: pointer;
}
.filter-category .category-name.data-v-71433351 {
	font-size: 24rpx;
	color: #666;
}
.filter-category.active.data-v-71433351 {
	background-color: #fff;
}
.filter-category.active .category-name.data-v-71433351 {
	color: #003399;
	font-weight: bold;
}
.filter-category.active.data-v-71433351::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4rpx;
	height: 30rpx;
	background-color: #003399;
	border-radius: 0 2rpx 2rpx 0;
}
.filter-options.data-v-71433351 {
	flex: 1;
	padding: 20rpx;
	box-sizing: border-box;
}
.filter-section.data-v-71433351 {
	padding-bottom: 60rpx;
	min-height: 80rpx;
}
.filter-section .section-title.data-v-71433351 {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-top: 5rpx;
}
.options-grid.data-v-71433351 {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 25rpx;
}
.option-item.data-v-71433351 {
	padding: 12rpx 16rpx;
	background-color: #eaeaea;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}
.option-item .option-text.data-v-71433351 {
	font-size: 22rpx;
	color: #444;
	text-align: center;
}
.option-item .option-percentage.data-v-71433351 {
	font-size: 18rpx;
	color: #666;
	margin-left: 8rpx;
}

/* 弹窗遮罩层 */
.modal-mask.data-v-71433351 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
}

/* 热点产业弹窗 */
.industry-modal.data-v-71433351 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #f8f9fa;
	z-index: 1000;
	animation: slideDown-71433351 0.3s ease-out;
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 城市地区弹窗 */
.city-modal.data-v-71433351 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background-color: #f8f9fa;
	z-index: 1000;
	animation: slideDown-71433351 0.3s ease-out;
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 弹窗内容 */
.modal-content.data-v-71433351 {
	padding: 40rpx 0rpx;
	background-color: #fff;
}
.content-title.data-v-71433351 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin: 0 40rpx;
}

/* 热点产业网格 */
.industry-grid.data-v-71433351 {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-top: 40rpx;
}
.industry-grid-item.data-v-71433351 {
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}
.industry-image.data-v-71433351 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
}
.industry-grid .industry-name.data-v-71433351 {
	font-size: 26rpx;
	color: #333;
	text-align: center;
}

/* 城市地区网格 */
.city-grid.data-v-71433351 {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-top: 40rpx;
}
.city-grid-item.data-v-71433351 {
	width: 25%;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}
.city-image.data-v-71433351 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
}
.city-name.data-v-71433351 {
	font-size: 26rpx;
	color: #333;
	text-align: center;
}
.option-item.selected.data-v-71433351 {
	background-color: #023caa;
}
.option-item.selected .option-text.data-v-71433351 {
	color: #fff;
}
.option-item.selected .option-percentage.data-v-71433351 {
	color: #fff;
}

/* 产业列表空状态样式 */
.industry-empty-state.data-v-71433351 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 30rpx;
	text-align: center;
}
.industry-empty-state .empty-text.data-v-71433351 {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 10rpx;
}
.industry-empty-state .empty-desc.data-v-71433351 {
	font-size: 24rpx;
	color: #999;
}

/* 弹窗空状态样式 */
.modal-empty-state.data-v-71433351 {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 80rpx 30rpx;
	text-align: center;
}
.modal-empty-state .empty-text.data-v-71433351 {
	font-size: 28rpx;
	color: #999;
}

/* 右侧悬浮按钮 - 与需求广场样式一致 */
.floating-buttons.data-v-71433351 {
	position: fixed;
	right: 0rpx;
	top: 36%;
	transform: translateY(-50%);
	z-index: 999;
	display: flex;
	flex-direction: column;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(193, 197, 212, 1);
}
.floating-btn.data-v-71433351 {
	width: 100rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}
.floating-btn.demand-btn.data-v-71433351 {
	background: #fad676;
	color: #333;
}
.floating-btn.info-btn.data-v-71433351 {
	background: #023caa;
	color: white;
}
.floating-btn-text.data-v-71433351 {
	font-size: 26rpx;
}
