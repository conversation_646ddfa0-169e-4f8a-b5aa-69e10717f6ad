.company-profile-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 20rpx;
}
/* 表单容器 */
.form-container {
  background-color: #fff;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
}
/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.form-label-note {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}
.required-star {
  color: #ff4757;
  margin-right: 4rpx;
}
.form-input {
  width: 100%;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.form-input.input-error {
  border-color: #ff4757;
}
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
  margin-top: 20rpx;
}
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  margin-right: 8rpx;
}
/* 选择器 */
.selector-picker {
  width: 100%;
}
.picker-input {
  width: 100%;
  height: 76rpx;
  background: #fbfbfb;
  border: 2rpx solid #E9ECEF;
  border-radius: 12rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
.picker-input.input-error {
  border-color: #ff4757;
}
.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.picker-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}
/* Logo上传 */
.logo-upload-section {
  margin-top: 16rpx;
}
.logo-upload-btn {
  width: 120rpx;
  height: 120rpx;
  background: #f5f5f5;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.uploaded-logo {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.upload-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 行业选择器入口样式 */
.industry-selector {
  margin-top: 16rpx;
}
.industry-selector .picker-input .picker-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 40rpx);
}
/* 行业选择器弹窗样式 */
.industry-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.industry-picker-content {
  width: 100%;
  max-height: 85vh;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 0 40rpx 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}
.industry-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}
.industry-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.industry-actions {
  display: flex;
  align-items: center;
  gap: 40rpx;
}
.cancel-btn {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
}
.confirm-btn {
  font-size: 28rpx;
  color: #007aff;
  padding: 10rpx 20rpx;
  font-weight: bold;
}
/* 树形选择器容器 */
.industry-tree-container {
  flex: 1;
  max-height: 60vh;
  padding: 20rpx 0;
}
/* 树形节点样式 */
.tree-node {
  margin-bottom: 0;
}
.tree-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}
.tree-item:active {
  background-color: #f0f0f0;
}
.tree-item.disabled {
  opacity: 0.5;
}
.tree-item.disabled:active {
  background-color: transparent;
}
.tree-content {
  display: flex;
  align-items: center;
  flex: 1;
}
.expand-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  transition: transform 0.2s ease;
  margin-right: 20rpx;
}
.expand-icon.expanded {
  transform: rotate(0deg);
}
.tree-label {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.no-children-hint {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}
/* 复选框样式 */
.tree-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 20rpx;
}
.tree-checkbox.checked {
  border-color: #007aff;
  background-color: #007aff;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 50%;
}
/* 一级分类样式 */
.level1-node {
  border-bottom: 1rpx solid #e5e5e5;
}
.level1-item {
  background-color: #fafafa;
}
.level1-item .tree-label {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
/* 二级分类样式 */
.tree-children {
  background-color: #fff;
}
.level2-children {
  padding-left: 40rpx;
}
.level2-item {
  background-color: #f9f9f9;
}
.level2-item .tree-label {
  font-size: 28rpx;
  color: #555;
}
.level3-children {
  padding-left: 80rpx;
}
/* 三级分类样式 */
.level3-item {
  background-color: #fff;
}
.level3-item .tree-label {
  font-size: 26rpx;
  color: #666;
}
.level3-item .expand-icon {
  display: none;
  /* 三级分类不需要展开图标 */
}
/* 流向分类样式 */
.stream-category {
  margin-bottom: 20rpx;
}
.stream-title {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx 40rpx;
  background-color: #f0f0f0;
  border-left: 4rpx solid #007aff;
  margin-bottom: 10rpx;
}
.upload-plus {
  font-size: 48rpx;
  color: #999;
  font-weight: 300;
}
/* 多选框网格 */
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 24rpx;
}
.checkbox-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}
.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox.checked {
  background-color: #2196F3;
  border-color: #2196F3;
}
.check-mark {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}
.checkbox-label {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.2;
}
/* 提交按钮 */
.submit-section {
  padding: 40rpx 30rpx 60rpx;
}
.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 44rpx;
  border: none;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn.disabled {
  background: #E9ECEF;
  color: #999;
}
.submit-btn:not(.disabled):active {
  transform: scale(0.98);
}
/* 状态提示条 */
.status-bar {
  margin: 0rpx 30rpx 20rpx;
  padding: 24rpx 30rpx;
  border-radius: 12rpx;
  border-left: 8rpx solid;
}
.status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.status-text {
  font-size: 28rpx;
  flex: 1;
  line-height: 1.4;
}
.status-action {
  font-size: 28rpx;
  font-weight: 600;
  margin-left: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
}
/* 不同状态的样式 */
.status-info {
  background: #E3F2FD;
  border-left-color: #2196F3;
}
.status-info .status-text {
  color: #1976D2;
}
.status-info .status-action {
  color: #1976D2;
  background: rgba(33, 150, 243, 0.1);
}
.status-warning {
  background: #FFF3E0;
  border-left-color: #FF9800;
}
.status-warning .status-text {
  color: #F57C00;
}
.status-warning .status-action {
  color: #F57C00;
  background: rgba(255, 152, 0, 0.1);
}
.status-success {
  background: #E8F5E8;
  border-left-color: #4CAF50;
}
.status-success .status-text {
  color: #388E3C;
}
.status-success .status-action {
  color: #388E3C;
  background: rgba(76, 175, 80, 0.1);
}
.status-error {
  background: #FFEBEE;
  border-left-color: #F44336;
}
.status-error .status-text {
  color: #D32F2F;
}
.status-error .status-action {
  color: #D32F2F;
  background: rgba(244, 67, 54, 0.1);
}
.status-pending {
  background: #FFF8E1;
  border-left-color: #FFC107;
}
.status-pending .status-text {
  color: #F9A825;
}
.status-pending .status-action {
  color: #F9A825;
  background: rgba(255, 193, 7, 0.1);
}
/* 错误提示文本 */
.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 8rpx;
  margin-left: 8rpx;
}
