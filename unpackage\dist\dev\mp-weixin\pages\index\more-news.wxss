.more-news-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #d3e2ff, #fff);
  padding-bottom: 120rpx;
  overflow-x: hidden;
  box-sizing: border-box;
}
/* 内容区域 - 纯色背景 */
.content-area {
  position: relative;
  z-index: 0;
  width: 100%;
  box-sizing: border-box;
}
/* 新闻中心区域 */
.section-block {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.7);
  width: 100%;
  box-sizing: border-box;
  padding-top: 30rpx;
}
.list_bg {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.news-block {
  margin-bottom: 30rpx;
}
/* 标题区域 */
.section-header {
  position: relative;
  z-index: 20;
  padding: 30rpx 30rpx 20rpx 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #003399;
}
.news-content {
  position: relative;
  z-index: 20;
}
.news-item {
  width: 100%;
  margin-bottom: 30rpx;
  display: flex;
  align-items: flex-start;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f2f2f5;
  box-sizing: border-box;
}
.news-item:last-child {
  margin-bottom: 0;
}
.news-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  object-fit: cover;
  flex-shrink: 0;
}
.news-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}
.news-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #000000;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-all;
}
.news-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
/* 空状态样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 30rpx;
  position: relative;
  z-index: 20;
}
.news-empty {
  padding: 100rpx 30rpx;
}
.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  text-align: center;
}
