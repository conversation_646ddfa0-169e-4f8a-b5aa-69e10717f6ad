"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      // 签到相关状态
      isTodayCheckedIn: false,
      // 今日是否已签到
      checkinLoading: false,
      // 签到加载状态
      checkinStats: null,
      // 签到统计信息
      calendarData: null,
      // 签到日历数据
      makeupLoading: false,
      // 补签加载状态
      availableMakeupDates: [],
      // 可补签日期列表
      availableActivities: [],
      // 可参与的活动列表
      joinActivityLoading: false,
      // 参与活动加载状态
      checkinConfig: null,
      // 签到配置参数
      goodsList: [],
      // 商品列表
      goodsLoading: false,
      // 商品加载状态
      selectedCategory: "",
      // 选中的商品分类
      // 图片相关
      inviteImage: "",
      // 邀请注册图片
      consultingImage: "",
      // 咨询服务图片
      giftImage: "",
      // 文创礼品图片
      makeupExplainImage: "",
      // 补签说明弹框图片
      // 弹框状态
      showMakeupExplainDialog: false,
      // 补签说明弹框显示状态
      // 任务配置
      taskList: [],
      // 任务列表
      taskLoading: false,
      // 任务加载状态
      // 签到提醒开关
      reminderEnabled: true,
      // 日历展开状态
      calendarExpanded: false,
      // 当前日期信息
      currentDate: /* @__PURE__ */ new Date(),
      // 当前显示的年月
      currentYear: (/* @__PURE__ */ new Date()).getFullYear(),
      currentMonth: (/* @__PURE__ */ new Date()).getMonth() + 1,
      // 签到记录（示例数据，实际应从服务器获取）
      checkinRecords: {},
      // 处理签到任务（每日签到和连续签到）
      handleCheckinTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:339", "📋 处理签到任务：滑到最上面、展开月度签到");
        common_vendor.index.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
        this.calendarExpanded = true;
      },
      // 处理邀请任务
      handleInviteTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:353", "📋 处理邀请任务：进入邀请页面");
        common_vendor.index.navigateTo({
          url: "/pages/points/invite"
        });
      },
      // 处理需求发布任务
      handleDemandPublishTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:363", "📋 处理需求发布任务：进入需求发布页面");
        common_vendor.index.navigateTo({
          url: "/pages/demand/type-select"
        });
      },
      // 处理分享任务
      handleShareTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:373", "📋 处理分享任务：弹框提示点击右上角三个点分享");
        this.showShareTip();
      },
      // 处理线上活动任务
      handleOnlineActivityTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:381", "📋 处理线上活动任务：跳转到活动报名页面");
        common_vendor.index.navigateTo({
          url: "/pages/competition/guidance?type=activity"
        });
      },
      // 处理发送弹幕任务
      handleSendBarrageTask() {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:391", "📋 处理发送弹幕任务：跳转到首页并点击发送弹幕按钮");
        common_vendor.index.navigateTo({
          url: "/pages/index/home?autoBarrage=true"
        });
      },
      // 任务弹窗显示状态
      showTaskModal: false
    };
  },
  mounted() {
    this.checkTodayCheckinStatus();
    this.getCheckinStats();
    this.getCheckinConfig();
    this.getAvailableMakeupDates();
    this.getAvailableActivities();
    this.getThreeMonthsCalendar();
    this.getTopImages();
    this.getTaskList();
  },
  computed: {
    // 当前年月显示文本
    currentYearMonth() {
      return `${this.currentYear}年${this.currentMonth.toString().padStart(2, "0")}月`;
    },
    // 动态生成完整月历
    fullMonthDays() {
      const year = this.currentYear;
      const month = this.currentMonth - 1;
      const today = /* @__PURE__ */ new Date();
      const todayStr = this.formatDate(today);
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const daysInMonth = lastDay.getDate();
      const firstDayOfWeek = firstDay.getDay();
      const calendar = [];
      let currentWeek = [];
      for (let i = 0; i < firstDayOfWeek; i++) {
        currentWeek.push({
          number: "",
          status: "",
          checked: false,
          isToday: false,
          supplement: false,
          waiting: false,
          isEmpty: true
        });
      }
      for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = this.formatDate(new Date(year, month, day));
        const dayData = this.checkinRecords[dateStr] || {};
        const isToday = dateStr === todayStr;
        let status = "待签";
        let checked = false;
        let supplement = false;
        let waiting = true;
        let hasReward = false;
        let canMakeup = false;
        const isInAvailableList = this.availableMakeupDates && this.availableMakeupDates.includes(dateStr);
        const isInCalendarList = this.calendarData && this.calendarData.availableMakeupDates && this.calendarData.availableMakeupDates.includes(dateStr);
        const isCanMakeup = isInAvailableList || isInCalendarList;
        if (dayData.checked) {
          status = "已签到";
          checked = true;
          waiting = false;
        } else if (dayData.supplement && !dayData.checked) {
          status = "补签";
          supplement = true;
          waiting = false;
        } else if (isToday) {
          status = "今日签到";
          waiting = false;
          hasReward = dayData.hasReward || false;
        } else if (isCanMakeup) {
          status = "补签";
          canMakeup = true;
          waiting = false;
        }
        currentWeek.push({
          number: day.toString().padStart(2, "0"),
          status,
          checked,
          isToday,
          supplement,
          waiting,
          isEmpty: false,
          hasReward,
          canMakeup,
          dateStr
        });
        if (currentWeek.length === 7) {
          calendar.push(currentWeek);
          currentWeek = [];
        }
      }
      while (currentWeek.length < 7 && currentWeek.length > 0) {
        currentWeek.push({
          number: "",
          status: "",
          checked: false,
          isToday: false,
          supplement: false,
          waiting: false,
          isEmpty: true
        });
      }
      if (currentWeek.length > 0) {
        calendar.push(currentWeek);
      }
      return calendar;
    },
    // 动态生成当前周的签到数据
    currentWeekDays() {
      const today = /* @__PURE__ */ new Date();
      const currentWeekStart = new Date(today);
      const dayOfWeek = today.getDay();
      currentWeekStart.setDate(today.getDate() - dayOfWeek);
      const weekDays = [];
      const weekLabels = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      for (let i = 0; i < 7; i++) {
        const date = new Date(currentWeekStart);
        date.setDate(currentWeekStart.getDate() + i);
        const dateStr = this.formatDate(date);
        const dayData = this.checkinRecords[dateStr] || {};
        const isToday = dateStr === this.formatDate(today);
        let status = "待签";
        let checked = false;
        let supplement = false;
        let waiting = true;
        let canMakeup = false;
        let hasReward = false;
        const isInAvailableList = this.availableMakeupDates && this.availableMakeupDates.includes(dateStr);
        const isInCalendarList = this.calendarData && this.calendarData.availableMakeupDates && this.calendarData.availableMakeupDates.includes(dateStr);
        const isCanMakeup = isInAvailableList || isInCalendarList;
        if (dayData.checked) {
          status = "已签到";
          checked = true;
          waiting = false;
        } else if (dayData.supplement && !dayData.checked) {
          status = "补签";
          supplement = true;
          waiting = false;
        } else if (isToday) {
          status = "今日签到";
          waiting = false;
          hasReward = dayData.hasReward || false;
        } else if (isCanMakeup) {
          status = "补签";
          canMakeup = true;
          waiting = false;
        }
        weekDays.push({
          label: weekLabels[i],
          number: date.getDate().toString().padStart(2, "0"),
          status,
          checked,
          isToday,
          supplement,
          waiting,
          canMakeup,
          hasReward,
          dateStr
        });
      }
      return weekDays;
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 处理签到
    async handleCheckin() {
      var _a;
      if (this.checkinLoading) {
        return;
      }
      try {
        this.checkinLoading = true;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:627", "🎯 开始执行签到...");
        const response = await utils_request.request.post("/miniapp/checkin/app/checkin");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:630", "🎯 签到API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const data = response.data.data;
          this.isTodayCheckedIn = true;
          let successMessage = data.message || "签到成功";
          if (data.pointsEarned > 0) {
            successMessage += `，获得${data.pointsEarned}积分！`;
          }
          if (data.hasMonthlySevenDaysBonus && data.monthlyBonusMessage) {
            successMessage += `
${data.monthlyBonusMessage}`;
          }
          if (data.completedActivities && data.completedActivities.length > 0) {
            data.completedActivities.forEach((activity) => {
              successMessage += `
完成活动：${activity.activityName}，获得${activity.rewardPoints}积分！`;
            });
          }
          common_vendor.index.showModal({
            title: "签到成功",
            content: successMessage,
            showCancel: false
          });
          this.getCheckinStats();
          this.getAvailableMakeupDates();
          this.getThreeMonthsCalendar();
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "签到失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:679", "🎯 签到失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.checkinLoading = false;
      }
    },
    // 检查今日签到状态
    async checkTodayCheckinStatus() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:692", "🎯 检查今日签到状态...");
        const response = await utils_request.request.get("/miniapp/checkin/app/checkTodayStatus");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:695", "🎯 签到状态API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.isTodayCheckedIn = response.data.data === true;
          common_vendor.index.__f__("log", "at pages/points/mall.vue:699", "🎯 今日签到状态:", this.isTodayCheckedIn);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:701", "🎯 获取签到状态失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:705", "🎯 检查签到状态失败:", error);
      }
    },
    // 获取签到统计信息
    async getCheckinStats() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:712", "🎯 获取签到统计信息...");
        const response = await utils_request.request.get("/miniapp/checkin/app/getStats");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:715", "🎯 签到统计API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.checkinStats = response.data.data;
          common_vendor.index.__f__("log", "at pages/points/mall.vue:719", "🎯 签到统计信息:", this.checkinStats);
          this.isTodayCheckedIn = this.checkinStats.isTodayCheckedIn;
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:725", "🎯 获取签到统计失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:729", "🎯 获取签到统计失败:", error);
      }
    },
    // 获取签到配置参数
    async getCheckinConfig() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:736", "🎯 获取签到配置参数...");
        const response = await utils_request.request.get("/miniapp/checkin/app/getConfig");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:739", "🎯 签到配置API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.checkinConfig = response.data.data;
          common_vendor.index.__f__("log", "at pages/points/mall.vue:743", "🎯 签到配置参数:", this.checkinConfig);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:746", "🎯 获取签到配置失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:750", "🎯 获取签到配置失败:", error);
      }
    },
    // 获取三个月的签到日历数据（上月、本月、下月）
    async getThreeMonthsCalendar() {
      try {
        const currentDate = /* @__PURE__ */ new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        const lastYear = currentMonth === 1 ? currentYear - 1 : currentYear;
        const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
        const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:767", "🎯 获取三个月签到日历数据...", {
          last: { year: lastYear, month: lastMonth },
          current: { year: currentYear, month: currentMonth },
          next: { year: nextYear, month: nextMonth }
        });
        const [lastMonthRes, currentMonthRes, nextMonthRes] = await Promise.all([
          utils_request.request.get("/miniapp/checkin/app/getCalendar", { year: lastYear, month: lastMonth }),
          utils_request.request.get("/miniapp/checkin/app/getCalendar", { year: currentYear, month: currentMonth }),
          utils_request.request.get("/miniapp/checkin/app/getCalendar", { year: nextYear, month: nextMonth })
        ]);
        common_vendor.index.__f__("log", "at pages/points/mall.vue:780", "🎯 三个月日历API响应:", { lastMonthRes, currentMonthRes, nextMonthRes });
        this.mergeThreeMonthsCalendarData(lastMonthRes, currentMonthRes, nextMonthRes);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:786", "🎯 获取三个月签到日历失败:", error);
      }
    },
    // 合并三个月的日历数据
    mergeThreeMonthsCalendarData(lastMonthRes, currentMonthRes, nextMonthRes) {
      var _a, _b, _c;
      const allCalendarData = {
        normalCheckinDates: [],
        makeupCheckinDates: [],
        availableMakeupDates: []
      };
      if (((_a = lastMonthRes == null ? void 0 : lastMonthRes.data) == null ? void 0 : _a.code) === 200 && lastMonthRes.data.data) {
        const data = lastMonthRes.data.data;
        allCalendarData.normalCheckinDates.push(...data.normalCheckinDates || []);
        allCalendarData.makeupCheckinDates.push(...data.makeupCheckinDates || []);
        allCalendarData.availableMakeupDates.push(...data.availableMakeupDates || []);
      }
      if (((_b = currentMonthRes == null ? void 0 : currentMonthRes.data) == null ? void 0 : _b.code) === 200 && currentMonthRes.data.data) {
        const data = currentMonthRes.data.data;
        allCalendarData.normalCheckinDates.push(...data.normalCheckinDates || []);
        allCalendarData.makeupCheckinDates.push(...data.makeupCheckinDates || []);
        allCalendarData.availableMakeupDates.push(...data.availableMakeupDates || []);
        this.calendarData = data;
      }
      if (((_c = nextMonthRes == null ? void 0 : nextMonthRes.data) == null ? void 0 : _c.code) === 200 && nextMonthRes.data.data) {
        const data = nextMonthRes.data.data;
        allCalendarData.normalCheckinDates.push(...data.normalCheckinDates || []);
        allCalendarData.makeupCheckinDates.push(...data.makeupCheckinDates || []);
        allCalendarData.availableMakeupDates.push(...data.availableMakeupDates || []);
      }
      common_vendor.index.__f__("log", "at pages/points/mall.vue:825", "🎯 合并后的三个月日历数据:", allCalendarData);
      this.updateCheckinRecordsFromMergedData(allCalendarData);
    },
    // 根据合并的三个月数据更新签到记录
    updateCheckinRecordsFromMergedData(allCalendarData) {
      var _a, _b;
      const newRecords = {};
      (_a = allCalendarData.normalCheckinDates) == null ? void 0 : _a.forEach((dateStr) => {
        newRecords[dateStr] = {
          checked: true,
          status: "已签到"
        };
      });
      (_b = allCalendarData.makeupCheckinDates) == null ? void 0 : _b.forEach((dateStr) => {
        newRecords[dateStr] = {
          checked: true,
          status: "已签到",
          supplement: true
        };
      });
      const makeupDates = this.availableMakeupDates.length > 0 ? this.availableMakeupDates : allCalendarData.availableMakeupDates || [];
      makeupDates.forEach((dateStr) => {
        if (!newRecords[dateStr]) {
          newRecords[dateStr] = {
            checked: false,
            status: "可补签",
            supplement: true,
            canMakeup: true
          };
        }
      });
      this.checkinRecords = { ...this.checkinRecords, ...newRecords };
      common_vendor.index.__f__("log", "at pages/points/mall.vue:870", "🎯 更新后的签到记录（三个月）:", this.checkinRecords);
    },
    // 获取签到日历数据（单月，用于月份切换）
    async getCheckinCalendar(year = null, month = null) {
      var _a;
      try {
        const targetYear = year || this.currentYear;
        const targetMonth = month || this.currentMonth;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:880", "🎯 获取签到日历数据...", { year: targetYear, month: targetMonth });
        const response = await utils_request.request.get("/miniapp/checkin/app/getCalendar", {
          year: targetYear,
          month: targetMonth
        });
        common_vendor.index.__f__("log", "at pages/points/mall.vue:886", "🎯 签到日历API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.calendarData = response.data.data;
          common_vendor.index.__f__("log", "at pages/points/mall.vue:890", "🎯 签到日历数据:", this.calendarData);
          this.updateCheckinRecordsFromCalendar();
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:896", "🎯 获取签到日历失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:900", "🎯 获取签到日历失败:", error);
      }
    },
    // 根据日历数据更新签到记录
    updateCheckinRecordsFromCalendar() {
      var _a, _b;
      if (!this.calendarData)
        return;
      const newRecords = {};
      (_a = this.calendarData.normalCheckinDates) == null ? void 0 : _a.forEach((dateStr) => {
        newRecords[dateStr] = {
          checked: true,
          status: "已签到"
        };
      });
      (_b = this.calendarData.makeupCheckinDates) == null ? void 0 : _b.forEach((dateStr) => {
        newRecords[dateStr] = {
          checked: true,
          status: "已签到",
          supplement: true
        };
      });
      const makeupDates = this.availableMakeupDates.length > 0 ? this.availableMakeupDates : this.calendarData.availableMakeupDates || [];
      makeupDates.forEach((dateStr) => {
        if (!newRecords[dateStr]) {
          newRecords[dateStr] = {
            checked: false,
            status: "可补签",
            supplement: true,
            canMakeup: true
          };
        }
      });
      this.checkinRecords = { ...this.checkinRecords, ...newRecords };
      common_vendor.index.__f__("log", "at pages/points/mall.vue:945", "🎯 更新后的签到记录:", this.checkinRecords);
    },
    // 处理日期点击
    handleDayTap(day) {
      common_vendor.index.__f__("log", "at pages/points/mall.vue:950", "🎯 点击日期:", day);
      if (day.isToday && !day.checked && !this.isTodayCheckedIn) {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:954", "🎯 点击今日日期，执行签到");
        this.handleCheckin();
        return;
      }
      if (day.checked) {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:961", "🎯 该日期已签到");
        return;
      }
      const isInAvailableList = this.availableMakeupDates && this.availableMakeupDates.includes(day.dateStr);
      const isInCalendarList = this.calendarData && this.calendarData.availableMakeupDates && this.calendarData.availableMakeupDates.includes(day.dateStr);
      const canMakeup = isInAvailableList || isInCalendarList;
      common_vendor.index.__f__("log", "at pages/points/mall.vue:970", "🎯 补签检查:", {
        dateStr: day.dateStr,
        isInAvailableList,
        isInCalendarList,
        canMakeup,
        checked: day.checked
      });
      if (canMakeup && !day.checked && day.dateStr) {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:980", "🎯 开始补签:", day.dateStr);
        this.handleMakeup(day.dateStr);
      } else {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:983", "🎯 该日期不可补签");
      }
    },
    // 用户补签
    async handleMakeup(dateStr) {
      var _a, _b;
      if (this.makeupLoading) {
        return;
      }
      try {
        const makeupCost = ((_a = this.checkinConfig) == null ? void 0 : _a.makeupCost) || 10;
        const confirmResult = await new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认补签",
            content: `补签 ${dateStr} 需要消耗${makeupCost}积分，确认补签吗？`,
            success: (res) => resolve(res.confirm),
            fail: () => resolve(false)
          });
        });
        if (!confirmResult) {
          return;
        }
        this.makeupLoading = true;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1012", "🎯 开始补签...", dateStr);
        const response = await utils_request.request.post(`/miniapp/checkin/app/makeup?makeupDate=${dateStr}`, {
          makeupDate: dateStr
        });
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1017", "🎯 补签API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const data = response.data.data;
          let successMessage = data.message || "补签成功";
          if (data.pointsCost > 0 && data.pointsEarned > 0) {
            successMessage += `
消耗${data.pointsCost}积分，获得${data.pointsEarned}积分`;
          }
          if (data.remainingPoints !== void 0) {
            successMessage += `
剩余积分：${data.remainingPoints}`;
          }
          common_vendor.index.showModal({
            title: "补签成功",
            content: successMessage,
            showCancel: false
          });
          this.getCheckinStats();
          this.getAvailableMakeupDates();
          this.getThreeMonthsCalendar();
        } else {
          const errorMsg = ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.msg) || "补签失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1054", "🎯 补签失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.makeupLoading = false;
      }
    },
    // 获取可补签日期列表
    async getAvailableMakeupDates() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1067", "🎯 获取可补签日期列表...");
        const response = await utils_request.request.get("/miniapp/checkin/app/getAvailableMakeupDates");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1070", "🎯 可补签日期API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.availableMakeupDates = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1074", "🎯 可补签日期列表:", this.availableMakeupDates);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1077", "🎯 获取可补签日期失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          this.availableMakeupDates = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1082", "🎯 获取可补签日期失败:", error);
        this.availableMakeupDates = [];
      }
    },
    // 检查日期是否可以补签
    canMakeupDate(dateStr) {
      if (this.availableMakeupDates && this.availableMakeupDates.length > 0) {
        const result = this.availableMakeupDates.includes(dateStr);
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1092", "🎯 检查可补签日期:", dateStr, "结果:", result, "列表:", this.availableMakeupDates);
        return result;
      }
      if (this.calendarData && this.calendarData.availableMakeupDates) {
        const result = this.calendarData.availableMakeupDates.includes(dateStr);
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1099", "🎯 使用日历数据检查可补签日期:", dateStr, "结果:", result);
        return result;
      }
      common_vendor.index.__f__("log", "at pages/points/mall.vue:1103", "🎯 无可补签数据，返回false:", dateStr);
      return false;
    },
    // 获取可参与的活动列表
    async getAvailableActivities() {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1110", "🎯 获取可参与的活动列表...");
        const response = await utils_request.request.get("/miniapp/checkin/app/getAvailableActivities");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1113", "🎯 活动列表API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.availableActivities = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1117", "🎯 可参与的活动列表:", this.availableActivities);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1120", "🎯 获取活动列表失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          this.availableActivities = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1125", "🎯 获取活动列表失败:", error);
        this.availableActivities = [];
      }
    },
    // 参与签到活动
    async joinActivity(activityId, activityName) {
      var _a;
      if (this.joinActivityLoading) {
        return;
      }
      try {
        const confirmResult = await new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认参与",
            content: `确认参与"${activityName}"活动吗？`,
            success: (res) => resolve(res.confirm),
            fail: () => resolve(false)
          });
        });
        if (!confirmResult) {
          return;
        }
        this.joinActivityLoading = true;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1153", "🎯 参与活动...", { activityId, activityName });
        const response = await utils_request.request.post("/miniapp/checkin/app/joinActivity", {
          activityId
        });
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1158", "🎯 参与活动API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const data = response.data.data;
          common_vendor.index.showToast({
            title: data.message || "参与成功",
            icon: "success",
            duration: 2e3
          });
          this.getAvailableActivities();
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || "参与失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1183", "🎯 参与活动失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.joinActivityLoading = false;
      }
    },
    // 获取商品列表
    async getGoodsList(category = "") {
      var _a;
      try {
        this.goodsLoading = true;
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1197", "🎯 获取商品列表...", { category });
        const params = {};
        if (category) {
          params.goodsCategory = category;
        }
        const response = await utils_request.request.get("/miniapp/points/goods/app/list", params);
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1205", "🎯 商品列表API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.goodsList = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1209", "🎯 商品列表:", this.goodsList);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1212", "🎯 获取商品列表失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          this.goodsList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1217", "🎯 获取商品列表失败:", error);
        this.goodsList = [];
      } finally {
        this.goodsLoading = false;
      }
    },
    // 切换商品分类
    switchCategory(category) {
      this.selectedCategory = category;
      if (category) {
        this.getGoodsList(category);
      } else {
        this.goodsList = [];
      }
    },
    // 查看商品详情
    async viewGoodsDetail(goodsId) {
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1239", "🎯 查看商品详情...", goodsId);
        common_vendor.index.navigateTo({
          url: `/pages/goods/detail?goodsId=${goodsId}`
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1247", "🎯 跳转商品详情失败:", error);
        common_vendor.index.showToast({
          title: "页面跳转失败",
          icon: "none"
        });
      }
    },
    // 切换提醒开关
    toggleReminder(e) {
      this.reminderEnabled = e.detail.value;
      common_vendor.index.showToast({
        title: this.reminderEnabled ? "已开启签到提醒" : "已关闭签到提醒",
        icon: "none"
      });
    },
    // 切换日历展开状态
    toggleCalendar() {
      this.calendarExpanded = !this.calendarExpanded;
    },
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 更新签到记录（实际项目中应该从服务器获取）
    updateCheckinRecords() {
    },
    // 查看月度签到奖励
    viewMonthlyReward() {
      common_vendor.index.navigateTo({
        url: "/pages/points/monthly-reward"
      });
    },
    // 参加挑战赛
    joinChallenge() {
      common_vendor.index.showToast({
        title: "报名成功",
        icon: "success"
      });
    },
    // 做任务
    doTasks() {
      this.showTaskModal = true;
    },
    // 关闭任务弹窗
    closeTaskModal() {
      this.showTaskModal = false;
    },
    // 查看咨询服务
    viewConsultingServices() {
      common_vendor.index.navigateTo({
        url: "/pages/points/products?type=consulting"
      });
    },
    // 查看文创礼品
    viewGifts() {
      common_vendor.index.navigateTo({
        url: "/pages/points/products?type=gifts"
      });
    },
    // 邀请注册
    inviteRegister() {
      common_vendor.index.__f__("log", "at pages/points/mall.vue:1324", "🎁 跳转到邀请注册页面");
      common_vendor.index.navigateTo({
        url: "/pages/points/invite"
      });
    },
    // 显示补签说明弹框
    showMakeupExplain() {
      common_vendor.index.__f__("log", "at pages/points/mall.vue:1332", "❓ 显示补签说明弹框");
      this.showMakeupExplainDialog = true;
    },
    // 关闭补签说明弹框
    closeMakeupExplain() {
      common_vendor.index.__f__("log", "at pages/points/mall.vue:1338", "❓ 关闭补签说明弹框");
      this.showMakeupExplainDialog = false;
    },
    // 获取任务列表
    async getTaskList() {
      var _a, _b;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1345", "📋 开始获取任务列表...");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1346", "📋 API地址: GET /miniapp/taskConfig/enabled");
        this.taskLoading = true;
        const response = await utils_request.request.get("/miniapp/taskConfig/enabled");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1351", "📋 任务列表API响应:", JSON.stringify(response, null, 2));
        if (response && response.data && response.data.code === 200) {
          this.taskList = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1355", "📋 任务列表数据:", this.taskList);
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1357", "📋 获取任务列表失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          this.taskList = [];
          common_vendor.index.showToast({
            title: ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.msg) || "获取任务列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1366", "📋 获取任务列表失败:", error);
        this.taskList = [];
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.taskLoading = false;
      }
    },
    // 获取任务图标
    getTaskIcon(task) {
      if (task.taskIcon) {
        return utils_imageUtils.processServerImageUrl(task.taskIcon, utils_imageUtils.getImagePath("score_icon.png"));
      }
      return utils_imageUtils.getImagePath("score_icon.png");
    },
    // 获取任务按钮样式类
    getTaskButtonClass(task) {
      return "action-btn";
    },
    // 处理任务操作
    handleTaskAction(task) {
      common_vendor.index.__f__("log", "at pages/points/mall.vue:1395", "📋 处理任务操作:", task);
      this.showTaskModal = false;
      switch (task.taskId) {
        case "DAILY_CHECKIN":
        case "CONSECUTIVE_CHECKIN":
          this.handleCheckinTask();
          break;
        case "INVITE_FRIEND":
          this.handleInviteTask();
          break;
        case "DEMAND_PUBLISH":
          this.handleDemandPublishTask();
          break;
        case "SHARE_FRIEND":
          this.handleShareTask();
          break;
        case "ONLINE_ACTIVITY":
          this.handleOnlineActivityTask();
          break;
        case "SEND_BARRAGE":
          this.handleSendBarrageTask();
          break;
        default:
          if (task.actionUrl) {
            common_vendor.index.__f__("log", "at pages/points/mall.vue:1436", "📋 跳转到:", task.actionUrl);
            common_vendor.index.navigateTo({
              url: task.actionUrl
            });
          } else {
            common_vendor.index.showToast({
              title: "功能开发中",
              icon: "none"
            });
          }
      }
    },
    // 获取积分商城相关图片
    async getTopImages() {
      try {
        await this.getTopImageByPage("PointGoodsInvite", "inviteImage");
        await this.getTopImageByPage("PointGoodsService", "consultingImage");
        await this.getTopImageByPage("PointGoodsGift", "giftImage");
        await this.getTopImageByPage("MakeupCheckinImage", "makeupExplainImage");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1465", "🎯 获取积分商城图片失败:", error);
      }
    },
    // 根据页面标识获取图片
    async getTopImageByPage(pageIdentifier, imageField) {
      var _a;
      try {
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1472", "🎯 获取图片...", { pageIdentifier, imageField });
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1473", "🎯 API地址: POST /miniapp/topimage/app/getEnabledListByPage");
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1474", "🎯 请求参数: pageCode =", pageIdentifier);
        const response = await utils_request.request.post("/miniapp/topimage/app/getEnabledListByPage", pageIdentifier);
        common_vendor.index.__f__("log", "at pages/points/mall.vue:1477", "🎯 图片API完整响应:", JSON.stringify(response, null, 2));
        if (response && response.data && response.data.code === 200) {
          const imageList = response.data.data || [];
          if (imageList.length > 0) {
            this[imageField] = utils_imageUtils.processServerImageUrl(imageList[0].imageUrl);
            common_vendor.index.__f__("log", "at pages/points/mall.vue:1484", "🎯 设置图片:", { imageField, url: this[imageField] });
          } else {
            common_vendor.index.__f__("log", "at pages/points/mall.vue:1486", "🎯 该页面标识下没有启用的图片:", pageIdentifier);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/points/mall.vue:1489", "🎯 获取图片失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/mall.vue:1493", "🎯 获取图片失败:", { pageIdentifier, error });
      }
    },
    // 查看更多兑换
    viewMoreExchange() {
      common_vendor.index.showToast({
        title: "更多兑换功能开发中",
        icon: "none"
      });
    },
    // 切换到上一个月
    previousMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--;
        this.currentMonth = 12;
      } else {
        this.currentMonth--;
      }
      this.getCheckinCalendar();
    },
    // 切换到下一个月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++;
        this.currentMonth = 1;
      } else {
        this.currentMonth++;
      }
      this.getCheckinCalendar();
    },
    // 自定义分享标题
    getShareTitle() {
      return "积分商城 - 天大海棠";
    },
    // 自定义分享内容
    getShareContent() {
      return "积分商城页面分享";
    },
    // 自定义分享图片
    getShareImageUrl() {
      return this.getImagePath("share-logo.png");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.t($data.checkinLoading ? "签到中..." : $data.isTodayCheckedIn ? "今日已签到" : "立即签到"),
    b: $data.isTodayCheckedIn ? 1 : "",
    c: $data.checkinLoading ? 1 : "",
    d: common_vendor.o((...args) => $options.handleCheckin && $options.handleCheckin(...args)),
    e: $data.reminderEnabled,
    f: common_vendor.o((...args) => $options.toggleReminder && $options.toggleReminder(...args)),
    g: common_vendor.t($data.checkinStats ? $data.checkinStats.remainingMonthlyMakeup : 0),
    h: $options.getImagePath("question_icon1.png"),
    i: common_vendor.o((...args) => $options.showMakeupExplain && $options.showMakeupExplain(...args)),
    j: $data.calendarExpanded
  }, $data.calendarExpanded ? {
    k: common_vendor.o((...args) => $options.previousMonth && $options.previousMonth(...args)),
    l: common_vendor.t($options.currentYearMonth),
    m: common_vendor.o((...args) => $options.nextMonth && $options.nextMonth(...args))
  } : {}, {
    n: !$data.calendarExpanded
  }, !$data.calendarExpanded ? {
    o: common_vendor.f($options.currentWeekDays, (day, index, i0) => {
      return {
        a: common_vendor.t(day.label),
        b: "label-" + index
      };
    }),
    p: common_vendor.f($options.currentWeekDays, (day, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(day.number),
        b: common_vendor.t(day.status),
        c: day.hasReward
      }, day.hasReward ? {} : {}, {
        d: day.canMakeup && !day.checked
      }, day.canMakeup && !day.checked ? {} : {}, {
        e: index,
        f: day.checked ? 1 : "",
        g: day.isToday ? 1 : "",
        h: day.supplement ? 1 : "",
        i: day.waiting ? 1 : "",
        j: day.canMakeup && !day.checked ? 1 : "",
        k: common_vendor.o(($event) => $options.handleDayTap(day), index)
      });
    })
  } : {
    q: common_vendor.f($options.fullMonthDays, (week, weekIndex, i0) => {
      return {
        a: common_vendor.f(week, (day, dayIndex, i1) => {
          return common_vendor.e({
            a: !day.isEmpty
          }, !day.isEmpty ? {
            b: common_vendor.t(day.number)
          } : {}, {
            c: !day.isEmpty
          }, !day.isEmpty ? {
            d: common_vendor.t(day.status)
          } : {}, {
            e: day.hasReward
          }, day.hasReward ? {} : {}, {
            f: day.canMakeup && !day.checked
          }, day.canMakeup && !day.checked ? {} : {}, {
            g: dayIndex,
            h: day.checked ? 1 : "",
            i: day.isToday ? 1 : "",
            j: day.supplement ? 1 : "",
            k: day.waiting ? 1 : "",
            l: day.isEmpty ? 1 : "",
            m: day.canMakeup && !day.checked ? 1 : "",
            n: common_vendor.o(($event) => $options.handleDayTap(day), dayIndex)
          });
        }),
        b: weekIndex
      };
    })
  }, {
    r: common_vendor.t(7),
    s: common_vendor.t(((_a = $data.checkinConfig) == null ? void 0 : _a.monthlySevenDaysBonus) || 50),
    t: common_vendor.t($data.calendarExpanded ? "收起月度签到" : "查看月度签到"),
    v: $options.getImagePath($data.calendarExpanded ? "arrow_up_blue.png" : "arrow_down_blue.png"),
    w: common_vendor.o((...args) => $options.toggleCalendar && $options.toggleCalendar(...args)),
    x: !$data.calendarExpanded
  }, !$data.calendarExpanded ? common_vendor.e({
    y: common_vendor.f($data.availableActivities, (activity, k0, i0) => {
      return {
        a: common_vendor.t(activity.activity_name),
        b: common_vendor.t(activity.start_date.replace("-", ".")),
        c: common_vendor.t(activity.end_date.slice(5).replace("-", ".")),
        d: common_vendor.t(activity.target_days),
        e: common_vendor.t(activity.reward_points),
        f: common_vendor.t($data.joinActivityLoading ? "参与中..." : activity.canJoin ? "立即参与" : "已参与"),
        g: !activity.canJoin || $data.joinActivityLoading ? 1 : "",
        h: common_vendor.o(($event) => activity.canJoin && !$data.joinActivityLoading ? $options.joinActivity(activity.activityId, activity.activityName) : null, activity.activityId),
        i: activity.activityId
      };
    }),
    z: $data.availableActivities.length === 0
  }, $data.availableActivities.length === 0 ? {} : {}, {
    A: common_vendor.o((...args) => $options.doTasks && $options.doTasks(...args))
  }) : {}, {
    B: $data.inviteImage
  }, $data.inviteImage ? {
    C: $data.inviteImage,
    D: common_vendor.o((...args) => $options.inviteRegister && $options.inviteRegister(...args))
  } : {}, {
    E: $options.getImagePath("list_bg.png"),
    F: $data.consultingImage
  }, $data.consultingImage ? {
    G: $data.consultingImage,
    H: common_vendor.o((...args) => $options.viewConsultingServices && $options.viewConsultingServices(...args))
  } : {}, {
    I: $data.giftImage
  }, $data.giftImage ? {
    J: $data.giftImage,
    K: common_vendor.o((...args) => $options.viewGifts && $options.viewGifts(...args))
  } : {}, {
    L: $data.showTaskModal
  }, $data.showTaskModal ? common_vendor.e({
    M: $options.getImagePath("cha.png"),
    N: common_vendor.o((...args) => $options.closeTaskModal && $options.closeTaskModal(...args)),
    O: $options.getImagePath("mall_title.png"),
    P: $data.taskLoading
  }, $data.taskLoading ? {} : {}, {
    Q: common_vendor.f($data.taskList, (task, index, i0) => {
      return {
        a: $options.getTaskIcon(task),
        b: common_vendor.t(task.taskName || "未知任务"),
        c: common_vendor.t(task.taskDesc || "暂无描述"),
        d: common_vendor.t(task.actionText || "去完成"),
        e: common_vendor.n($options.getTaskButtonClass(task)),
        f: common_vendor.o(($event) => $options.handleTaskAction(task), task.taskId || index),
        g: task.taskId || index
      };
    }),
    R: !$data.taskLoading && $data.taskList.length === 0
  }, !$data.taskLoading && $data.taskList.length === 0 ? {} : {}, {
    S: common_vendor.o(() => {
    }),
    T: common_vendor.o((...args) => $options.closeTaskModal && $options.closeTaskModal(...args))
  }) : {}, {
    U: $data.showMakeupExplainDialog
  }, $data.showMakeupExplainDialog ? common_vendor.e({
    V: $data.makeupExplainImage
  }, $data.makeupExplainImage ? {
    W: $data.makeupExplainImage
  } : {}, {
    X: common_vendor.o((...args) => $options.closeMakeupExplain && $options.closeMakeupExplain(...args)),
    Y: $options.getImagePath("cha.png"),
    Z: common_vendor.o((...args) => $options.closeMakeupExplain && $options.closeMakeupExplain(...args)),
    aa: common_vendor.o(() => {
    }),
    ab: common_vendor.o((...args) => $options.closeMakeupExplain && $options.closeMakeupExplain(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/mall.js.map
