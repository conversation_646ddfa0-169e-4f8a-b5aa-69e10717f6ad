.demand-detail-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
  padding-top: 50rpx;
}
.header-section {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 40rpx 60rpx 40rpx;
  position: relative;
}
.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.main-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
}
.detail-content {
  padding: 30rpx;
  margin-top: -30rpx;
}
.detail-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx #c7cce1;
  padding: 40rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.detail-header {
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
}
.detail-title {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.detail-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20rpx;
}
.info-item {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
  min-width: 200rpx;
}
/* 状态徽章样式 */
.status-badge {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
  color: white;
}
.status-pending {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
  /* 橙色 - 待审核 */
}
.status-published {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  /* 绿色 - 已发布 */
}
.status-docked {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  /* 蓝色 - 已对接 */
}
.status-offline {
  background: linear-gradient(135deg, #8c8c8c, #595959);
  /* 灰色 - 已下架 */
}
.status-rejected {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
  /* 红色 - 审核拒绝 */
}
.status-unknown {
  background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
  /* 浅灰色 - 未知状态 */
}
.top-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}
.detail-section {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 30rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: #1e3c72;
  border-radius: 4rpx;
}
.section-content {
  padding: 0 10rpx;
}
.section-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}
.action-buttons {
  margin-top: 60rpx;
  display: flex;
  justify-content: center;
}
/* 我的发布操作按钮区域 */
.my-publish-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}
.action-btn {
  width: 80%;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}
.jiegang-btn {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(30, 60, 114, 0.3);
}
/* 我的发布操作按钮样式 */
.my-publish-actions .action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  color: white;
  transition: all 0.3s ease;
}
.my-publish-actions .action-btn:active {
  transform: scale(0.98);
}
.docked-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  /* 蓝色 - 设置为已对接 */
}
.offline-btn {
  background: linear-gradient(135deg, #8c8c8c, #595959);
  /* 灰色 - 下架 */
}
.online-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  /* 绿色 - 上架 */
}
.delete-btn {
  background: linear-gradient(135deg, #ff4757, #ff3742);
  /* 红色 - 删除 */
}
/* 表单数据样式 */
.form-data-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #4A90E2;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.field-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
  max-width: 200rpx;
  margin-right: 20rpx;
  word-wrap: break-word;
  flex-shrink: 0;
}
.field-value {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}
/* 联系方式样式 */
.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx 0;
}
.contact-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 120rpx;
  margin-right: 20rpx;
}
.contact-value {
  font-size: 26rpx;
  color: #4A90E2;
  font-weight: 500;
}
/* 对接弹窗样式 */
.dock-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.dock-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
