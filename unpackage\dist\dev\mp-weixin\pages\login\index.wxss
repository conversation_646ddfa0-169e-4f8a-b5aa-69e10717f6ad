.login-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #c3dbff 0%, #f3f8fe 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  padding-top: 120rpx;
  box-sizing: border-box;
}
.status-bar {
  background: transparent;
}
.background-img {
  width: 100%;
}
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0rpx 60rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 2;
}
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  min-height: 500rpx;
  padding-top: 200rpx;
}
.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 40rpx;
}
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}
.logo-wrapper {
  margin-bottom: 30rpx;
}
.logo-img {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background: #1a56db;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -140rpx;
}
.logo-img image {
  width: 130rpx;
  height: 130rpx;
}
.app-title {
  font-size: 38rpx;
  font-weight: 900;
  color: #333;
  text-align: center;
  letter-spacing: 1rpx;
}
.permission-section {
  width: 100%;
  margin-bottom: 80rpx;
}
.permission-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 60rpx;
}
.permission-item:last-child {
  margin-bottom: 0;
}
.permission-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.permission-text {
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
}
.login-section {
  width: 100%;
  margin-bottom: 60rpx;
}
.wechat-login-btn {
  width: 100%;
  height: 88rpx;
  background: #6192ee;
  border-radius: 44rpx;
  border: none;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(97, 146, 238, 0.3);
  margin-bottom: 20rpx;
}
.phone-login-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  border-radius: 44rpx;
  border: none;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.3);
}
.agreement-section {
  z-index: 3;
}
.agreement-label {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  line-height: 1.6;
}
.custom-checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.custom-checkbox.checked {
  border-color: #6192ee;
  background: #6192ee;
}
.checkbox-inner {
  width: 12rpx;
  height: 12rpx;
  background: white;
  border-radius: 100rpx;
}
.agreement-text {
  color: #666;
  font-size: 24rpx;
  margin: 0 4rpx;
}
.agreement-link {
  color: #6192ee;
  font-size: 24rpx;
  text-decoration: none;
  margin: 0 4rpx;
}
/* 用户信息填写区域 */
.user-info-section {
  margin: 60rpx 0;
  padding: 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}
.avatar-section {
  margin-bottom: 40rpx;
}
.avatar-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  background: none;
  border: none;
  border-radius: 0;
  position: relative;
}
.avatar-wrapper::after {
  border: none;
}
.avatar-wrapper[disabled] {
  opacity: 0.7;
}
.avatar-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 2rpx solid #e0e0e0;
}
.avatar-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.avatar-tip.uploading {
  color: #6192ee;
}
.upload-mask {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-mask::before {
  content: '';
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #fff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.nickname-section {
  margin-bottom: 20rpx;
}
.nickname-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.nickname-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.nickname-input:focus {
  border-color: #6192ee;
}
