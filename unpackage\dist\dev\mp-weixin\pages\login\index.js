"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  data() {
    return {
      statusBarHeight: 0,
      isAgreed: false,
      loginLoading: false,
      needPhoneAuth: false,
      // 是否需要手机号授权
      isLoggedIn: false,
      // 用户是否已登录
      currentCode: "",
      // 当前的微信登录code
      pageLoading: true,
      // 页面加载状态检查
      showUserInfoForm: false,
      // 不显示用户信息填写表单（保留样式但不使用）
      userAvatar: "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0",
      // 用户头像
      userNickname: "微信用户",
      // 用户昵称
      avatarUploading: false
      // 头像上传状态
    };
  },
  onLoad() {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.checkLoginStatusOnLoad();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 校友创新创业平台";
    },
    // 自定义分享内容
    getShareContent() {
      return "校友创新创业平台分享";
    },
    // 页面加载时检查用户登录状态
    checkLoginStatusOnLoad() {
      common_vendor.index.__f__("log", "at pages/login/index.vue:156", "🔍 页面加载时检查用户登录状态");
      const token = common_vendor.index.getStorageSync("token");
      common_vendor.index.__f__("log", "at pages/login/index.vue:160", "📱 本地token:", token);
      if (!token) {
        common_vendor.index.__f__("log", "at pages/login/index.vue:164", "❌ 没有token，显示登录界面");
        this.isLoggedIn = false;
        this.needPhoneAuth = true;
        this.pageLoading = false;
        return;
      }
      common_vendor.index.__f__("log", "at pages/login/index.vue:172", "✅ 有token，验证token有效性");
      utils_request.request.get("/miniapp/user/getCurrentUser").then((result) => {
        common_vendor.index.__f__("log", "at pages/login/index.vue:175", "🔍 验证token响应:", result);
        if (result && result.data && result.data.code === 200) {
          common_vendor.index.__f__("log", "at pages/login/index.vue:179", "✅ token有效，用户已登录");
          const userInfo = result.data.data;
          try {
            common_vendor.index.setStorageSync("userInfo", userInfo);
            const isInfoComplete = userInfo.isInfoComplete;
            common_vendor.index.setStorageSync("isInfoComplete", isInfoComplete);
            common_vendor.index.__f__("log", "at pages/login/index.vue:188", "💾 保存用户信息成功:", userInfo);
            common_vendor.index.__f__("log", "at pages/login/index.vue:189", "💾 保存个人资料完善状态:", isInfoComplete);
            utils_profileCheck.resetProfileGuideSession();
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/login/index.vue:194", "💾 保存用户信息失败:", error);
          }
          this.isLoggedIn = true;
          const redirectPath = common_vendor.index.getStorageSync("redirectPath");
          if (redirectPath) {
            common_vendor.index.__f__("log", "at pages/login/index.vue:202", "🔄 检测到保存的跳转路径:", redirectPath);
            common_vendor.index.removeStorageSync("redirectPath");
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: redirectPath
              });
            }, 500);
          } else {
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/index/home"
              });
            }, 500);
          }
        } else if (result && result.data && result.data.code === 401) {
          common_vendor.index.__f__("log", "at pages/login/index.vue:222", "❌ token无效，需要重新登录");
          this.clearLocalData();
          this.isLoggedIn = false;
          this.needPhoneAuth = true;
          this.pageLoading = false;
        } else {
          common_vendor.index.__f__("log", "at pages/login/index.vue:229", "❌ 验证token失败，显示登录界面");
          this.isLoggedIn = false;
          this.needPhoneAuth = true;
          this.pageLoading = false;
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/login/index.vue:236", "🔍 验证token失败:", error);
        if (error.statusCode === 401) {
          common_vendor.index.__f__("log", "at pages/login/index.vue:240", "❌ token已过期，清除本地数据");
          this.clearLocalData();
        }
        this.isLoggedIn = false;
        this.needPhoneAuth = true;
        this.pageLoading = false;
      });
    },
    // 清除本地数据
    clearLocalData() {
      try {
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("userInfo");
        common_vendor.index.removeStorageSync("openid");
        common_vendor.index.removeStorageSync("sessionKey");
        common_vendor.index.__f__("log", "at pages/login/index.vue:257", "🗑️ 清除本地数据成功");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/index.vue:259", "🗑️ 清除本地数据失败:", error);
      }
    },
    // 处理登录按钮点击
    handleLoginClick() {
      if (!this.isAgreed) {
        common_vendor.index.showToast({
          title: "请先同意用户协议",
          icon: "none"
        });
        return;
      }
      if (this.needPhoneAuth) {
        common_vendor.index.__f__("log", "at pages/login/index.vue:275", "📱 需要手机号授权，由open-type处理");
        return;
      }
      if (this.isLoggedIn) {
        common_vendor.index.__f__("log", "at pages/login/index.vue:281", "✅ 用户已登录，直接跳转首页");
        common_vendor.index.reLaunch({
          url: "/pages/index/home"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/login/index.vue:289", "🚀 开始登录流程");
      this.startLogin();
    },
    // 开始登录流程
    startLogin() {
      this.loginLoading = true;
      common_vendor.index.login({
        provider: "weixin",
        success: (loginRes) => {
          common_vendor.index.__f__("log", "at pages/login/index.vue:301", "🔑 获取微信登录code成功:", loginRes);
          this.currentCode = loginRes.code;
          common_vendor.index.__f__("log", "at pages/login/index.vue:305", "📱 需要手机号授权");
          this.needPhoneAuth = true;
          this.loginLoading = false;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/login/index.vue:310", "🔑 获取登录code失败:", err);
          this.loginLoading = false;
          common_vendor.index.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
        }
      });
    },
    // 选择头像回调
    onChooseAvatar(e) {
      common_vendor.index.__f__("log", "at pages/login/index.vue:323", "选择头像回调:", e.detail);
      if (e.detail.avatarUrl) {
        common_vendor.index.__f__("log", "at pages/login/index.vue:325", "✅ 头像选择成功，开始上传:", e.detail.avatarUrl);
        this.uploadAvatar(e.detail.avatarUrl);
      }
    },
    // 上传头像到服务器
    uploadAvatar(tempFilePath) {
      this.avatarUploading = true;
      common_vendor.index.showLoading({
        title: "上传头像中..."
      });
      utils_request.request.upload("/common/weixin/upload", tempFilePath, "file").then((result) => {
        if (result.success) {
          this.userAvatar = result.url;
          common_vendor.index.__f__("log", "at pages/login/index.vue:343", "头像上传成功，服务器URL:", result.url);
          common_vendor.index.showToast({
            title: "头像上传成功",
            icon: "success"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/login/index.vue:350", "头像上传失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "头像上传失败",
            icon: "none"
          });
          this.userAvatar = "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0";
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/login/index.vue:360", "头像上传失败:", error);
        common_vendor.index.showToast({
          title: "头像上传失败，请重试",
          icon: "none"
        });
        this.userAvatar = "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0";
      }).finally(() => {
        this.avatarUploading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 昵称输入失焦处理
    onNicknameBlur(e) {
      this.userNickname = e.detail.value || "微信用户";
      common_vendor.index.__f__("log", "at pages/login/index.vue:377", "昵称更新:", this.userNickname);
    },
    // 获取登录按钮文本
    getLoginButtonText() {
      return "微信一键登录";
    },
    // 获取手机号授权成功
    onGetPhoneNumber(e) {
      common_vendor.index.__f__("log", "at pages/login/index.vue:387", "📱 手机号授权回调:", e.detail);
      if (e.detail.code) {
        common_vendor.index.__f__("log", "at pages/login/index.vue:390", "✅ 获取手机号授权成功，开始登录流程，code:", e.detail.code);
        this.loginLoading = true;
        this.wechatLogin({
          phoneCode: e.detail.code,
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv
        });
      } else {
        common_vendor.index.__f__("log", "at pages/login/index.vue:400", "❌ 获取手机号失败或用户拒绝授权:", e.detail);
        common_vendor.index.showToast({
          title: "需要手机号授权才能登录",
          icon: "none"
        });
        this.needPhoneAuth = false;
        this.loginLoading = false;
      }
    },
    // 调用后端登录接口
    callLoginApi(loginData) {
      return utils_request.request.post("/miniapp/user/weixinLogin", loginData);
    },
    // 微信登录（包含注册和登录逻辑）
    wechatLogin(phoneData) {
      common_vendor.index.__f__("log", "at pages/login/index.vue:418", "🔑 开始微信登录流程，手机号数据:", phoneData);
      common_vendor.index.login({
        provider: "weixin",
        success: (loginRes) => {
          common_vendor.index.__f__("log", "at pages/login/index.vue:424", "🔑 重新获取微信登录code成功:", loginRes);
          const loginData = {
            code: loginRes.code,
            // 使用最新获取的code
            phoneCode: phoneData.phoneCode,
            // 手机号授权码
            encryptedData: phoneData.encryptedData,
            // 加密数据
            iv: phoneData.iv,
            // 初始向量
            // 使用默认的头像和昵称
            avatar: "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0",
            nickName: "微信用户",
            weixinAvatar: "https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0",
            weixinNickname: "微信用户",
            sex: "2",
            // 未知
            realName: "",
            openid: "",
            // 后端通过code获取
            sessionKey: "",
            // 后端通过code获取
            unionid: ""
            // 后端通过code获取
          };
          common_vendor.index.__f__("log", "at pages/login/index.vue:443", "📤 准备调用登录API，数据:", loginData);
          common_vendor.index.__f__("log", "at pages/login/index.vue:444", "📤 使用的code:", loginRes.code);
          this.callLoginApi(loginData).then((result) => {
            var _a, _b;
            common_vendor.index.__f__("log", "at pages/login/index.vue:447", "📥 登录API响应:", result);
            if (result && result.data && result.data.data && result.data.data.token) {
              common_vendor.index.__f__("log", "at pages/login/index.vue:450", "✅ 登录成功，获得token");
              const token = result.data.data.token;
              const userData = result.data.data;
              try {
                common_vendor.index.setStorageSync("userInfo", userData);
                common_vendor.index.setStorageSync("token", token);
                common_vendor.index.setStorageSync("phone", userData.phonenumber);
                const isInfoComplete = userData.isInfoComplete;
                common_vendor.index.setStorageSync("isInfoComplete", isInfoComplete);
                common_vendor.index.__f__("log", "at pages/login/index.vue:465", "💾 保存用户数据成功");
                common_vendor.index.__f__("log", "at pages/login/index.vue:466", "💾 保存个人资料完善状态:", isInfoComplete);
                utils_profileCheck.resetProfileGuideSession();
                common_vendor.index.showToast({
                  title: "登录成功",
                  icon: "success"
                });
                const redirectPath = common_vendor.index.getStorageSync("redirectPath");
                if (redirectPath) {
                  common_vendor.index.__f__("log", "at pages/login/index.vue:479", "🔄 检测到保存的跳转路径:", redirectPath);
                  common_vendor.index.removeStorageSync("redirectPath");
                  setTimeout(() => {
                    common_vendor.index.reLaunch({
                      url: redirectPath
                    });
                  }, 1500);
                } else {
                  setTimeout(() => {
                    common_vendor.index.reLaunch({
                      url: "/pages/index/home"
                    });
                  }, 1500);
                }
              } catch (saveError) {
                common_vendor.index.__f__("error", "at pages/login/index.vue:498", "❌ 保存数据失败:", saveError);
                common_vendor.index.showToast({
                  title: "保存用户信息失败",
                  icon: "none"
                });
              }
            } else {
              common_vendor.index.__f__("log", "at pages/login/index.vue:506", "❌ 登录失败，未找到token");
              const errorMsg = ((_a = result == null ? void 0 : result.data) == null ? void 0 : _a.msg) || ((_b = result == null ? void 0 : result.data) == null ? void 0 : _b.message) || "登录失败，请重试";
              common_vendor.index.showToast({
                title: errorMsg,
                icon: "none"
              });
            }
            this.loginLoading = false;
          }).catch((error) => {
            common_vendor.index.__f__("error", "at pages/login/index.vue:517", "🔑 登录失败:", error);
            common_vendor.index.showToast({
              title: "网络错误，请重试",
              icon: "none"
            });
            this.loginLoading = false;
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/login/index.vue:526", "🔑 重新获取登录code失败:", err);
          common_vendor.index.showToast({
            title: "获取登录code失败，请重试",
            icon: "none"
          });
          this.loginLoading = false;
        }
      });
    },
    // 协议状态改变
    onAgreementChange(e) {
      this.isAgreed = e.detail.value.includes("agree");
    },
    // 切换协议状态
    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },
    // 处理协议区域点击
    handleAgreementTap() {
      this.isAgreed = !this.isAgreed;
    },
    // 显示用户协议
    showUserAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/agreement/user"
      });
    },
    // 显示隐私政策
    showPrivacyPolicy() {
      common_vendor.index.navigateTo({
        url: "/pages/agreement/privacy"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: $options.getImagePath("login_bg.png"),
    c: $data.pageLoading
  }, $data.pageLoading ? {
    d: $options.getImagePath("bottom_order_icon.png")
  } : common_vendor.e({
    e: $options.getImagePath("bottom_order_icon.png"),
    f: $options.getImagePath("login_icon1.png"),
    g: $options.getImagePath("login_icon2.png"),
    h: $options.getImagePath("login_icon3.png"),
    i: !$data.isLoggedIn && $data.showUserInfoForm
  }, !$data.isLoggedIn && $data.showUserInfoForm ? common_vendor.e({
    j: $data.userAvatar,
    k: !$data.avatarUploading
  }, !$data.avatarUploading ? {} : {}, {
    l: $data.avatarUploading
  }, $data.avatarUploading ? {} : {}, {
    m: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    n: $data.avatarUploading,
    o: common_vendor.o((...args) => $options.onNicknameBlur && $options.onNicknameBlur(...args)),
    p: $data.userNickname,
    q: common_vendor.o(($event) => $data.userNickname = $event.detail.value)
  }) : {}, {
    r: common_vendor.t($data.loginLoading ? "登录中..." : $options.getLoginButtonText()),
    s: $data.needPhoneAuth && $data.isAgreed ? "getPhoneNumber" : "",
    t: common_vendor.o((...args) => $options.onGetPhoneNumber && $options.onGetPhoneNumber(...args)),
    v: common_vendor.o((...args) => $options.handleLoginClick && $options.handleLoginClick(...args)),
    w: $data.loginLoading,
    x: $data.isAgreed
  }, $data.isAgreed ? {} : {}, {
    y: $data.isAgreed ? 1 : "",
    z: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    A: common_vendor.o((...args) => $options.showUserAgreement && $options.showUserAgreement(...args)),
    B: common_vendor.o((...args) => $options.showPrivacyPolicy && $options.showPrivacyPolicy(...args)),
    C: common_vendor.o((...args) => $options.handleAgreementTap && $options.handleAgreementTap(...args)),
    D: common_vendor.o((...args) => $options.onAgreementChange && $options.onAgreementChange(...args))
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/index.js.map
