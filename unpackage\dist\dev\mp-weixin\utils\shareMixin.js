"use strict";
const common_vendor = require("../common/vendor.js");
const utils_shareReward = require("./shareReward.js");
const utils_imageUtils = require("./imageUtils.js");
const shareMixin = {
  data() {
    return {
      // 分享检测相关
      shareDetectionHandlers: null,
      // 分享状态跟踪
      _shareTriggered: false,
      _shareContent: "",
      _pageHideTime: 0
    };
  },
  onLoad() {
  },
  onShow() {
    if (this._disableShareDetection) {
      common_vendor.index.__f__("log", "at utils/shareMixin.js:29", "📤 分享检测已禁用，跳过处理");
      return;
    }
    if (this._shareTriggered && this._pageHideTime) {
      const now = Date.now();
      const hideDuration = now - this._pageHideTime;
      common_vendor.index.__f__("log", "at utils/shareMixin.js:38", "📤 检测到分享操作后页面显示，隐藏时长:", hideDuration);
      if (hideDuration >= 1e3 && hideDuration <= 3e4) {
        common_vendor.index.__f__("log", "at utils/shareMixin.js:42", "📤 检测到可能的分享操作，处理积分奖励");
        this.triggerShareReward(this._shareContent || "页面分享");
      }
      this._shareTriggered = false;
      this._shareContent = "";
      this._pageHideTime = 0;
    }
  },
  onHide() {
    if (this._disableShareDetection) {
      common_vendor.index.__f__("log", "at utils/shareMixin.js:61", "📤 分享检测已禁用，跳过隐藏处理");
      return;
    }
    this._pageHideTime = Date.now();
    common_vendor.index.__f__("log", "at utils/shareMixin.js:67", "📤 页面隐藏，记录时间:", this._pageHideTime);
  },
  /**
   * 分享给朋友
   * 每个页面可以重写这个方法来自定义分享内容
   */
  onShareAppMessage() {
    const title = this.getShareTitle ? this.getShareTitle() : this.getDefaultShareTitle();
    const path = this.getSharePath ? this.getSharePath() : this.getDefaultSharePath();
    const imageUrl = this.getShareImageUrl ? this.getShareImageUrl() : this.getDefaultShareImageUrl();
    common_vendor.index.__f__("log", "at utils/shareMixin.js:89", "📤 分享给朋友配置:", { title, path, imageUrl });
    this._shareTriggered = true;
    this._shareContent = this.getShareContent ? this.getShareContent() : title;
    return {
      title,
      path,
      imageUrl
    };
  },
  /**
   * 分享到朋友圈
   * 每个页面可以重写这个方法来自定义分享内容
   */
  onShareTimeline() {
    const title = this.getShareTitle ? this.getShareTitle() : this.getDefaultShareTitle();
    const imageUrl = this.getShareImageUrl ? this.getShareImageUrl() : this.getDefaultShareImageUrl();
    common_vendor.index.__f__("log", "at utils/shareMixin.js:113", "📤 分享到朋友圈配置:", { title, imageUrl });
    this._shareTriggered = true;
    this._shareContent = this.getShareContent ? this.getShareContent() : title;
    return {
      title,
      imageUrl
    };
  },
  methods: {
    /**
     * 获取默认分享标题
     * 子页面可以重写 getShareTitle 方法来自定义
     */
    getDefaultShareTitle() {
      if (this.pageTitle) {
        return `${this.pageTitle} - 天大海棠`;
      }
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        const routeTitleMap = {
          "pages/index/home": "天大海棠 - 创新创业服务平台",
          "pages/index/demand-square": "需求广场 - 天大海棠",
          "pages/index/contacts-new": "人脉资源 - 天大海棠",
          "pages/index/mine": "个人中心 - 天大海棠",
          "pages/competition/haitang": "海棠杯大赛 - 天大海棠",
          "pages/competition/guidance": "活动报名 - 天大海棠",
          "pages/garden/index": "园区入驻 - 天大海棠",
          "pages/points/mall": "积分商城 - 天大海棠",
          "pages/index/star-detail": "科技之星 - 天大海棠",
          "pages/index/job-detail": "职位详情 - 天大海棠",
          "pages/index/activity-detail": "活动详情 - 天大海棠"
        };
        return routeTitleMap[route] || "天大海棠 - 创新创业服务平台";
      }
      return "天大海棠 - 创新创业服务平台";
    },
    /**
     * 获取默认分享路径
     * 子页面可以重写 getSharePath 方法来自定义
     */
    getDefaultSharePath() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = currentPage.route;
        const options = currentPage.options;
        let path = "/" + route;
        if (options && Object.keys(options).length > 0) {
          const queryString = Object.keys(options).map((key) => `${key}=${encodeURIComponent(options[key])}`).join("&");
          path += "?" + queryString;
        }
        return path;
      }
      return "/pages/index/home";
    },
    /**
     * 获取默认分享图片
     * 子页面可以重写 getShareImageUrl 方法来自定义
     */
    getDefaultShareImageUrl() {
      if (this.shareImage) {
        return this.shareImage;
      }
      return utils_imageUtils.getImagePath("share-logo.png");
    },
    /**
     * 手动触发分享积分奖励
     * 用于处理一些特殊场景，比如用户点击分享按钮但没有实际分享
     */
    triggerShareReward(content = "") {
      common_vendor.index.__f__("log", "at utils/shareMixin.js:209", "📤 手动触发分享积分奖励:", content);
      return utils_shareReward.shareRewardManager.processShareReward(content);
    },
    /**
     * 显示分享提示
     * 用于引导用户分享
     */
    showShareTip() {
      common_vendor.index.showModal({
        title: "分享提示",
        content: '请点击右上角三个点按钮，选择"转发"分享给好友或朋友圈，完成分享可获得积分奖励！',
        showCancel: false,
        confirmText: "我知道了",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__("log", "at utils/shareMixin.js:225", "📤 用户确认分享提示");
          }
        }
      });
    },
    /**
     * 检查分享功能是否可用
     */
    checkShareAvailable() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        return systemInfo.platform !== "devtools";
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/shareMixin.js:241", "📤 检查分享功能可用性失败:", error);
        return false;
      }
    }
  }
};
exports.shareMixin = shareMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/shareMixin.js.map
