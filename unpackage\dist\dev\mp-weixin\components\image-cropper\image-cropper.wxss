/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.image-cropper-container.data-v-d0be24ff {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: #000;
}
.cropper-overlay.data-v-d0be24ff {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.cropper-header.data-v-d0be24ff {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: 60rpx;
  background-color: rgba(0, 0, 0, 0.8);
}
.cropper-header .cancel-btn.data-v-d0be24ff, .cropper-header .confirm-btn.data-v-d0be24ff {
  color: #fff;
  font-size: 32rpx;
  padding: 10rpx 20rpx;
}
.cropper-header .title.data-v-d0be24ff {
  color: #fff;
  font-size: 36rpx;
  font-weight: 600;
}
.cropper-content.data-v-d0be24ff {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}
.image-container.data-v-d0be24ff {
  position: relative;
  overflow: hidden;
}
.source-image.data-v-d0be24ff {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.mask-overlay.data-v-d0be24ff {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2;
  pointer-events: none;
}
.crop-box.data-v-d0be24ff {
  position: absolute;
  cursor: move;
  z-index: 10;
}
.crop-box .crop-border.data-v-d0be24ff {
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  pointer-events: none;
}
.crop-box .crop-inner.data-v-d0be24ff {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.crop-box .crop-inner .crop-hint.data-v-d0be24ff {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  text-align: center;
  pointer-events: none;
}
.crop-box .control-point.data-v-d0be24ff {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background-color: #fff;
  border: 4rpx solid #007aff;
  border-radius: 50%;
  z-index: 20;
  cursor: nw-resize;
}
.crop-box .control-point.data-v-d0be24ff::before {
  content: "";
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
}
.crop-box .control-point.top-left.data-v-d0be24ff {
  top: -12rpx;
  left: -12rpx;
  cursor: nw-resize;
}
.crop-box .control-point.top-right.data-v-d0be24ff {
  top: -12rpx;
  right: -12rpx;
  cursor: ne-resize;
}
.crop-box .control-point.bottom-left.data-v-d0be24ff {
  bottom: -12rpx;
  left: -12rpx;
  cursor: sw-resize;
}
.crop-box .control-point.bottom-right.data-v-d0be24ff {
  bottom: -12rpx;
  right: -12rpx;
  cursor: se-resize;
}
.cropper-footer.data-v-d0be24ff {
  padding: 30rpx;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
}
.cropper-footer .reset-btn.data-v-d0be24ff {
  padding: 20rpx 40rpx;
  border: 2rpx solid #fff;
  border-radius: 10rpx;
}
.cropper-footer .reset-btn text.data-v-d0be24ff {
  color: #fff;
  font-size: 28rpx;
}
.hidden-canvas.data-v-d0be24ff {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  opacity: 0;
  pointer-events: none;
}