.home-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  /* 底部导航栏的高度 */
  color: #333;
  font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
  position: relative;
}
/* 顶部区域 */
.header {
  position: relative;
  height: 300rpx;
  padding-bottom: 40rpx;
  /* 减小底部内边距，轮播图只需稍微偏移 */
  z-index: 1;
}
/* 发送弹幕按钮 */
.send-danmu-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  transition: all 0.3s ease;
}
.send-danmu-btn:active {
  transform: scale(0.95);
}
.top-title {
  padding: 40rpx 30rpx 20rpx;
}
.main-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #003399;
  /* 深蓝色文字 */
  letter-spacing: 2rpx;
}
/* 轮播图区域 - 稍微向上偏移 */
.banner-wrapper {
  position: relative;
  z-index: 10;
  margin: 0 30rpx;
  margin-top: -100rpx;
  height: 360rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  border-radius: 20rpx;
  overflow: hidden;
}
.banner-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.banner-swiper {
  width: 100%;
  height: 360rpx;
  border-radius: 20rpx;
}
.main-banner {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}
/* 轮播图占位样式 */
.banner-placeholder {
  width: 100%;
  height: 360rpx;
  border-radius: 20rpx;
  background-color: #f0f5ff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.placeholder-content {
  text-align: center;
}
.placeholder-text {
  font-size: 28rpx;
  color: #999;
}
/* 音频提示区域样式 */
.audio-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  /* 半透明黑色背景 */
  padding: 6rpx 15rpx;
  z-index: 5;
  height: 50rpx;
}
.audio-icon {
  margin-right: 20rpx;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.audio-icon image {
  width: 13px;
  height: 13px;
}
.notice-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}
.notice-wrapper {
  width: 100%;
  height: 100%;
}
.notice-swiper {
  width: 100%;
  height: 100%;
}
.notice-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.notice-text {
  font-size: 24rpx;
  color: #000;
  /* 黑色文字 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.notice-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
/* 功能导航区 - 调整外边距 */
.nav-section {
  margin: 0;
}
.nav-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0;
  margin: 0 30rpx;
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 25%;
  /* 每行4个 */
  height: 180rpx;
  /* 固定高度 */
  padding: 15rpx 0;
  position: relative;
  box-sizing: border-box;
}
.nav-item .nav-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  box-shadow: 0 7rpx 5rpx rgba(0, 0, 0, 0.1);
}
.nav-item .nav-icon .nav-image {
  width: 46rpx;
  height: 46rpx;
}
.nav-item .nav-icon .nav-module-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.module-0,
.module-1,
.module-2,
.module-3,
.module-4,
.module-5 {
  background: transparent !important;
  box-shadow: none !important;
}
.nav-loading {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180rpx;
}
.nav-loading .loading-text {
  font-size: 24rpx;
  color: #999;
}
.nav-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}
.innovation {
  background: linear-gradient(135deg, #7098bf, #0e4bb0);
}
.events {
  background: linear-gradient(135deg, #f34b79, #b1474e);
}
.garden {
  background: linear-gradient(135deg, #5fcbcf, #3883a7);
}
.investment {
  background: linear-gradient(135deg, #e6b77a, #e19d77);
}
.join {
  background: linear-gradient(45deg, #23adc2, #56e6d8);
}
.guide {
  background: linear-gradient(45deg, #4e4abb, #bda0fc);
}
/* 边框样式 - 基于位置添加不同边框 */
/* 第1个模块 - 右和下边框 */
.nav-item:nth-child(1) {
  border-right: 2px solid #f1f2f7;
  border-bottom: 2px solid #f1f2f7;
}
/* 第2个模块 - 右和下边框 */
.nav-item:nth-child(2) {
  border-right: 2px solid #f1f2f7;
  border-bottom: 2px solid #f1f2f7;
}
/* 第3个模块 - 右和下边框 */
.nav-item:nth-child(3) {
  border-right: 2px solid #f1f2f7;
  border-bottom: 2px solid #f1f2f7;
}
/* 第4个模块 - 只有下边框 */
.nav-item:nth-child(4) {
  border-bottom: 2px solid #f1f2f7;
}
/* 第5个模块 - 只有右边框 */
.nav-item:nth-child(5) {
  border-right: 2px solid #f1f2f7;
}
/* 第6个模块 - 只有右边框  */
.nav-item:nth-child(6) {
  border-right: 2px solid #f1f2f7;
}
/* 区块通用样式 */
.section-block {
  margin: 50rpx 30rpx;
  border-radius: 20rpx;
  background-color: #fff;
  position: relative;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  /* 给容器添加阴影 */
}
.section-block .list_bg {
  width: 100%;
  position: absolute;
  top: -4rpx;
  left: 0;
  z-index: 16;
  border-radius: 20rpx;
  /* 添加圆角 */
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  position: relative;
}
.section-title {
  font-size: 32rpx;
  font-weight: 900;
  color: #003399;
  margin-left: 20rpx;
  z-index: 100;
}
.more-link {
  font-size: 28rpx;
  color: #003399;
  margin-right: 10rpx;
  z-index: 100;
}
.star-content {
  padding: 30rpx;
  position: relative;
  z-index: 20;
}
.star-swiper {
  width: 100%;
  height: 260rpx;
  margin-bottom: 30rpx;
}
.star-card {
  display: flex;
  padding: 20rpx;
  border-radius: 10rpx;
  height: 100%;
}
.star-avatar {
  width: 210rpx;
  height: 220rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  object-fit: cover;
}
.star-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.star-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #000000;
}
.star-position {
  font-size: 26rpx;
  color: #000000;
  margin-bottom: 10rpx;
}
.star-desc {
  font-size: 24rpx;
  color: #000000;
  line-height: 1.5;
}
/* 自定义滚动条样式 */
.star-indicators {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 40rpx;
}
.indicator-track {
  position: relative;
  width: calc(100% - 40rpx);
  height: 6rpx;
  background-color: #e8e8e8;
  border-radius: 3rpx;
}
.indicator-thumb {
  position: absolute;
  width: 90rpx;
  height: 24rpx;
  background: linear-gradient(90deg, #003399, #4A90E2);
  border-radius: 100rpx;
  transition: all 0.3s ease;
  top: -8rpx;
  left: 0;
}
.activity-list {
  padding: 30rpx;
  position: relative;
  z-index: 20;
}
.activity-item {
  display: flex;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.activity-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.activity-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  object-fit: cover;
}
.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.activity-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.activity-desc {
  font-size: 24rpx;
  color: #222222;
  line-height: 1.4;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.activity-time {
  font-size: 24rpx;
  color: #999;
}
.job-list {
  padding: 30rpx;
  position: relative;
  z-index: 20;
}
.job-item {
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.job-item:last-child {
  border-bottom: none;
}
.job-main {
  display: flex;
  flex-direction: column;
}
.job-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.job-title {
  font-size: 30rpx;
  font-weight: bold;
}
.job-salary {
  font-size: 28rpx;
  color: #003399;
  font-weight: bold;
}
.job-company {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.job-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}
.job-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #d7e6ff;
  padding: 6rpx 16rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
  border-radius: 6rpx;
}
.job-bottom {
  display: flex;
  justify-content: space-between;
}
.job-date,
.job-location {
  font-size: 24rpx;
  color: #999;
}
/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -6rpx 16rpx #c8d5f2;
  z-index: 100;
  padding-bottom: 20rpx;
}
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  position: relative;
}
.tab-icon {
  width: 44rpx;
  height: 44rpx;
}
.tab-text {
  font-size: 22rpx;
  color: #444;
}
.tab-item.active .tab-text {
  color: #003399;
}
.tab-center-icon {
  width: 88rpx;
  height: 88rpx;
  background-color: #003399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: relative;
  top: -48rpx;
}
.tab-center-image {
  width: 60rpx;
  height: 60rpx;
}
.tab-center-text {
  font-size: 22rpx;
  color: #444;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.footer {
  padding: 30rpx 0;
  text-align: center;
}
/* 弹幕动画 */
@keyframes danmuMove {
0% {
    transform: translateX(100vw);
}
100% {
    transform: translateX(-100%);
}
}
/* 用户头像和详情样式 */
.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #fff;
  overflow: hidden;
  margin-right: 12rpx;
  margin-left: -12rpx;
  /* 向左移动头像再多一点 */
}
.avatar-img {
  width: 100%;
  height: 100%;
}
.danmu-content {
  display: flex;
  flex-direction: column;
  /* 内容垂直排列 */
  justify-content: center;
  height: 100%;
  flex: 1;
  /* 占据剩余空间 */
  overflow: visible;
  /* 允许内容显示完整 */
  min-width: 0;
  /* 允许收缩 */
  max-width: 300rpx;
  /* 相应缩小内容区域宽度 */
  padding: 2rpx 0;
  /* 增加一点内边距 */
}
.danmu-user {
  font-size: 22rpx;
  /* 适当增加字体大小 */
  color: #fff;
  margin-top: 4rpx;
  margin-bottom: 8rpx;
  /* 增加行间距 */
  text-align: left;
  /* 左对齐文本 */
  line-height: 1.2;
  /* 设置行高 */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: 100%;
}
.danmu-tag {
  font-size: 24rpx;
  /* 适当增加字体大小 */
  color: #fff;
  text-align: left;
  /* 左对齐文本 */
  line-height: 1.2;
  /* 设置行高 */
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: 100%;
}
/* 内容区域 - 纯色背景 */
.content-area {
  background-color: #f3f8fe;
  /* 浅灰色背景，而非渐变 */
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  position: relative;
  z-index: 0;
}
/* 弹幕容器样式 */
.danmu-wrapper {
  position: relative;
  height: 290rpx;
  /* 大幅增加高度，扩展到轮播图位置 */
  overflow: hidden;
  margin-bottom: 20rpx;
  z-index: 25;
  /* 确保弹幕在发送按钮之上 */
  pointer-events: none;
  /* 让弹幕容器不阻挡按钮点击 */
}
/* 用户信息弹幕样式 */
.user-danmu {
  position: absolute;
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  /* 增加内边距 */
  border-radius: 50rpx;
  /* 更圆润的边角 */
  animation: danmuMove linear 1;
  /* 移除infinite，每条弹幕只播放一次 */
  animation-fill-mode: forwards;
  /* 动画结束后保持最终状态 */
  transform: translateX(100vw);
  background-color: #003399;
  height: 74rpx;
  /* 增加高度以容纳两行文字 */
  white-space: nowrap;
  /* 防止内容换行 */
  z-index: 26;
  /* 确保单个弹幕在发送按钮之上 */
  min-width: 200rpx;
  /* 最小宽度 */
  max-width: 400rpx;
  /* 进一步缩小测试省略号 */
  pointer-events: auto;
  /* 重新启用弹幕的点击事件 */
  width: auto;
  /* 宽度自适应内容 */
  box-sizing: border-box;
}
/* 空白占位符样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  min-height: 240rpx;
  position: relative;
}
.empty-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.4;
  filter: grayscale(0.3);
}
.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.empty-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  max-width: 400rpx;
}
/* 不同模块的占位符特殊样式 */
.star-empty {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-radius: 20rpx;
  margin: 20rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.activity-empty {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: 16rpx;
  margin: 20rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.08);
}
.job-empty {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: 16rpx;
  margin: 20rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.08);
}
/* 占位符动画效果 */
.empty-placeholder .empty-icon {
  animation: emptyIconFloat 3s ease-in-out infinite;
}
@keyframes emptyIconFloat {
0%,
  100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-10rpx);
}
}
/* 弹幕输入弹窗样式 */
.danmu-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.danmu-modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
.danmu-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border-bottom: 1px solid #eee;
}
.danmu-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.danmu-modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}
.danmu-input-section {
  padding: 25rpx;
  display: flex;
  flex-direction: column;
}
.danmu-textarea {
  width: 100%;
  height: 150rpx;
  background-color: #f8f8f8;
  border: 1px solid #eee;
  border-radius: 10rpx;
  padding: 15rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  box-sizing: border-box;
  margin-bottom: 15rpx;
  min-height: 80rpx;
  /* 最小高度 */
  max-height: 200rpx;
  /* 最大高度 */
  word-break: break-all;
  /* 允许单词换行 */
}
.danmu-char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  padding-right: 10rpx;
}
.danmu-modal-footer {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1px solid #eee;
}
.danmu-cancel-btn,
.danmu-send-btn {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 40rpx;
  border: 1rpx solid #f1f1f1;
  color: #fff;
  font-weight: bold;
}
button::after {
  border: none;
}
.danmu-cancel-btn {
  background: #fff;
  color: #333333;
}
.danmu-send-btn {
  background: linear-gradient(45deg, #003399, #4A90E2);
}
.danmu-send-btn[disabled] {
  background: #ccc;
  color: #666;
}
/* 发送中状态样式 */
.danmu-sending {
  background: #4A90E2 !important;
  opacity: 0.8;
  position: relative;
}
.danmu-sending::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 30%;
  width: 20rpx;
  height: 20rpx;
  margin-top: -10rpx;
  border-radius: 50%;
  background-color: #fff;
  animation: sending-dot 1.5s infinite ease-in-out;
}
@keyframes sending-dot {
0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 1;
}
40% {
    transform: scale(1);
    opacity: 0.8;
}
}
