<view class="company-detail-container"><view wx:if="{{a}}" class="loading-container"><text class="loading-text">加载中...</text></view><view wx:else class="company-detail-content"><view class="top-image-section"><image src="{{b}}" mode="widthFix" class="top-image" binderror="{{c}}"></image><view class="company-mask"><view class="company-info"><view class="company-basic-info"><text class="company-name">{{d}}</text></view></view></view></view><view class="content-section"><view class="tags-container"><view wx:if="{{e}}" class="tag">{{f}}</view><block wx:if="{{g}}"><view wx:for="{{h}}" wx:for-item="tag" wx:key="b" class="tag">{{tag.a}}</view></block><view wx:if="{{i}}" class="tag">{{j}}</view><view wx:if="{{k}}" class="tag">{{l}}</view><view wx:if="{{m}}" class="tag">{{n}}</view><block wx:if="{{o}}"><view wx:for="{{p}}" wx:for-item="tag" wx:key="b" class="tag">{{tag.a}}</view></block></view><view wx:if="{{q}}" class="detail-section"><view class="section-title">企业简介</view><view class="description-content"><text class="description-text">{{r}}</text></view></view><view wx:if="{{s}}" class="detail-section"><view class="section-title">主营产品</view><view wx:if="{{t}}" class="tags-container"><view wx:for="{{v}}" wx:for-item="tag" wx:key="b" class="tag">{{tag.a}}</view></view><view wx:if="{{w}}" class="description-content"><text class="description-text">{{x}}</text></view></view><view wx:if="{{y}}" class="detail-section"><view class="section-title">联系方式</view><view wx:if="{{z}}" class="info-item"><text class="info-label">联系电话：</text><text class="info-value contact-link" bindtap="{{B}}">{{A}}</text></view><view wx:if="{{C}}" class="info-item"><text class="info-label">邮箱地址：</text><text class="info-value">{{D}}</text></view><view wx:if="{{E}}" class="info-item"><text class="info-label">官方网站：</text><text class="info-value contact-link" bindtap="{{G}}">{{F}}</text></view></view></view></view></view><view class="floating-buttons"><view class="floating-btn question-btn" bindtap="{{H}}"><text class="floating-btn-text"><text style="display:block">我有</text><text style="display:block">疑问</text></text></view><view class="floating-btn demand-btn" bindtap="{{I}}"><text class="floating-btn-text"><text style="display:block">我有</text><text style="display:block">需求</text></text></view></view><view wx:if="{{J}}" class="contact-popup-mask" bindtap="{{Q}}"><view class="contact-popup-content" catchtap="{{P}}"><view class="close-btn" bindtap="{{K}}"><text class="close-icon">×</text></view><view class="popup-main-title"><text class="title-line">1v1 为 您 答 疑</text><text class="title-line">做 好 链 接 与 服 务</text></view><view class="contact-phone" bindtap="{{M}}"><text class="phone-number">联系方式:{{L}}</text></view><view wx:if="{{N}}" class="qr-code-container"><image src="{{O}}" mode="aspectFit" class="qr-code-image"></image></view><view class="bottom-tip"><text class="tip-text">扫码添加 直接沟通</text></view></view></view>