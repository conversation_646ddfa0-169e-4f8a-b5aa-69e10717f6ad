.container {
  width: 100%;
  min-height: 100vh;
  background: #f2f7fd;
}
.photo-section {
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.photo-section .profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.card-section {
  position: relative;
  margin-top: -80rpx;
  padding: 0 30rpx 30rpx;
  z-index: 2;
}
.business-card {
  background: linear-gradient(180deg, #edf0fa 0%, #f7faff 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  position: relative;
}
.points-badge {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  background: #dcb955;
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  display: flex;
  align-items: center;
}
.points-badge .points-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.points-badge .points-text {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}
.basic-info {
  margin-bottom: 20rpx;
  margin-right: 120rpx;
}
.basic-info .name-location {
  display: flex;
  align-items: baseline;
  margin-bottom: 6rpx;
}
.basic-info .name-location .name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}
.basic-info .name-location .location {
  font-size: 22rpx;
  color: #333;
  font-weight: 900;
}
.basic-info .education {
  font-size: 22rpx;
  color: #333;
  font-weight: 900;
  margin-bottom: 6rpx;
}
.work-info {
  margin-bottom: 20rpx;
}
.work-info .work-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}
.work-info .work-row .company {
  font-size: 24rpx;
  color: #333;
}
.work-info .work-row .position {
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  text-align: right;
}
.industry-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 30rpx;
}
.industry-tags .tag-item {
  background: #aacaf9;
  border-radius: 30rpx;
  padding: 0rpx 15rpx 5rpx;
}
.industry-tags .tag-item .tag-text {
  font-size: 26rpx;
  color: #ffffff;
}
.divider-line {
  width: 100%;
  height: 1rpx;
  background: #dde0e9;
  margin: 20rpx 0;
}
.bio-section {
  margin-bottom: 20rpx;
  display: flex;
}
.bio-section .bio-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
.collect-section .collect-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(180deg, #023ca8 0%, #295bb8 100%);
  color: #fff;
  border: none;
  border-radius: 1000rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  transition: all 0.3s ease;
}
.collect-section .collect-btn:active {
  transform: scale(0.98);
}
.collect-section .collect-btn:disabled {
  opacity: 0.6;
  transform: none;
}
.collect-section .collect-btn.followed {
  background: linear-gradient(180deg, #28a745 0%, #34ce57 100%);
}
.collect-section .collect-btn.followed:active {
  transform: scale(0.98);
}
