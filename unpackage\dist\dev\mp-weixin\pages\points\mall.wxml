<view class="points-mall-container"><view class="daily-checkin-section"><view class="checkin-card"><view class="checkin-header"><text class="checkin-title">每日签到</text><view class="{{['checkin-btn', b && 'checked', c && 'loading']}}" bindtap="{{d}}"><text class="checkin-btn-text">{{a}}</text></view></view><view class="checkin-subtitle-row"><view class="subtitle-left"><text class="checkin-subtitle">明日未签提醒</text><switch class="reminder-switch" checked="{{e}}" bindchange="{{f}}"/></view><view class="subtitle-right"><text class="remaining-text"> 剩余补签{{g}}次 </text><image src="{{h}}" class="question-icon" mode="aspectFit" bindtap="{{i}}"></image></view></view><view wx:if="{{j}}" class="date-switcher"><view class="date-switcher-container"><view class="date-arrow left-arrow" bindtap="{{k}}"><text class="arrow-text">◀</text></view><view class="date-display"><text class="date-text">{{l}}</text></view><view class="date-arrow right-arrow" bindtap="{{m}}"><text class="arrow-text">▶</text></view></view></view><view class="checkin-calendar"><view wx:if="{{n}}"><view class="calendar-labels"><text wx:for="{{o}}" wx:for-item="day" wx:key="b" class="week-label">{{day.a}}</text></view><view class="calendar-days"><view wx:for="{{p}}" wx:for-item="day" wx:key="e" class="{{['calendar-day', day.f && 'checked', day.g && 'today', day.h && 'supplement', day.i && 'waiting', day.j && 'can-makeup']}}" bindtap="{{day.k}}"><text class="day-number">{{day.a}}</text><text class="day-status">{{day.b}}</text><view wx:if="{{day.c}}" class="reward-icon">⭐</view><view wx:if="{{day.d}}" class="makeup-icon">📝</view></view></view></view><view wx:else class="expanded-calendar"><view class="calendar-labels"><text class="week-label">周日</text><text class="week-label">周一</text><text class="week-label">周二</text><text class="week-label">周三</text><text class="week-label">周四</text><text class="week-label">周五</text><text class="week-label">周六</text></view><view class="full-month-calendar"><view wx:for="{{q}}" wx:for-item="week" wx:key="b" class="calendar-week"><view wx:for="{{week.a}}" wx:for-item="day" wx:key="g" class="{{['calendar-day', day.h && 'checked', day.i && 'today', day.j && 'supplement', day.k && 'waiting', day.l && 'empty', day.m && 'can-makeup']}}" bindtap="{{day.n}}"><text wx:if="{{day.a}}" class="day-number">{{day.b}}</text><text wx:if="{{day.c}}" class="day-status">{{day.d}}</text><view wx:if="{{day.e}}" class="reward-icon">⭐</view><view wx:if="{{day.f}}" class="makeup-icon">📝</view></view></view></view></view></view><view class="checkin-footer"><view class="checkin-tip"><text> 本月连续打卡{{r}}天可额外获得{{s}}积分 </text></view><view class="monthly-reward" bindtap="{{w}}"><text class="reward-text">{{t}}</text><image src="{{v}}" class="arrow-icon" mode="aspectFit"></image></view></view></view></view><view wx:if="{{x}}" class="activities-section"><view wx:for="{{y}}" wx:for-item="activity" wx:key="i" class="activity-card challenge-card"><view class="activity-content"><view class="activity-title"><text>{{activity.a}}</text></view><text class="activity-time">活动时间：{{activity.b}}-{{activity.c}}</text><text class="activity-reward">连续签到<text class="highlight-red">{{activity.d}}天</text> (不含补签)</text><text class="activity-reward">获得<text class="highlight-red">{{activity.e}}积分</text>奖励</text></view><view class="{{['activity-btn', 'red-btn', activity.g && 'disabled']}}" bindtap="{{activity.h}}"><text class="btn-text">{{activity.f}}</text></view></view><view wx:if="{{z}}" class="activity-card challenge-card"><view class="activity-content"><view class="activity-title"><text>暂无活动</text></view><text class="activity-time">敬请期待更多精彩活动</text></view></view><view class="activity-card task-card"><view class="activity-content"><text class="activity-title">做任务赚积分</text><text class="activity-time">活动时间：2025.09.01-09.15</text><text class="activity-task">收藏名片获取<text class="highlight-blue">10积分</text></text><text class="activity-task">发布需求获取<text class="highlight-blue">30积分</text></text><text class="activity-more">......</text></view><view class="activity-btn blue-btn" bindtap="{{A}}"><text class="btn-text">做任务</text></view></view></view><view class="invite-section"><image wx:if="{{B}}" src="{{C}}" class="invite-card-image" mode="widthFix" bindtap="{{D}}"></image></view><view class="section-block exchange-block"><image src="{{E}}" mode="widthFix" class="list_bg"></image><view class="section-header"><text class="section-title">积分兑换</text></view><view class="exchange-content"><image wx:if="{{F}}" src="{{G}}" class="card-image" mode="widthFix" bindtap="{{H}}"></image><image wx:if="{{I}}" src="{{J}}" class="card-image" mode="widthFix" bindtap="{{K}}"></image></view></view></view><view wx:if="{{L}}" class="task-modal-overlay" bindtap="{{T}}"><view class="task-modal-container" catchtap="{{S}}"><view class="task-modal-close" bindtap="{{N}}"><image src="{{M}}" class="close-icon" mode="aspectFit"></image></view><view class="task-modal-header"><image src="{{O}}" class="task-modal-title-img" mode="widthFix"></image></view><view class="task-list"><view wx:if="{{P}}" class="task-loading"><text>加载任务列表中...</text></view><view wx:for="{{Q}}" wx:for-item="task" wx:key="g" class="task-item"><view class="task-icon"><image src="{{task.a}}" class="task-icon-img" mode="aspectFit"></image></view><view class="task-content"><text class="task-title">{{task.b}}</text><text class="task-desc">{{task.c}}</text></view><view class="{{['task-btn', task.e]}}" bindtap="{{task.f}}"><text class="task-btn-text">{{task.d}}</text></view></view><view wx:if="{{R}}" class="task-empty"><text>暂无可用任务</text></view></view></view></view><view wx:if="{{U}}" class="makeup-explain-overlay" bindtap="{{ab}}"><view class="makeup-explain-dialog" catchtap="{{aa}}"><image wx:if="{{V}}" src="{{W}}" class="makeup-explain-bg" mode="widthFix"></image><button class="makeup-explain-btn" bindtap="{{X}}">我 知 道 了</button><view class="makeup-explain-close" bindtap="{{Z}}"><view class="close-circle"><image src="{{Y}}" class="close-icon" mode="aspectFit"></image></view></view></view></view>