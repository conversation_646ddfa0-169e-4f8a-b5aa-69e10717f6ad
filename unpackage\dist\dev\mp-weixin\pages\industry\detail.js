"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      loading: true,
      companyDetail: {},
      parsedTags: [],
      parsedProductTags: [],
      parsedQualifications: [],
      parsedIndustryTags: [],
      // 联系人信息弹窗
      showContactPopup: false,
      contactInfo: {
        contactName: "",
        contactPhone: "",
        qrCodeUrl: "",
        title: ""
      }
    };
  },
  onLoad(options) {
    if (options.companyData) {
      try {
        this.companyDetail = JSON.parse(decodeURIComponent(options.companyData));
        this.parsedProductTags = this.parseProductTags(this.companyDetail.mainProductsServices);
        this.parsedQualifications = this.parseQualifications(this.companyDetail.qualifications);
        this.parsedIndustryTags = this.parseIndustryTags(this.companyDetail.industryNames);
        this.loading = false;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/detail.vue:193", "解析企业数据失败:", error);
      }
    } else {
      this.loading = false;
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
    }
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 解析标签
    parseTags(tagsString) {
      if (!tagsString)
        return [];
      return tagsString.split(",").filter((tag) => tag.trim());
    },
    // 解析产品标签
    parseProductTags(productTagsString) {
      if (!productTagsString)
        return [];
      if (typeof productTagsString === "string") {
        return productTagsString.split(",").filter((tag) => tag.trim()).slice(0, 5);
      }
      if (Array.isArray(productTagsString)) {
        return productTagsString.slice(0, 5);
      }
      return [];
    },
    // 解析资质标签
    parseQualifications(qualificationsString) {
      if (!qualificationsString)
        return [];
      if (typeof qualificationsString === "string") {
        return qualificationsString.split(",").filter((tag) => tag.trim());
      }
      if (Array.isArray(qualificationsString)) {
        return qualificationsString;
      }
      return [];
    },
    // 解析行业标签
    parseIndustryTags(industryTagsString) {
      if (!industryTagsString)
        return [];
      if (typeof industryTagsString === "string") {
        return industryTagsString.split(",").filter((tag) => tag.trim());
      }
      if (Array.isArray(industryTagsString)) {
        return industryTagsString;
      }
      return [];
    },
    // 图片错误处理
    onTopImageError() {
      common_vendor.index.__f__("log", "at pages/industry/detail.vue:265", "顶图加载失败");
    },
    onLogoError() {
      common_vendor.index.__f__("log", "at pages/industry/detail.vue:269", "Logo加载失败");
    },
    // 拨打电话
    makePhoneCall(phoneNumber) {
      common_vendor.index.makePhoneCall({
        phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/detail.vue:277", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    },
    // 打开网站
    openWebsite(url) {
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "https://" + url;
      }
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          common_vendor.index.showToast({
            title: "网址已复制到剪贴板",
            icon: "success"
          });
        }
      });
    },
    // 联系企业
    contactCompany() {
      if (this.companyDetail.contactInfo && this.companyDetail.contactInfo.phone) {
        this.makePhoneCall(this.companyDetail.contactInfo.phone);
      } else {
        common_vendor.index.showToast({
          title: "暂无联系方式",
          icon: "none"
        });
      }
    },
    // 分享企业
    shareCompany() {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: "",
        title: this.companyDetail.companyName || "企业详情",
        summary: this.companyDetail.description || "查看企业详细信息",
        imageUrl: this.companyDetail.logoUrl || "",
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/detail.vue:334", "分享失败:", err);
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    },
    // 我有需求 - 跳转到资源场景需求发布页面（与主页面一致）
    async haveDemand() {
      common_vendor.index.__f__("log", "at pages/industry/detail.vue:345", "🎯 我有需求 - 跳转到资源场景需求发布页面");
      try {
        common_vendor.index.__f__("log", "at pages/industry/detail.vue:349", "🎯 获取分类列表，查找资源场景分类ID...");
        const response = await utils_request.request.post("/miniapp/demandcategory/app/getEnabledList");
        common_vendor.index.__f__("log", "at pages/industry/detail.vue:352", "🎯 分类列表响应:", response);
        if (response && response.data && response.data.code === 200) {
          const categories = response.data.data || [];
          const scenarioCategory = categories.find(
            (cat) => cat.categoryCode === "scenario" || cat.categoryName === "资源场景" || cat.name === "资源场景"
          );
          if (scenarioCategory) {
            const categoryId = scenarioCategory.categoryId || scenarioCategory.id;
            const categoryName = scenarioCategory.categoryName || scenarioCategory.name || "资源场景";
            common_vendor.index.__f__("log", "at pages/industry/detail.vue:368", "🎯 找到资源场景分类:", { categoryId, categoryName });
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryId=${categoryId}&categoryName=${encodeURIComponent(categoryName)}&categoryCode=scenario`
            });
          } else {
            common_vendor.index.__f__("log", "at pages/industry/detail.vue:375", "🎯 ❌ 未找到资源场景分类，使用默认方式");
            common_vendor.index.navigateTo({
              url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/industry/detail.vue:382", "🎯 获取分类列表失败:", response);
          common_vendor.index.navigateTo({
            url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/detail.vue:389", "🎯 获取分类列表异常:", error);
        common_vendor.index.navigateTo({
          url: `/pages/demand/publish?categoryName=${encodeURIComponent("资源场景")}&categoryCode=scenario`
        });
      }
    },
    // 我有疑问 - 与需求广场功能一致
    async haveQuestion() {
      var _a, _b;
      common_vendor.index.__f__("log", "at pages/industry/detail.vue:399", "📋 问题咨询");
      common_vendor.index.showLoading({
        title: "获取联系信息..."
      });
      try {
        common_vendor.index.__f__("log", "at pages/industry/detail.vue:408", "📤 调用获取联系人信息接口");
        const requestData = {
          contactCode: "",
          contactId: 0,
          contactName: "",
          contactPhone: "",
          createBy: "",
          createTime: "",
          params: {},
          qrCodeUrl: "",
          remark: "",
          sortOrder: 0,
          status: "",
          updateBy: "",
          updateTime: ""
        };
        const response = await utils_request.request.post("/miniapp/contact/app/getByContactCode", requestData);
        common_vendor.index.__f__("log", "at pages/industry/detail.vue:425", "📥 联系人信息响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const contactInfo = response.data.data;
          common_vendor.index.__f__("log", "at pages/industry/detail.vue:431", "✅ 获取联系人信息成功:", contactInfo);
          this.showContactModal(contactInfo, "企业详情咨询");
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || "获取联系信息失败";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/detail.vue:444", "📋 获取联系人信息失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 显示联系人信息弹窗
    showContactModal(contactInfo, title) {
      this.contactInfo = {
        contactName: contactInfo.contactName || contactInfo.name || "客服",
        contactPhone: contactInfo.contactPhone || contactInfo.phone || "",
        qrCodeUrl: contactInfo.qrCodeUrl || "",
        title: title || "联系信息"
      };
      this.showContactPopup = true;
    },
    // 关闭联系人弹窗
    closeContactPopup() {
      this.showContactPopup = false;
    },
    // 拨打电话
    makeCall() {
      const phoneNumber = this.contactInfo.contactPhone || "15620361895";
      common_vendor.index.makePhoneCall({
        phoneNumber,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/industry/detail.vue:479", "拨打电话失败:", err);
          common_vendor.index.showToast({
            title: "拨打电话失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $options.processServerImageUrl($data.companyDetail.logoUrl, $options.getImagePath("avatar1.png")),
    c: common_vendor.o((...args) => $options.onTopImageError && $options.onTopImageError(...args)),
    d: common_vendor.t($data.companyDetail.enterpriseName || "暂无名称"),
    e: $data.companyDetail.location
  }, $data.companyDetail.location ? {
    f: common_vendor.t($data.companyDetail.location)
  } : {}, {
    g: $data.parsedIndustryTags && $data.parsedIndustryTags.length > 0
  }, $data.parsedIndustryTags && $data.parsedIndustryTags.length > 0 ? {
    h: common_vendor.f($data.parsedIndustryTags, (tag, tagIndex, i0) => {
      return {
        a: common_vendor.t(tag),
        b: "industry-" + tagIndex
      };
    })
  } : {}, {
    i: $data.companyDetail.financingRound
  }, $data.companyDetail.financingRound ? {
    j: common_vendor.t($data.companyDetail.financingRound)
  } : {}, {
    k: $data.companyDetail.scale
  }, $data.companyDetail.scale ? {
    l: common_vendor.t($data.companyDetail.scale)
  } : {}, {
    m: $data.companyDetail.enterpriseType
  }, $data.companyDetail.enterpriseType ? {
    n: common_vendor.t($data.companyDetail.enterpriseType)
  } : {}, {
    o: $data.parsedQualifications && $data.parsedQualifications.length > 0
  }, $data.parsedQualifications && $data.parsedQualifications.length > 0 ? {
    p: common_vendor.f($data.parsedQualifications, (tag, tagIndex, i0) => {
      return {
        a: common_vendor.t(tag),
        b: "qual-" + tagIndex
      };
    })
  } : {}, {
    q: $data.companyDetail.businessDescription
  }, $data.companyDetail.businessDescription ? {
    r: common_vendor.t($data.companyDetail.businessDescription)
  } : {}, {
    s: $data.companyDetail.mainProductsServices || $data.companyDetail.additionalContent
  }, $data.companyDetail.mainProductsServices || $data.companyDetail.additionalContent ? common_vendor.e({
    t: $data.parsedProductTags && $data.parsedProductTags.length > 0
  }, $data.parsedProductTags && $data.parsedProductTags.length > 0 ? {
    v: common_vendor.f($data.parsedProductTags, (tag, tagIndex, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tagIndex
      };
    })
  } : {}, {
    w: $data.companyDetail.additionalContent
  }, $data.companyDetail.additionalContent ? {
    x: common_vendor.t($data.companyDetail.additionalContent)
  } : {}) : {}, {
    y: $data.companyDetail.contactInfo
  }, $data.companyDetail.contactInfo ? common_vendor.e({
    z: $data.companyDetail.contactInfo.phone
  }, $data.companyDetail.contactInfo.phone ? {
    A: common_vendor.t($data.companyDetail.contactInfo.phone),
    B: common_vendor.o(($event) => $options.makePhoneCall($data.companyDetail.contactInfo.phone))
  } : {}, {
    C: $data.companyDetail.contactInfo.email
  }, $data.companyDetail.contactInfo.email ? {
    D: common_vendor.t($data.companyDetail.contactInfo.email)
  } : {}, {
    E: $data.companyDetail.contactInfo.website
  }, $data.companyDetail.contactInfo.website ? {
    F: common_vendor.t($data.companyDetail.contactInfo.website),
    G: common_vendor.o(($event) => $options.openWebsite($data.companyDetail.contactInfo.website))
  } : {}) : {}), {
    H: common_vendor.o((...args) => $options.haveQuestion && $options.haveQuestion(...args)),
    I: common_vendor.o((...args) => $options.haveDemand && $options.haveDemand(...args)),
    J: $data.showContactPopup
  }, $data.showContactPopup ? common_vendor.e({
    K: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args)),
    L: common_vendor.t($data.contactInfo.contactPhone || "15620361895"),
    M: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args)),
    N: $data.contactInfo.qrCodeUrl
  }, $data.contactInfo.qrCodeUrl ? {
    O: $data.contactInfo.qrCodeUrl
  } : {}, {
    P: common_vendor.o(() => {
    }),
    Q: common_vendor.o((...args) => $options.closeContactPopup && $options.closeContactPopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/detail.js.map
