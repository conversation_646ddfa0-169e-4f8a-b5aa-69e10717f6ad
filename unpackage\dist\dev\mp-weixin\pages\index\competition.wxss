.competition-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.header-section {
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  position: relative;
  color: white;
  text-align: center;
  margin-bottom: 45rpx;
}
/* 顶图容器样式 */
.header-image-container {
  position: relative;
  width: 100%;
}
.header-image {
  width: 100%;
  display: block;
}
.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  letter-spacing: 2rpx;
}
.sub-title {
  font-size: 28rpx;
  margin-bottom: 16rpx;
  opacity: 0.9;
}
.sub-title2 {
  font-size: 28rpx;
  opacity: 0.9;
}
.cards-section {
  padding: 40rpx 30rpx;
  margin-top: -40rpx;
}
.competition-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  padding: 0;
  box-shadow: 0 8rpx 30rpx #a7c0e6;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.competition-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(167, 192, 230, 0.8);
}
/* 动态路演卡片样式 */
.roadshow-card-0,
.roadshow-card-1,
.roadshow-card-2,
.roadshow-card-3 {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  padding: 0;
  box-shadow: 0 8rpx 30rpx #a7c0e6;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.roadshow-card-0:active,
.roadshow-card-1:active,
.roadshow-card-2:active,
.roadshow-card-3:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(167, 192, 230, 0.8);
}
/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 30rpx rgba(167, 192, 230, 0.3);
}
.loading-text {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}
.card-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: auto;
  height: auto;
}
.card-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  position: absolute;
  left: 40rpx;
  top: 50rpx;
}
.icon-image {
  width: 80rpx;
  height: 80rpx;
  filter: brightness(0) invert(1);
}
.card-text {
  margin-left: 180rpx;
  margin-top: 30rpx;
  flex: 1;
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
.enter-btn {
  background: rgba(255, 255, 255, 0.15);
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid rgba(255, 255, 255, 0.25);
  position: absolute;
  right: 40rpx;
  bottom: 30rpx;
  width: auto;
}
.btn-text {
  font-size: 22rpx;
  color: white;
  margin-right: 8rpx;
}
.btn-arrow {
  font-size: 22rpx;
  color: white;
  font-weight: bold;
}
/* 点击效果 */
.competition-card:active {
  transform: scale(0.98);
  transition: transform 0.2s ease;
}
