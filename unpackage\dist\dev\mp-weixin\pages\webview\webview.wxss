
.webview-container.data-v-deb32cb9 {
	height: 100vh;
	background-color: #fff;
}

/* 自定义导航栏 */
.custom-navbar.data-v-deb32cb9 {
	background: linear-gradient(45deg, #003399, #4A90E2);
	color: white;
}
.navbar-content.data-v-deb32cb9 {
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
}
.navbar-left.data-v-deb32cb9 {
	display: flex;
	align-items: center;
	flex: 1;
}
.iconfont.data-v-deb32cb9 {
	font-size: 18px;
	margin-right: 5px;
}
.back-text.data-v-deb32cb9 {
	font-size: 16px;
}
.navbar-title.data-v-deb32cb9 {
	flex: 2;
	text-align: center;
	font-size: 16px;
	font-weight: 500;
	/* 文字超长省略 */
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.navbar-right.data-v-deb32cb9 {
	display: flex;
	align-items: center;
	flex: 1;
	justify-content: flex-end;
}
.navbar-btn.data-v-deb32cb9 {
	padding: 5px 8px;
	margin-left: 10px;
	border-radius: 4px;
	background: rgba(255, 255, 255, 0.2);
}

/* 进度条 */
.progress-bar.data-v-deb32cb9 {
	height: 2px;
	background-color: #f0f0f0;
	position: relative;
	overflow: hidden;
}
.progress-fill.data-v-deb32cb9 {
	height: 100%;
	background: linear-gradient(90deg, #4A90E2, #003399);
	transition: width 0.3s ease;
}

/* WebView */
.webview.data-v-deb32cb9 {
	width: 100%;
	background-color: #fff;
}

/* 加载遮罩 */
.loading-mask.data-v-deb32cb9 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}
.loading-content.data-v-deb32cb9 {
	text-align: center;
}
.loading-spinner.data-v-deb32cb9 {
	width: 40px;
	height: 40px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #4A90E2;
	border-radius: 50%;
	animation: spin-deb32cb9 1s linear infinite;
	margin: 0 auto 15px;
}
.loading-text.data-v-deb32cb9 {
	font-size: 14px;
	color: #666;
}
@keyframes spin-deb32cb9 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* 错误页面 */
.error-page.data-v-deb32cb9 {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 40px 20px;
}
.error-icon.data-v-deb32cb9 {
	font-size: 60px;
	margin-bottom: 20px;
}
.error-title.data-v-deb32cb9 {
	font-size: 18px;
	color: #333;
	margin-bottom: 10px;
	font-weight: 500;
}
.error-desc.data-v-deb32cb9 {
	font-size: 14px;
	color: #666;
	margin-bottom: 30px;
	line-height: 1.5;
}
.error-actions.data-v-deb32cb9 {
	display: flex;
	justify-content: center;
	gap: 15px;
}
.retry-btn.data-v-deb32cb9, .copy-btn.data-v-deb32cb9 {
	padding: 10px 20px;
	border-radius: 6px;
	font-size: 14px;
	border: none;
}
.retry-btn.data-v-deb32cb9 {
	background: linear-gradient(45deg, #003399, #4A90E2);
	color: white;
}
.copy-btn.data-v-deb32cb9 {
	background: #f0f0f0;
	color: #333;
}
