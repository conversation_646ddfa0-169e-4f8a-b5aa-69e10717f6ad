.demand-square-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #bfdbfe 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}
.header-section {
  padding: 40rpx 50rpx 60rpx;
}
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx #c7cce1;
}
.category-icon {
  margin-bottom: 0rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-icon image {
  width: 48rpx;
  height: 48rpx;
}
.category-text {
  font-size: 24rpx;
  color: #111111;
  font-weight: 900;
}
.filter-section {
  background: white;
  display: flex;
  padding: 10rpx 0rpx;
  margin: -30rpx 30rpx 30rpx;
  position: relative;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx #c7d6f2;
}
.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #f0f0f0;
}
.filter-item:last-child {
  border-right: none;
}
.filter-picker {
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 40rpx;
  font-weight: 900;
}
.filter-arrow {
  font-size: 20rpx;
  margin-top: -2rpx;
}
.demand-list {
  padding: 0 30rpx;
}
.demand-item {
  position: relative;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 30rpx 30rpx 120rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx #c2d1f1;
}
.demand-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20rpx;
}
.demand-category {
  padding: 26rpx 20rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: white;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #333;
}
.finance-category {
  background: linear-gradient(180deg, #e6cc78 0%, #e2a86f 100%);
}
.tech-category {
  background: linear-gradient(180deg, #5bbce0 0%, #176db0 100%);
}
.venue-category {
  background: linear-gradient(180deg, #50E3C2 0%, #4ECDC4 100%);
}
.resource-category {
  background: linear-gradient(180deg, #f5ac1b 0%, #d44c0d 100%);
}
.office-category {
  background: linear-gradient(180deg, #BD10E0 0%, #9013FE 100%);
}
.factory-category {
  background: linear-gradient(180deg, #B8E986 0%, #50C878 100%);
}
.media-category {
  background: linear-gradient(180deg, #FF6B6B 0%, #EE5A52 100%);
}
.other-category {
  background: linear-gradient(180deg, #A8A8A8 0%, #808080 100%);
}
/* 根据categoryCode的新样式类 */
.financing-category {
  background: linear-gradient(180deg, #4A90E2 0%, #357ABD 100%);
  /* 蓝色 - 融资对接 */
}
.technology-category {
  background: linear-gradient(180deg, #7ED321 0%, #5CB85C 100%);
  /* 绿色 - 技术合作 */
}
.exposure-category {
  background: linear-gradient(180deg, #50E3C2 0%, #4ECDC4 100%);
  /* 青色 - 曝光 */
}
.scenario-category {
  background: linear-gradient(180deg, #BD10E0 0%, #9013FE 100%);
  /* 紫色 - 资源场景 */
}
.qualification-category {
  background: linear-gradient(180deg, #F5A623 0%, #D68910 100%);
  /* 橙色 - 政策资质 */
}
.office-category {
  background: linear-gradient(180deg, #FF6B9D 0%, #E91E63 100%);
  /* 粉色 - 办公载体 */
}
.factory-category {
  background: linear-gradient(180deg, #8B4513 0%, #A0522D 100%);
  /* 棕色 - 厂房载体 */
}
.consult-category {
  background: linear-gradient(180deg, #95A5A6 0%, #7F8C8D 100%);
  /* 灰色 - 管理咨询 */
}
.demand-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.demand-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 显示 2 行 */
  overflow: hidden;
}
.demand-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.demand-date {
  font-size: 24rpx;
  color: #999;
}
.demand-status {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}
/* 状态颜色 - 与详情页面保持一致 */
.demand-status.status-pending {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
  /* 橙色 - 待审核 */
}
.demand-status.status-published {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  /* 绿色 - 已发布 */
}
.demand-status.status-docked {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  /* 蓝色 - 已对接 */
}
.demand-status.status-offline {
  background: linear-gradient(135deg, #8c8c8c, #595959);
  /* 灰色 - 已下架 */
}
.demand-status.status-rejected {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
  /* 红色 - 审核拒绝 */
}
.demand-status.status-unknown {
  background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
  /* 浅灰色 - 未知状态 */
}
.status-text {
  font-size: 22rpx;
  color: white;
}
.publish-btn {
  padding: 6rpx 16rpx;
  background: #fad676;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}
/* 底部导航栏样式 - 与其他页面保持一致 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -6rpx 16rpx #c8d5f2;
  z-index: 100;
  padding-bottom: 20rpx;
}
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  position: relative;
}
.tab-icon {
  width: 44rpx;
  height: 44rpx;
}
.tab-text {
  font-size: 22rpx;
  color: #444;
}
.tab-item.active .tab-text {
  color: #003399;
}
.tab-center-icon {
  width: 88rpx;
  height: 88rpx;
  background-color: #003399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: relative;
  top: -48rpx;
}
.tab-center-image {
  width: 60rpx;
  height: 60rpx;
}
.tab-center-text {
  font-size: 22rpx;
  color: #023caa;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
/* 右侧悬浮按钮 */
.floating-buttons {
  position: fixed;
  right: 0rpx;
  top: 36%;
  transform: translateY(-50%);
  z-index: 999;
  display: flex;
  flex-direction: column;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx #c1c5d4;
}
.floating-btn {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.floating-btn.question-btn {
  background: #023caa;
  color: white;
}
.floating-btn.demand-btn {
  background: #fad676;
  color: #333;
}
.floating-btn-text {
  font-size: 26rpx;
}
/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
/* 置顶标签样式 */
.top-badge {
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
/* 需求元信息样式 */
.demand-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.view-count {
  font-size: 24rpx;
  color: #999;
}
/* 联系人弹窗样式 */
.contact-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.contact-popup-content {
  width: 500rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx 40rpx;
  position: relative;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #203ba3;
  border-radius: 50rpx;
}
.close-icon {
  font-size: 36rpx;
  font-weight: normal;
  line-height: 1;
  color: #203ba3;
}
.popup-main-title {
  margin-bottom: 40rpx;
}
.title-line {
  display: block;
  font-size: 36rpx;
  font-weight: 900;
  color: #000;
  margin-bottom: 10rpx;
}
.contact-phone {
  margin-bottom: 40rpx;
}
.phone-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 900;
}
.qr-code-container {
  display: flex;
  justify-content: center;
}
.qr-code-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 8rpx;
}
.tip-text {
  font-size: 24rpx;
  font-weight: 900;
  color: #000;
}
