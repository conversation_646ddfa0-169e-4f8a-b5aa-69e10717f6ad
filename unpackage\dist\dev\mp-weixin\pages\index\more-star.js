"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      starList: []
      // 科技之星列表
    };
  },
  onLoad() {
    this.getTechStarList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 获取科技之星列表 - 使用与首页相同的API
    async getTechStarList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/more-star.vue:53", "⭐ 开始获取科技之星列表...");
        common_vendor.index.__f__("log", "at pages/index/more-star.vue:54", "⭐ API地址: GET /miniapp/techstar/enabled");
        const response = await utils_request.request.get("/miniapp/techstar/enabled");
        if (response && response.success && response.data && response.data.code === 200) {
          let starData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/more-star.vue:59", "⭐ 原始科技之星数据:", starData);
          this.starList = starData.map((item) => ({
            id: item.id || item.starId,
            name: item.name || item.realName || "暂无姓名",
            description1: item.description1 || "暂无介绍",
            description2: item.description2 || "暂无介绍",
            // 使用与首页一致的头像字段
            avatar: utils_imageUtils.processServerImageUrl(item.coverUrl || item.photoUrl, utils_imageUtils.getImagePath("default-avatar.png")),
            // 保留原始数据用于详情页
            rawData: item
          }));
          common_vendor.index.__f__("log", "at pages/index/more-star.vue:73", "⭐ 科技之星列表获取成功，共", this.starList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/more-star.vue:74", "⭐ 处理后的科技之星数据:", this.starList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/more-star.vue:76", "⭐ ❌ 科技之星数据获取失败！");
          this.starList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-star.vue:80", "⭐ 获取科技之星列表失败:", error);
        this.starList = [];
      }
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/index/more-star.vue:87", "图片加载失败:", e);
    },
    // 查看科技之星详情
    viewStarDetail(star) {
      common_vendor.index.__f__("log", "at pages/index/more-star.vue:92", "查看科技之星详情:", star);
      const starData = encodeURIComponent(JSON.stringify(star.rawData || star));
      common_vendor.index.navigateTo({
        url: `/pages/index/star-detail?starData=${starData}`
      });
    },
    // 导航到首页
    navigateToHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/home"
      });
    },
    // 导航到人脉资源页面
    navigateToContacts() {
      common_vendor.index.reLaunch({
        url: "/pages/index/contacts"
      });
    },
    // 导航到需求广场页面
    navigateToDemandSquare() {
      common_vendor.index.reLaunch({
        url: "/pages/index/demand-square"
      });
    },
    // 导航到我的页面
    navigateToMine() {
      common_vendor.index.reLaunch({
        url: "/pages/index/mine"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.starList && $data.starList.length > 0
  }, $data.starList && $data.starList.length > 0 ? {
    b: common_vendor.f($data.starList, (star, index, i0) => {
      return {
        a: star.avatar,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), star.id || index),
        c: common_vendor.t(star.name),
        d: common_vendor.t(star.description1),
        e: common_vendor.t(star.description2),
        f: star.id || index,
        g: common_vendor.o(($event) => $options.viewStarDetail(star), star.id || index)
      };
    })
  } : {
    c: $options.getImagePath("icon7.png")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/more-star.js.map
