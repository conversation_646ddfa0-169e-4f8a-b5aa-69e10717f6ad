/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.contacts-new-container.data-v-c3d5c24b {
  min-height: 100vh;
  background-color: #f3f8fe;
  position: relative;
  padding-bottom: 120rpx;
  /* 底部导航栏的高度 */
}

/* 搜索和筛选区域 */
.search-filter-section.data-v-c3d5c24b {
  padding: 40rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.search-filter-section .search-container.data-v-c3d5c24b {
  flex: 1;
  display: flex;
  align-items: center;
  border-radius: 8rpx;
  padding: 0;
}
.search-filter-section .search-container .search-input.data-v-c3d5c24b {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f6f7f9;
  padding: 0 20rpx;
  border: 1px solid #dfe0e2;
}
.search-filter-section .search-container .search-btn.data-v-c3d5c24b {
  background-color: #72a5ff;
  color: #fff;
  display: flex;
  align-items: center;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  height: 60rpx;
}
.search-filter-section .filter-btn.data-v-c3d5c24b {
  background-color: #ffb83c;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  height: 60rpx;
}
.search-filter-section .filter-btn .filter-icon.data-v-c3d5c24b {
  width: 28rpx;
  height: 28rpx;
}
.search-filter-section .filter-btn .filter-text.data-v-c3d5c24b {
  font-size: 26rpx;
}

/* 排行榜区域 */
.ranking-section.data-v-c3d5c24b {
  padding: 10rpx 20rpx;
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.ranking-section .ranking-card.data-v-c3d5c24b {
  flex: 1;
  background: linear-gradient(180deg, #d1e0ff, #fff);
  border-radius: 12rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  /* 给容器添加阴影 */
}
.ranking-section .ranking-card .ranking-header.data-v-c3d5c24b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
}
.ranking-section .ranking-card .ranking-header .flex.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  gap: 10rpx;
}
.ranking-section .ranking-card .ranking-header .flex image.data-v-c3d5c24b {
  width: 28rpx;
  height: 28rpx;
}
.ranking-section .ranking-card .ranking-header .flex .ranking-title.data-v-c3d5c24b {
  font-size: 24rpx;
  font-weight: 600;
}
.ranking-section .ranking-card .ranking-header .view-more.data-v-c3d5c24b {
  color: #003399;
  font-size: 20rpx;
}
.ranking-section .ranking-card .ranking-avatars.data-v-c3d5c24b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
}
.ranking-section .ranking-card .ranking-avatars .ranking-avatar-item.data-v-c3d5c24b {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  cursor: pointer;
}
.ranking-section .ranking-card .ranking-avatars .ranking-avatar-item .avatar-container.data-v-c3d5c24b {
  margin-bottom: 10rpx;
}
.ranking-section .ranking-card .ranking-avatars .ranking-avatar-item .avatar-container .ranking-avatar.data-v-c3d5c24b {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.ranking-section .ranking-card .ranking-avatars .ranking-avatar-item .ranking-nickname.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100rpx;
}
.ranking-section .ranking-card .ranking-list.data-v-c3d5c24b {
  padding: 20rpx;
}
.ranking-section .ranking-card .ranking-list .ranking-item.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 8rpx 0;
  color: #161551;
}
.ranking-section .ranking-card .ranking-list .ranking-item .ranking-badge.data-v-c3d5c24b {
  width: 60rpx;
  height: 30rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #fff;
  margin-right: 10rpx;
  flex-shrink: 0;
}
.ranking-section .ranking-card .ranking-list .ranking-item .ranking-name.data-v-c3d5c24b {
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ranking-section .ranking-card .ranking-list .ranking-item .ranking-score.data-v-c3d5c24b {
  font-size: 22rpx;
  white-space: nowrap;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4rpx;
}
.ranking-section .ranking-card .ranking-list .ranking-item .ranking-score text.data-v-c3d5c24b {
  font-size: 22rpx;
}
.ranking-section .ranking-card.active-users .top1.data-v-c3d5c24b {
  background-color: #de7c54;
}
.ranking-section .ranking-card.active-users .top2.data-v-c3d5c24b {
  background-color: #db9f59;
}
.ranking-section .ranking-card.active-users .top3.data-v-c3d5c24b {
  background-color: #dbb958;
}
.ranking-section .ranking-card.active-users .score1.data-v-c3d5c24b {
  color: #de7c54;
  font-weight: 900;
}
.ranking-section .ranking-card.active-users .score2.data-v-c3d5c24b {
  color: #db9f59;
  font-weight: 900;
}
.ranking-section .ranking-card.active-users .score3.data-v-c3d5c24b {
  color: #dbb958;
  font-weight: 900;
}
.ranking-section .ranking-card.popular-users .top1.data-v-c3d5c24b {
  background-color: #c50c1a;
}
.ranking-section .ranking-card.popular-users .top2.data-v-c3d5c24b {
  background-color: #ec5372;
}
.ranking-section .ranking-card.popular-users .top3.data-v-c3d5c24b {
  background-color: #f88c9c;
}
.ranking-section .ranking-card.popular-users .score1.data-v-c3d5c24b {
  color: #c50c1a;
  font-weight: 900;
}
.ranking-section .ranking-card.popular-users .score2.data-v-c3d5c24b {
  color: #ec5372;
  font-weight: 900;
}
.ranking-section .ranking-card.popular-users .score3.data-v-c3d5c24b {
  color: #f88c9c;
  font-weight: 900;
}

/* 用户卡片区域 */
.cards-container.data-v-c3d5c24b {
  padding: 40rpx calc((100% - 676rpx) / 2);
  height: 1076rpx;
  width: 676rpx;
  position: relative;
  overflow: visible;
}
.cards-container .cards-wrapper.data-v-c3d5c24b {
  height: 100%;
  position: relative;
  overflow: visible;
  /* 防止iOS系统手势冲突 */
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  /* 只允许垂直滚动 */
}
.cards-container .user-card.data-v-c3d5c24b {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx #c8d5f2;
  /* 给容器添加阴影 */
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  height: 100%;
}
.cards-container .user-card .card-avatar.data-v-c3d5c24b {
  height: 676rpx;
  position: relative;
  overflow: hidden;
}
.cards-container .user-card .card-avatar .avatar-image.data-v-c3d5c24b {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cards-container .user-card .card-info.data-v-c3d5c24b {
  padding: 32rpx;
  position: relative;
}
.cards-container .user-card .card-info .user-header.data-v-c3d5c24b {
  display: flex;
  align-items: baseline;
  margin-bottom: 6rpx;
  margin-right: 120rpx;
}
.cards-container .user-card .card-info .user-header .user-name.data-v-c3d5c24b {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}
.cards-container .user-card .card-info .user-header .user-location.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #333;
  font-weight: 900;
}
.cards-container .user-card .card-info .user-header .user-points.data-v-c3d5c24b {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  background: #dcb955;
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.cards-container .user-card .card-info .user-header .user-points .points-icon.data-v-c3d5c24b {
  width: 28rpx;
  height: 28rpx;
}
.cards-container .user-card .card-info .user-header .user-points .points-text.data-v-c3d5c24b {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}
.cards-container .user-card .card-info .user-education.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #333;
  font-weight: 900;
  margin-bottom: 6rpx;
}
.cards-container .user-card .card-info .user-profession.data-v-c3d5c24b {
  margin-bottom: 30rpx;
}
.cards-container .user-card .card-info .user-profession .profession-item.data-v-c3d5c24b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}
.cards-container .user-card .card-info .user-profession .profession-item .company.data-v-c3d5c24b {
  font-size: 24rpx;
  color: #333;
}
.cards-container .user-card .card-info .user-profession .profession-item .position.data-v-c3d5c24b {
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  text-align: right;
}
.cards-container .user-card .card-info .flex.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.cards-container .user-card .card-info .flex .user-tags.data-v-c3d5c24b {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.cards-container .user-card .card-info .flex .user-tags .tag.data-v-c3d5c24b {
  background: #aacaf9;
  border-radius: 30rpx;
  padding: 0rpx 15rpx 5rpx;
  color: #ffffff;
  font-size: 26rpx;
}
.cards-container .user-card .card-info .flex .detail-link.data-v-c3d5c24b {
  text-align: right;
}
.cards-container .user-card .card-info .flex .detail-link .detail-text.data-v-c3d5c24b {
  color: #1976d2;
  font-size: 26rpx;
}
.cards-container .user-card .collect-btn.data-v-c3d5c24b {
  background: linear-gradient(180deg, #003dad, #3166c8);
  color: #fff;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 28rpx;
  border-radius: 100rpx;
}
.cards-container .user-card .collect-btn .collect-icon.data-v-c3d5c24b {
  width: 40rpx;
  height: 40rpx;
}

/* 卡片指示器 */
.card-indicator.data-v-c3d5c24b {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
  margin-top: 30rpx;
}
.card-indicator .indicator-dot.data-v-c3d5c24b {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ddd;
  transition: all 0.3s ease;
}
.card-indicator .indicator-dot.active.data-v-c3d5c24b {
  background-color: #003399;
  transform: scale(1.2);
}

/* 底部导航栏 */
.tab-bar.data-v-c3d5c24b {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -6rpx 16rpx #c8d5f2;
  z-index: 100;
  padding-bottom: 20rpx;
}
.tab-item.data-v-c3d5c24b {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  position: relative;
}
.tab-icon.data-v-c3d5c24b {
  width: 44rpx;
  height: 44rpx;
}
.tab-text.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #444;
}
.tab-item.active .tab-text.data-v-c3d5c24b {
  color: #003399;
}
.tab-center-icon.data-v-c3d5c24b {
  width: 88rpx;
  height: 88rpx;
  background-color: #003399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: relative;
  top: -48rpx;
}
.tab-center-image.data-v-c3d5c24b {
  width: 60rpx;
  height: 60rpx;
}
.tab-center-text.data-v-c3d5c24b {
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.tab-center-text.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #444;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

/* 遮罩层 */
.filter-mask.data-v-c3d5c24b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 筛选面板样式 */
.filter-panel.data-v-c3d5c24b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #f8f9fa;
  z-index: 1000;
  animation: slideDown-c3d5c24b 0.3s ease-out;
}

/* 搜索关键词样式 */
.search-keywords.data-v-c3d5c24b {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.search-keywords .keywords-label.data-v-c3d5c24b {
  font-size: 28rpx;
  color: #023caa;
  display: block;
  font-weight: 900;
}
.search-keywords .keyword-tag.data-v-c3d5c24b {
  background-color: #eaeaea;
  border-radius: 100rpx;
  padding: 12rpx 26rpx;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-keywords .keyword-tag .keyword-text.data-v-c3d5c24b {
  font-size: 24rpx;
  color: #444444;
}
@keyframes slideDown-c3d5c24b {
from {
    transform: translateY(-100%);
}
to {
    transform: translateY(0);
}
}
.filter-body.data-v-c3d5c24b {
  display: flex;
  height: 400rpx;
  background-color: #fff;
}
.filter-sidebar.data-v-c3d5c24b {
  width: 160rpx;
  background-color: #eaeaea;
}
.filter-sidebar .filter-category.data-v-c3d5c24b {
  padding: 20rpx;
  position: relative;
  cursor: pointer;
}
.filter-sidebar .filter-category .category-name.data-v-c3d5c24b {
  font-size: 24rpx;
  color: #666;
}
.filter-sidebar .filter-category.active.data-v-c3d5c24b {
  background-color: #fff;
}
.filter-sidebar .filter-category.active .category-name.data-v-c3d5c24b {
  color: #003399;
  font-weight: bold;
}
.filter-sidebar .filter-category.active.data-v-c3d5c24b::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4rpx;
  height: 30rpx;
  background-color: #003399;
  border-radius: 0 2rpx 2rpx 0;
}
.filter-options.data-v-c3d5c24b {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
}
.filter-section.data-v-c3d5c24b {
  padding-bottom: 60rpx;
  min-height: 80rpx;
}
.filter-section .section-title.data-v-c3d5c24b {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  padding-top: 5rpx;
}
.filter-section .options-grid.data-v-c3d5c24b {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25rpx;
}
.filter-section .option-item.data-v-c3d5c24b {
  padding: 12rpx 16rpx;
  background-color: #eaeaea;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-section .option-item .option-text.data-v-c3d5c24b {
  font-size: 22rpx;
  color: #444;
  text-align: center;
}
.filter-section .option-item.selected.data-v-c3d5c24b {
  background-color: #023caa;
}
.filter-section .option-item.selected .option-text.data-v-c3d5c24b {
  color: #fff;
}
.filter-footer.data-v-c3d5c24b {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
  margin-top: 60rpx;
}
.filter-footer .filter-btn-reset.data-v-c3d5c24b,
.filter-footer .filter-btn-confirm.data-v-c3d5c24b {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  cursor: pointer;
}
.filter-footer .filter-btn-reset.data-v-c3d5c24b {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
}
.filter-footer .filter-btn-confirm.data-v-c3d5c24b {
  background-color: #003399;
  color: #fff;
}

/* 测试按钮样式 */
.test-btn.data-v-c3d5c24b {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  background-color: #4a90e2;
  color: white;
  padding: 20rpx 30rpx;
  border-radius: 30rpx;
  z-index: 1000;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

/* 空数据占位符样式 */
.empty-placeholder.data-v-c3d5c24b {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  min-height: 400rpx;
  position: relative;
}
.empty-icon.data-v-c3d5c24b {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.4;
  filter: grayscale(0.3);
  animation: emptyIconFloat-c3d5c24b 3s ease-in-out infinite;
}
.empty-text.data-v-c3d5c24b {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.empty-desc.data-v-c3d5c24b {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  max-width: 400rpx;
}

/* 占位符动画效果 */
@keyframes emptyIconFloat-c3d5c24b {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-10rpx);
}
}
/* 排行榜空状态样式 */
.ranking-empty.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  min-height: 120rpx;
}
.empty-ranking-text.data-v-c3d5c24b {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 排行榜弹窗样式 */
.ranking-modal-overlay.data-v-c3d5c24b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}
.ranking-modal.data-v-c3d5c24b {
  width: 100%;
  background: linear-gradient(180deg, #d1e0ff 0%, #ffffff 100%);
  border-radius: 40rpx 40rpx 0 0;
  position: relative;
  max-height: 80vh;
  animation: slideUp-c3d5c24b 0.3s ease-out;
}
@keyframes slideUp-c3d5c24b {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.modal-close.data-v-c3d5c24b {
  position: absolute;
  top: -25rpx;
  right: 30rpx;
  z-index: 10;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0137a6;
  border-radius: 50%;
  z-index: 9999;
  border: 2px solid #fff;
}
.close-icon.data-v-c3d5c24b {
  width: 30rpx;
  height: 30rpx;
}
.modal-header.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx 20rpx;
  gap: 10rpx;
}
.header-icon.data-v-c3d5c24b {
  width: 34rpx;
  height: 34rpx;
}
.modal-title.data-v-c3d5c24b {
  font-size: 32rpx;
  font-weight: 600;
  color: #003399;
}
.top-three-section.data-v-c3d5c24b {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}
.building-bg.data-v-c3d5c24b {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 300rpx;
  z-index: 1;
}
.top-three-container.data-v-c3d5c24b {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
  padding: 0 40rpx;
  margin-top: -60rpx;
  /* 整体向上移动 */
  gap: 12rpx;
}
.modal-top-item.data-v-c3d5c24b {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 190rpx;
}
.modal-top1.data-v-c3d5c24b {
  margin-bottom: 70rpx;
  /* TOP1最高，不向下偏移 */
}
.modal-top2.data-v-c3d5c24b {
  margin-bottom: 50rpx;
  /* TOP2较低 */
}
.modal-top3.data-v-c3d5c24b {
  margin-bottom: 40rpx;
  /* TOP3中等高度 */
}
.modal-top-card-bg.data-v-c3d5c24b {
  position: absolute;
  top: 50%;
  /* 定位到中心点 */
  left: 50%;
  /* 定位到中心点 */
  width: 100%;
  /* 调整到你想要的大小 */
  height: 100%;
  /* 调整到你想要的大小 */
  transform: translate(-50%, -50%);
  /* 通过transform居中 */
  z-index: 1;
  filter: drop-shadow(0 8rpx 16rpx #b8cbf0);
}
.modal-top-content.data-v-c3d5c24b {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  padding-top: 0;
}
.modal-top-number.data-v-c3d5c24b {
  font-size: 24rpx;
  font-weight: 600;
  color: #7696d1;
  margin-bottom: 10rpx;
}
.modal-user-avatar.data-v-c3d5c24b {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.modal-avatar-img.data-v-c3d5c24b {
  width: 100%;
  height: 100%;
}
.modal-user-name.data-v-c3d5c24b {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
}
.modal-user-score.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rpx;
}
.score-unit.data-v-c3d5c24b {
  color: #333;
  font-size: 20rpx;
  font-weight: normal;
}
.modal-ranking-list-section-big.data-v-c3d5c24b {
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  margin-top: -20rpx;
}
.modal-ranking-list-section-big .modal-ranking-list-section.data-v-c3d5c24b {
  position: relative;
  z-index: 3;
  max-height: 600rpx;
  /* 限制最大高度，超出部分滚动 */
}
.modal-ranking-list-item.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  padding: 25rpx;
  gap: 20rpx;
}
.modal-ranking-list-item.bg-white.data-v-c3d5c24b {
  background: #ffffff;
}
.modal-ranking-list-item.bg-light-blue.data-v-c3d5c24b {
  background: #e2edff;
}
.modal-rank-number.data-v-c3d5c24b {
  font-size: 28rpx;
  font-weight: 600;
  color: #7696d1;
  width: 60rpx;
}
.modal-user-avatar-small.data-v-c3d5c24b {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
}
.modal-avatar-img-small.data-v-c3d5c24b {
  width: 100%;
  height: 100%;
}
.modal-user-name-small.data-v-c3d5c24b {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  font-weight: 900;
}
.modal-user-score-small.data-v-c3d5c24b {
  display: flex;
  align-items: center;
  gap: 2rpx;
}
.score-unit-small.data-v-c3d5c24b {
  color: #333;
  font-size: 24rpx;
  font-weight: normal;
}
.modal-active-score.data-v-c3d5c24b {
  color: #7696d1;
  font-weight: 600;
  font-size: 24rpx;
}
.modal-popular-score.data-v-c3d5c24b {
  color: #7696d1;
  font-weight: 600;
  font-size: 24rpx;
}

/* 弹窗中的热度用户颜色 */
.modal-popular-score1.data-v-c3d5c24b {
  color: #c50c1a;
  font-weight: 900;
  font-size: 20rpx;
}
.modal-popular-score2.data-v-c3d5c24b {
  color: #ec5372;
  font-weight: 900;
  font-size: 20rpx;
}
.modal-popular-score3.data-v-c3d5c24b {
  color: #f88c9c;
  font-weight: 900;
  font-size: 20rpx;
}

/* 弹窗中的活跃用户颜色 */
.modal-score1.data-v-c3d5c24b {
  color: #de7c54;
  font-weight: 900;
  font-size: 20rpx;
}
.modal-score2.data-v-c3d5c24b {
  color: #db9f59;
  font-weight: 900;
  font-size: 20rpx;
}
.modal-score3.data-v-c3d5c24b {
  color: #dbb958;
  font-weight: 900;
  font-size: 20rpx;
}