<view class="login-container"><view class="status-bar" style="{{'height:' + a}}"></view><image class="background-img" src="{{b}}" mode="widthFix"></image><view class="login-content"><view wx:if="{{c}}" class="loading-section"><view class="loading-wrapper"><view class="logo-img"><image src="{{d}}" mode="aspectFit"></image></view><text class="loading-text">正在检查登录状态...</text></view></view><view wx:else><view class="logo-section"><view class="logo-wrapper"><view class="logo-img"><image src="{{e}}" mode="aspectFit"></image></view></view><text class="app-title">天大海棠创新创业生态</text></view><view class="permission-section"><view class="permission-item"><image src="{{f}}" mode="" class="permission-icon"></image><text class="permission-text">获取您的昵称、头像</text></view><view class="permission-item"><image src="{{g}}" mode="" class="permission-icon"></image><text class="permission-text">获取您的手机号（用于身份验证）</text></view><view class="permission-item"><image src="{{h}}" mode="" class="permission-icon"></image><text class="permission-text">为您提供个性化的创新创业服务</text></view></view><view wx:if="{{i}}" class="user-info-section"><view class="info-title">完善个人信息</view><view class="avatar-section"><view class="avatar-label">头像</view><button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="{{m}}" disabled="{{n}}"><image class="avatar-preview" src="{{j}}" mode="aspectFill"></image><view wx:if="{{k}}" class="avatar-tip">点击选择头像</view><view wx:else class="avatar-tip uploading">上传中...</view><view wx:if="{{l}}" class="upload-mask"></view></button></view><view class="nickname-section"><view class="nickname-label">昵称</view><input class="nickname-input" type="nickname" placeholder="请输入昵称" bindblur="{{o}}" value="{{p}}" bindinput="{{q}}"/></view></view><view class="login-section"><button class="wechat-login-btn" open-type="{{s}}" bindgetphonenumber="{{t}}" bindtap="{{v}}" disabled="{{w}}">{{r}}</button></view><view class="agreement-section"><checkbox-group bindchange="{{D}}"><label class="agreement-label" bindtap="{{C}}"><view class="{{['custom-checkbox', y && 'checked']}}" catchtap="{{z}}"><view wx:if="{{x}}" class="checkbox-inner"></view></view><text class="agreement-text">我已阅读并同意</text><text class="agreement-link" catchtap="{{A}}">《用户协议》</text><text class="agreement-text">与</text><text class="agreement-link" catchtap="{{B}}">《隐私协议》</text></label></checkbox-group></view><view style="height:60rpx"></view></view></view></view>