.star-detail-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f8fe;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.star-detail-content {
  position: relative;
}
/* 顶部图片区域 */
.top-image-section {
  width: 100%;
  position: relative;
}
.top-image {
  width: 100%;
}
.top-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.placeholder-text {
  color: #fff;
  font-size: 32rpx;
}
/* 创始人信息卡片 - 遮罩效果 */
.founder-card {
  background: rgba(1, 1, 1, 0.3);
  position: absolute;
  z-index: 10;
  left: 0;
  bottom: 10rpx;
  width: 100%;
}
.founder-info {
  padding: 10rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.founder-avatar-container {
  margin-right: 30rpx;
}
.founder-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
}
.founder-basic-info {
  flex: 1;
}
/* 浏览量显示 */
.view-count-container {
  display: flex;
  align-items: center;
}
.view-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  color: #fff;
}
.view-count {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}
.founder-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 5rpx;
}
.founder-company {
  font-size: 24rpx;
  color: #fff;
  display: block;
}
.founder-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
  display: block;
}
/* 内容区块样式 */
.content-section {
  background: #ffffff;
  padding: 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.content-section-son {
  margin: 10rpx 0 70rpx;
}
.section-header {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}
.section-subtitle {
  font-size: 24rpx;
  color: #999;
}
.section-content {
  line-height: 1.8;
}
/* 富文本内容样式优化 */
.rich-text-content {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}
/* 富文本内部图片样式限制 */
.rich-text-content img {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
  box-sizing: border-box !important;
  margin: 10rpx 0 !important;
}
/* 针对微信小程序的特殊处理 */
.section-content {
  overflow: hidden;
  max-width: 100%;
}
/* 企业信息样式 */
.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-icon {
  margin-right: 15rpx;
  width: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon-text {
  font-size: 32rpx;
}
.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.info-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}
.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
/* 联系方式区域 */
.contact-section {
  background: #ffffff;
  margin-top: 40rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 16rpx rgba(200, 213, 242, 0.3);
}
.contact-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1px solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.contact-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.contact-text {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}
