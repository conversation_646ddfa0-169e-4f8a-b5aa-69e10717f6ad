"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: 0,
      // 状态提示条
      statusBar: {
        show: false,
        type: "",
        // info, warning, success, error, pending
        text: "",
        action: "",
        actionType: ""
      },
      // 错误信息
      companyNameErrorMsg: "",
      form: {
        companyName: "",
        logo: "",
        companyDescription: "",
        region: "",
        founderSchool: "",
        industry: "",
        annualRevenue: "",
        companySize: "",
        companyType: "",
        qualifications: [],
        financingRound: "",
        productServices: [],
        productDescription: "",
        // 错误状态
        companyNameError: false,
        regionError: false,
        founderSchoolError: false,
        industryError: false,
        annualRevenueError: false,
        companySizeError: false,
        companyTypeError: false,
        financingRoundError: false
      },
      // 选择器数据
      regionList: [
        { name: "北京市", value: "beijing" },
        { name: "上海市", value: "shanghai" },
        { name: "天津市", value: "tianjin" },
        { name: "重庆市", value: "chongqing" },
        { name: "河北省", value: "hebei" },
        { name: "山西省", value: "shanxi" },
        { name: "内蒙古自治区", value: "neimenggu" },
        { name: "辽宁省", value: "liaoning" },
        { name: "吉林省", value: "jilin" },
        { name: "黑龙江省", value: "heilongjiang" },
        { name: "江苏省", value: "jiangsu" },
        { name: "浙江省", value: "zhejiang" },
        { name: "安徽省", value: "anhui" },
        { name: "福建省", value: "fujian" },
        { name: "江西省", value: "jiangxi" },
        { name: "山东省", value: "shandong" },
        { name: "河南省", value: "henan" },
        { name: "湖北省", value: "hubei" },
        { name: "湖南省", value: "hunan" },
        { name: "广东省", value: "guangdong" },
        { name: "广西壮族自治区", value: "guangxi" },
        { name: "海南省", value: "hainan" },
        { name: "四川省", value: "sichuan" },
        { name: "贵州省", value: "guizhou" },
        { name: "云南省", value: "yunnan" },
        { name: "西藏自治区", value: "xizang" },
        { name: "陕西省", value: "shaanxi" },
        { name: "甘肃省", value: "gansu" },
        { name: "青海省", value: "qinghai" },
        { name: "宁夏回族自治区", value: "ningxia" },
        { name: "新疆维吾尔自治区", value: "xinjiang" },
        { name: "香港特别行政区", value: "hongkong" },
        { name: "澳门特别行政区", value: "macao" },
        { name: "台湾省", value: "taiwan" }
      ],
      industryList: [],
      companySizeList: [
        { name: "1-10人", value: "1-10" },
        { name: "11-50人", value: "11-50" },
        { name: "51-200人", value: "51-200" },
        { name: "201-500人", value: "201-500" },
        { name: "500人以上", value: "500+" }
      ],
      companyTypeList: [],
      financingRoundList: [
        { name: "种子轮", value: "seed" },
        { name: "Pre-A轮", value: "pre_a" },
        { name: "A轮", value: "a_round" },
        { name: "B轮", value: "b_round" },
        { name: "C轮", value: "c_round" },
        { name: "D轮及以后", value: "d_plus" },
        { name: "已上市", value: "ipo" },
        { name: "不需要融资", value: "no_need" }
      ],
      // 选中的选择器项
      selectedRegion: null,
      selectedIndustry: null,
      selectedCompanySize: null,
      selectedCompanyType: null,
      selectedFinancingRound: null,
      // 多选项数据
      qualificationOptions: [
        { label: "科技型中小企业", value: "科技型中小企业" },
        { label: "高新技术企业", value: "高新技术企业" },
        { label: "创新型中小企业", value: "创新型中小企业" },
        { label: "专精特新企业", value: "专精特新企业" },
        { label: '专精特新"小巨人"企业', value: '专精特新"小巨人"企业' },
        { label: "制造业单项冠军", value: "制造业单项冠军" },
        { label: "拟IPO公司", value: "拟IPO公司" },
        { label: "上市公司", value: "上市公司" },
        { label: "其他", value: "其他" }
      ],
      productServiceOptions: [],
      // 行业选择器所需（与个人资料页一致）
      showIndustryModal: false,
      industryData: [],
      selectedIndustryData: { level1: [], level2: [], level3: [] },
      tempSelectedIndustry: { level1: [], level2: [], level3: [] },
      // 企业信息审核状态
      enterpriseStatus: null
      // 用于控制提交按钮状态
    };
  },
  computed: {
    canSubmit() {
      const formComplete = !!(this.form.companyName && this.form.region && this.form.founderSchool && this.form.industry && this.form.annualRevenue && this.form.companySize && this.form.companyType && this.form.financingRound);
      const canSubmitByStatus = true;
      return formComplete && canSubmitByStatus;
    },
    // 提交按钮文本
    submitButtonText() {
      if (!this.enterpriseStatus || this.enterpriseStatus === "not_submitted") {
        return "提交信息";
      } else if (this.enterpriseStatus === "pending") {
        return "重新提交";
      } else if (this.enterpriseStatus === "rejected") {
        return "重新提交";
      } else if (this.enterpriseStatus === "approved") {
        return "更新信息";
      } else {
        return "提交信息";
      }
    },
    // 选中的行业显示文本（与个人资料页一致）
    selectedIndustryText() {
      if (this.selectedIndustryData.level3 && this.selectedIndustryData.level3.length > 0) {
        const l1n = this.selectedIndustryData.level1.length;
        const l2n = this.selectedIndustryData.level2.length;
        const l3n = this.selectedIndustryData.level3.length;
        if (l1n <= 2 && l2n <= 2 && l3n <= 3) {
          const t1 = this.selectedIndustryData.level1.map((i) => i.name).join("、");
          const t2 = this.selectedIndustryData.level2.map((i) => i.name).join("、");
          const t3 = this.selectedIndustryData.level3.map((i) => i.name).join("、");
          const full = `${t1} > ${t2} > ${t3}`;
          return full.length > 30 ? `已选择${l1n}个一级分类、${l2n}个二级分类、${l3n}个细分领域` : full;
        }
        return `已选择${l1n}个一级分类、${l2n}个二级分类、${l3n}个细分领域`;
      }
      return "";
    }
  },
  onLoad() {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.checkUserProfileStatus();
    Promise.all([
      this.loadCompanyTypes(),
      this.loadIndustryData(),
      this.loadProductServices()
    ]).then(() => {
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:489", "🏢 所有基础数据加载完成，开始根据状态回显表单数据");
      this.loadFormDataBasedOnStatus();
    }).catch((error) => {
      common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:493", "🏢 基础数据加载失败:", error);
      this.loadFormDataBasedOnStatus();
    });
  },
  methods: {
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 表单验证方法
    validateCompanyName() {
      const company = this.form.companyName ? this.form.companyName.trim() : "";
      if (!company) {
        this.form.companyNameError = true;
        this.companyNameErrorMsg = "请输入企业名称";
        return;
      }
      const companyValidation = this.validateCompanyNameFormat(company);
      if (companyValidation) {
        this.form.companyNameError = true;
        this.companyNameErrorMsg = companyValidation;
        return;
      }
      this.form.companyNameError = false;
      this.companyNameErrorMsg = "";
    },
    // 验证企业名称格式（按照中国企业全称规范）
    validateCompanyNameFormat(company) {
      if (!company) {
        return "";
      }
      company = company.trim();
      if (company.length < 5) {
        return "企业全称至少需要5个字符";
      }
      if (company.length > 80) {
        return "企业名称不得超过80个字符";
      }
      const companyRegex = /^[\u4e00-\u9fa5a-zA-Z0-9\(\)（）]+$/;
      if (!companyRegex.test(company)) {
        return "企业名称只能包含中文、英文、数字和括号";
      }
      const validOrganizationForms = [
        // 有限责任公司
        "有限责任公司",
        "有限公司",
        // 股份有限公司
        "股份有限公司",
        "股份公司",
        // 个人独资企业
        "个人独资企业",
        // 合伙企业
        "合伙企业",
        "普通合伙企业",
        "有限合伙企业",
        // 个体工商户
        "个体工商户",
        "个体户",
        // 外商投资企业
        "外商独资企业",
        "中外合资企业",
        "中外合作企业",
        // 集团公司
        "集团有限公司",
        "集团股份有限公司",
        "集团公司",
        // 其他特殊形式
        "事务所",
        "工作室",
        "中心",
        "研究院",
        "研究所"
      ];
      const hasValidOrganizationForm = validOrganizationForms.some(
        (form) => company.endsWith(form)
      );
      if (!hasValidOrganizationForm) {
        return "企业名称必须包含正确的组织形式，如：有限公司、股份有限公司等";
      }
      let remainingName = company;
      for (const form of validOrganizationForms) {
        if (company.endsWith(form)) {
          remainingName = company.substring(0, company.length - form.length);
          break;
        }
      }
      if (remainingName.length < 2) {
        return "企业字号部分过短，请输入完整的企业名称";
      }
      return "";
    },
    // 企业名称输入处理
    onCompanyNameInput(e) {
      const company = e.detail.value;
      this.form.companyName = company;
      if (company.trim()) {
        this.companyNameErrorMsg = this.validateCompanyNameFormat(company.trim());
        this.form.companyNameError = !!this.companyNameErrorMsg;
      } else {
        this.companyNameErrorMsg = "";
        this.form.companyNameError = false;
      }
      this.checkFormCompletion();
    },
    validateFounderSchool() {
      if (!this.form.founderSchool || this.form.founderSchool.trim() === "") {
        this.form.founderSchoolError = true;
      } else {
        this.form.founderSchoolError = false;
      }
    },
    validateAnnualRevenue() {
      if (!this.form.annualRevenue || this.form.annualRevenue.trim() === "") {
        this.form.annualRevenueError = true;
      } else {
        this.form.annualRevenueError = false;
      }
    },
    // 检查表单完成状态
    checkFormCompletion() {
    },
    // 检查用户企业信息状态
    async checkUserProfileStatus() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:641", "👤 开始检查用户企业信息状态...");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:642", "👤 API地址: GET /miniapp/unified-enterprise/user/status");
        common_vendor.index.showLoading({
          title: "检查状态中...",
          mask: true
        });
        const response = await utils_request.request.get("/miniapp/unified-enterprise/user/status");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:651", "👤 用户状态API响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const statusData = response.data || {};
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:658", "👤 状态数据详情:", statusData);
          this.setStatusBar(statusData);
        } else {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:663", "👤 获取用户状态失败:", response);
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:669", "👤 检查用户状态异常:", error);
      }
    },
    // 设置状态提示条
    setStatusBar(statusData) {
      const status = statusData.status;
      const statusName = statusData.statusName || "";
      const statusDescription = statusData.statusDescription || "";
      const needCompleteInfo = statusData.needCompleteInfo || false;
      this.enterpriseStatus = status;
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:684", "👤 企业信息状态:", status, statusName, statusDescription);
      if (this.industryData && this.industryData.length > 0) {
        this.loadFormDataBasedOnStatus();
      }
      switch (status) {
        case "info_incomplete":
          this.statusBar = {
            show: true,
            type: "warning",
            text: "请先完善个人资料信息，才能填写企业信息",
            action: "去完善",
            actionType: "profile"
          };
          break;
        case "not_submitted":
          if (needCompleteInfo) {
            this.statusBar = {
              show: true,
              type: "warning",
              text: "请先完善个人资料信息，才能填写企业信息",
              action: "去完善",
              actionType: "profile"
            };
          } else {
            this.statusBar = {
              show: true,
              type: "info",
              text: "请填写完整的企业信息并提交申请",
              action: "",
              actionType: ""
            };
          }
          break;
        case "pending":
          this.statusBar = {
            show: true,
            type: "pending",
            text: "您的企业信息申请正在审核中，请耐心等待",
            action: "",
            actionType: ""
          };
          break;
        case "approved":
          this.statusBar = {
            show: true,
            type: "success",
            text: "恭喜！您的企业信息已审核通过",
            action: "",
            actionType: ""
          };
          break;
        case "rejected":
          this.statusBar = {
            show: true,
            type: "error",
            text: "您的企业信息申请未通过审核，请重新完善信息后提交",
            action: "",
            actionType: ""
          };
          break;
        default:
          this.statusBar = {
            show: false,
            type: "",
            text: "",
            action: "",
            actionType: ""
          };
          break;
      }
    },
    // 状态条操作
    handleStatusAction() {
      if (this.statusBar.actionType === "profile") {
        common_vendor.index.redirectTo({
          url: "/pages/index/profile?from=company"
        });
      }
    },
    // 加载企业类型数据
    async loadCompanyTypes() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:784", "🏢 开始获取企业类型数据...");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:785", "🏢 API地址: GET /miniapp/resourceCatalog/listByType/enterprise_type");
        const response = await utils_request.request.get("/miniapp/resourceCatalog/listByType/enterprise_type");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:788", "🏢 企业类型API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const typeData = response.data.data || [];
          this.companyTypeList = typeData.map((item) => ({
            name: item.resourceName,
            value: item.resourceName
          }));
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:796", "🏢 企业类型数据处理完成:", this.companyTypeList);
        } else {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:798", "🏢 获取企业类型失败:", response);
          common_vendor.index.showToast({
            title: "获取企业类型失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:805", "🏢 获取企业类型异常:", error);
        common_vendor.index.showToast({
          title: "获取企业类型失败",
          icon: "none"
        });
      }
    },
    // 加载行业树（与个人资料页一致）
    async loadIndustryData() {
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:815", "开始加载行业数据");
      try {
        const result = await utils_request.request.get("/miniapp/industry/tree");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:818", "行业数据响应:", result);
        if (result && result.data && result.data.code === 200) {
          const industryTree = result.data.data || [];
          this.industryData = this.formatIndustryData(industryTree);
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:822", "行业数据加载成功:", this.industryData);
          return true;
        } else {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:825", "行业数据加载失败:", result);
          return false;
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:829", "加载行业数据失败:", e);
        return false;
      }
    },
    // 按个人资料页逻辑格式化行业树
    formatIndustryData(treeData) {
      return (treeData || []).map((level1) => {
        const level1Item = {
          id: level1.id,
          name: level1.nodeName,
          hasStreamType: level1.hasStreamType,
          children: []
        };
        (level1.children || []).forEach((level2) => {
          const level2Item = {
            id: level2.id,
            name: level2.nodeName,
            parentId: level2.parentId,
            streamType: level2.streamType,
            children: []
          };
          (level2.children || []).forEach((level3) => {
            level2Item.children.push({
              id: level3.id,
              name: level3.nodeName,
              parentId: level3.parentId
            });
          });
          level1Item.children.push(level2Item);
        });
        return level1Item;
      });
    },
    // 加载主营产品/服务数据
    async loadProductServices() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:867", "📦 开始获取主营产品数据...");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:868", "📦 API地址: GET /miniapp/resourceCatalog/listByType/product_type");
        const response = await utils_request.request.get("/miniapp/resourceCatalog/listByType/product_type");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:871", "📦 主营产品API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const productData = response.data.data || [];
          this.productServiceOptions = productData.map((item) => ({
            label: item.resourceName,
            value: item.resourceName
          }));
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:879", "📦 主营产品数据处理完成:", this.productServiceOptions);
        } else {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:881", "📦 获取主营产品失败:", response);
          common_vendor.index.showToast({
            title: "获取主营产品失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:888", "📦 获取主营产品异常:", error);
        common_vendor.index.showToast({
          title: "获取主营产品失败",
          icon: "none"
        });
      }
    },
    // 上传logo
    uploadLogo() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        // 压缩图
        sourceType: ["album", "camera"],
        // 从相册选择或拍照
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:904", "选择企业logo成功:", tempFilePath);
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          utils_request.request.upload("/common/upload", tempFilePath, "file").then((result) => {
            if (result.success) {
              this.form.logo = result.url;
              this.checkFormCompletion();
              common_vendor.index.showToast({
                title: "上传成功",
                icon: "success"
              });
              common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:923", "企业logo上传成功，URL:", result.url);
            } else {
              common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:925", "上传失败:", result.message);
              common_vendor.index.showToast({
                title: result.message || "上传失败",
                icon: "none"
              });
            }
          }).catch((error) => {
            common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:933", "上传失败:", error);
            common_vendor.index.showToast({
              title: "上传失败，请重试",
              icon: "none"
            });
          }).finally(() => {
            common_vendor.index.hideLoading();
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:944", "选择图片失败:", error);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 地区选择
    onRegionChange(e) {
      const selectedIndex = e.detail.value;
      this.selectedRegion = this.regionList[selectedIndex];
      this.form.region = this.selectedRegion.value;
      this.form.regionError = false;
      this.checkFormCompletion();
    },
    // 与个人资料页一致：显示行业弹窗
    showIndustryPicker() {
      this.tempSelectedIndustry = {
        level1: [...this.selectedIndustryData.level1],
        level2: [...this.selectedIndustryData.level2],
        level3: [...this.selectedIndustryData.level3]
      };
      this.initTreeExpandState();
      this.expandSelectedParents();
      this.showIndustryModal = true;
    },
    hideIndustryPicker() {
      this.showIndustryModal = false;
      if (!this.form.industry)
        this.validateIndustry();
    },
    // 树形选择器所需方法（对齐个人资料页）
    initTreeExpandState() {
      (this.industryData || []).forEach((level1) => {
        this.$set(level1, "expanded", false);
        (level1.children || []).forEach((level2) => this.$set(level2, "expanded", false));
      });
    },
    expandSelectedParents() {
      if (!this.tempSelectedIndustry.level3 || this.tempSelectedIndustry.level3.length === 0)
        return;
      const selectedLevel3Ids = this.tempSelectedIndustry.level3.map((item) => item.id);
      (this.industryData || []).forEach((level1) => {
        let hasSelectedInLevel1 = false;
        (level1.children || []).forEach((level2) => {
          const hasSelected = (level2.children || []).some((l3) => selectedLevel3Ids.includes(l3.id));
          if (hasSelected) {
            hasSelectedInLevel1 = true;
            this.$set(level2, "expanded", true);
          }
        });
        if (hasSelectedInLevel1)
          this.$set(level1, "expanded", true);
      });
    },
    toggleLevel1(level1) {
      if (this.canSelectLevel1(level1)) {
        this.$set(level1, "expanded", !level1.expanded);
      }
    },
    canSelectLevel1(level1) {
      return level1.children && level1.children.length > 0;
    },
    getLevel2ByStream(level1, streamLabel) {
      if (!level1.children)
        return [];
      const streamTypeMap = { "上游": "upstream", "中游": "midstream", "下游": "downstream" };
      return (level1.children || []).filter((l2) => l2.streamType === streamTypeMap[streamLabel]);
    },
    canSelectLevel2(level2) {
      return level2.children && level2.children.length > 0;
    },
    toggleLevel2(level2) {
      if (this.canSelectLevel2(level2)) {
        this.$set(level2, "expanded", !level2.expanded);
      }
    },
    isLevel3Selected(level3) {
      return this.tempSelectedIndustry.level3.some((s) => s.id === level3.id);
    },
    selectLevel1(item) {
      if (!this.canSelectLevel1(item)) {
        common_vendor.index.showToast({ title: "请选择具体的行业分类", icon: "none" });
        return;
      }
      const isSelected = this.isLevel1Selected(item);
      if (isSelected) {
        this.removeChildSelections(item.id, "level1");
      } else {
        this.selectAllChildren(item, "level1");
      }
      this.updateParentSelections();
    },
    isLevel1Selected(level1) {
      if (!level1.children || level1.children.length === 0)
        return false;
      return level1.children.some((level2) => this.isLevel2Selected(level2));
    },
    selectLevel2(item) {
      if (!this.canSelectLevel2(item)) {
        common_vendor.index.showToast({ title: "请选择具体的行业分类", icon: "none" });
        return;
      }
      const isSelected = this.isLevel2Selected(item);
      if (isSelected) {
        this.removeChildSelections(item.id, "level2");
      } else {
        this.selectAllChildren(item, "level2");
      }
      this.updateParentSelections();
    },
    isLevel2Selected(level2) {
      if (!level2.children || level2.children.length === 0)
        return false;
      return level2.children.some((level3) => this.isLevel3Selected(level3));
    },
    selectLevel3(level3) {
      const arr = this.tempSelectedIndustry.level3;
      const idx = arr.findIndex((s) => s.id === level3.id);
      if (idx > -1)
        arr.splice(idx, 1);
      else
        arr.push({ id: level3.id, name: level3.name, parentId: level3.parentId });
      this.updateParentSelections();
    },
    // 展开全部子级（展示用）
    expandAllChildren(item, level) {
      if (level === "level1") {
        this.$set(item, "expanded", true);
        const fullLevel1 = (this.industryData || []).find((l1) => l1.id === item.id);
        ((fullLevel1 == null ? void 0 : fullLevel1.children) || []).forEach((l2) => this.$set(l2, "expanded", true));
      } else if (level === "level2") {
        this.$set(item, "expanded", true);
      }
    },
    // 选择/取消选择的工具
    removeChildSelections(parentId, parentLevel) {
      if (parentLevel === "level1") {
        this.tempSelectedIndustry.level3 = this.tempSelectedIndustry.level3.filter((level3) => {
          const level1Item = (this.industryData || []).find((l1) => l1.id === parentId);
          return !((level1Item == null ? void 0 : level1Item.children) || []).some((l2) => (l2.children || []).some((l3) => l3.id === level3.id));
        });
      } else if (parentLevel === "level2") {
        this.tempSelectedIndustry.level3 = this.tempSelectedIndustry.level3.filter((level3) => level3.parentId !== parentId);
      }
    },
    selectAllChildren(item, level) {
      if (level === "level1") {
        const fullLevel1 = (this.industryData || []).find((l1) => l1.id === item.id);
        const newLevel3 = [];
        ((fullLevel1 == null ? void 0 : fullLevel1.children) || []).forEach((l2) => (l2.children || []).forEach((l3) => newLevel3.push({ id: l3.id, name: l3.name, parentId: l3.parentId })));
        const map = new Map(this.tempSelectedIndustry.level3.map((i) => [i.id, i]));
        newLevel3.forEach((i) => map.set(i.id, i));
        this.tempSelectedIndustry.level3 = Array.from(map.values());
      } else if (level === "level2") {
        const fullLevel2 = this.findLevel2ById(item.id);
        const newLevel3 = ((fullLevel2 == null ? void 0 : fullLevel2.children) || []).map((l3) => ({ id: l3.id, name: l3.name, parentId: l3.parentId }));
        const map = new Map(this.tempSelectedIndustry.level3.map((i) => [i.id, i]));
        newLevel3.forEach((i) => map.set(i.id, i));
        this.tempSelectedIndustry.level3 = Array.from(map.values());
      }
    },
    updateParentSelections() {
      const selectedLevel3Ids = this.tempSelectedIndustry.level3.map((i) => i.id);
      const level2Set = /* @__PURE__ */ new Set();
      const level1Set = /* @__PURE__ */ new Set();
      (this.industryData || []).forEach((l1) => {
        (l1.children || []).forEach((l2) => {
          const has = (l2.children || []).some((l3) => selectedLevel3Ids.includes(l3.id));
          if (has) {
            level2Set.add(l2.id);
            level1Set.add(l1.id);
          }
        });
      });
      this.tempSelectedIndustry.level2 = [...level2Set].map((id) => this.findLevel2ById(id)).filter(Boolean).map((l2) => ({ id: l2.id, name: l2.name, parentId: l2.parentId }));
      this.tempSelectedIndustry.level1 = [...level1Set].map((id) => this.findLevel1ById(id)).filter(Boolean).map((l1) => ({ id: l1.id, name: l1.name }));
    },
    findLevel1ById(id) {
      return (this.industryData || []).find((l1) => l1.id === id);
    },
    findLevel2ById(id) {
      for (let l1 of this.industryData || []) {
        const f = (l1.children || []).find((l2) => l2.id === id);
        if (f)
          return f;
      }
      return null;
    },
    confirmIndustrySelection() {
      if (this.tempSelectedIndustry.level3 && this.tempSelectedIndustry.level3.length > 0) {
        this.selectedIndustryData = {
          level1: [...this.tempSelectedIndustry.level1],
          level2: [...this.tempSelectedIndustry.level2],
          level3: [...this.tempSelectedIndustry.level3]
        };
        this.form.industry = this.tempSelectedIndustry.level3.map((i) => i.id).join(",");
        this.form.industryError = false;
        this.checkFormCompletion();
        this.hideIndustryPicker();
      } else {
        common_vendor.index.showToast({ title: "请至少选择一个行业", icon: "none" });
      }
    },
    // 根据企业状态决定回显逻辑
    loadFormDataBasedOnStatus() {
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1164", "🏢 根据企业状态决定回显逻辑, 当前状态:", this.enterpriseStatus);
      if (!this.enterpriseStatus || this.enterpriseStatus === "not_submitted") {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1168", "🏢 企业未提交，从个人资料回显行业信息");
        this.loadPersonalIndustryForCompanyPage();
      } else {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1172", "🏢 企业已提交，从企业申请详情回显表单数据");
        this.loadEnterpriseApplicationDetail();
      }
    },
    // 从个人资料页加载行业和企业名称回显（仅在未提交时使用）
    loadPersonalIndustryForCompanyPage() {
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1179", "🏢 从个人资料回显行业信息和企业名称（未提交状态）");
      utils_request.request.get("/miniapp/user/getProfileDetail").then((res) => {
        var _a;
        if (res && res.data && res.data.code === 200) {
          const careerInfo = ((_a = res.data.data) == null ? void 0 : _a.careerInfo) || {};
          const industryField = careerInfo.industryField || "";
          if (industryField) {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1187", "🏢 个人资料行业字段:", industryField);
            this.loadIndustrySelectionFromIds(industryField);
            this.form.industry = industryField;
          }
          const currentCompany = careerInfo.currentCompany || "";
          if (currentCompany) {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1195", "🏢 个人资料企业名称:", currentCompany);
            this.form.companyName = currentCompany;
            this.companyNameErrorMsg = "";
          }
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1201", "🏢 获取个人资料失败:", error);
      });
    },
    // 加载企业申请详情（已提交状态回显）
    async loadEnterpriseApplicationDetail() {
      try {
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1208", "🏢 开始加载企业申请详情...");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1209", "🏢 API地址: GET /miniapp/unified-enterprise/user/application-detail");
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        const response = await utils_request.request.get("/miniapp/unified-enterprise/user/application-detail");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1217", "🏢 企业申请详情响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          const responseData = response.data.data || {};
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1223", "🏢 企业申请响应数据:", responseData);
          this.fillFormWithApplicationData(responseData);
        } else {
          common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1228", "🏢 获取企业申请详情失败:", response);
          this.loadPersonalIndustryForCompanyPage();
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1234", "🏢 加载企业申请详情异常:", error);
        this.loadPersonalIndustryForCompanyPage();
      }
    },
    // 用企业申请数据填充表单
    fillFormWithApplicationData(responseData) {
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1242", "🏢 开始回显企业申请数据:", responseData);
      try {
        const enterprise = responseData.enterprise || {};
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1247", "🏢 企业详细信息:", enterprise);
        if (enterprise.enterpriseName) {
          this.form.companyName = enterprise.enterpriseName;
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1252", "🏢 回显企业名称:", enterprise.enterpriseName);
        }
        if (enterprise.logoUrl) {
          this.form.logo = enterprise.logoUrl;
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1257", "🏢 回显企业Logo:", enterprise.logoUrl);
        }
        if (enterprise.businessDescription) {
          this.form.companyDescription = enterprise.businessDescription;
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1262", "🏢 回显企业描述:", enterprise.businessDescription);
        }
        if (enterprise.location) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1267", "🏢 回显所在地区:", enterprise.location);
          const region = this.regionList.find(
            (r) => r.name === enterprise.location || r.name.includes(enterprise.location) || enterprise.location.includes(r.name)
          );
          if (region) {
            this.selectedRegion = region;
            this.form.region = region.value;
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1276", "🏢 匹配到地区:", region);
          } else {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1278", "🏢 未找到匹配的地区:", enterprise.location);
          }
        }
        if (enterprise.founderUniversity) {
          this.form.founderSchool = enterprise.founderUniversity;
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1285", "🏢 回显创始人院校:", enterprise.founderUniversity);
        }
        if (enterprise.industryList && Array.isArray(enterprise.industryList) && enterprise.industryList.length > 0) {
          const industryTreeIds = enterprise.industryList.map((item) => item.industryTreeId);
          const industryIdsStr = industryTreeIds.join(",");
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1292", "🏢 回显行业ID:", industryIdsStr, "详细列表:", enterprise.industryList);
          this.loadIndustrySelectionFromIds(industryIdsStr);
          this.form.industry = industryIdsStr;
        }
        if (enterprise.annualRevenue !== void 0 && enterprise.annualRevenue !== null) {
          this.form.annualRevenue = enterprise.annualRevenue.toString();
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1300", "🏢 回显年营收:", enterprise.annualRevenue);
        }
        if (enterprise.scale) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1305", "🏢 回显企业规模:", enterprise.scale);
          const companySize = this.companySizeList.find((s) => s.name === enterprise.scale);
          if (companySize) {
            this.selectedCompanySize = companySize;
            this.form.companySize = companySize.value;
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1310", "🏢 匹配到企业规模:", companySize);
          } else {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1312", "🏢 未找到匹配的企业规模:", enterprise.scale);
          }
        }
        if (enterprise.enterpriseType) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1318", "🏢 回显企业类型:", enterprise.enterpriseType);
          const companyType = this.companyTypeList.find((t) => t.name === enterprise.enterpriseType);
          if (companyType) {
            this.selectedCompanyType = companyType;
            this.form.companyType = companyType.value;
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1323", "🏢 匹配到企业类型:", companyType);
          } else {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1325", "🏢 未找到匹配的企业类型:", enterprise.enterpriseType);
          }
        }
        if (enterprise.qualifications) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1331", "🏢 回显企业资质:", enterprise.qualifications);
          const qualifications = enterprise.qualifications.split(",").filter((q) => q.trim());
          this.form.qualifications = qualifications;
        }
        if (enterprise.financingRound) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1338", "🏢 回显融资轮次:", enterprise.financingRound);
          const financingRound = this.financingRoundList.find((f) => f.name === enterprise.financingRound);
          if (financingRound) {
            this.selectedFinancingRound = financingRound;
            this.form.financingRound = financingRound.value;
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1343", "🏢 匹配到融资轮次:", financingRound);
          } else {
            common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1345", "🏢 未找到匹配的融资轮次:", enterprise.financingRound);
          }
        }
        if (enterprise.mainProductsServices) {
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1351", "🏢 回显主营产品/服务原始数据:", enterprise.mainProductsServices);
          const productServices = this.parseMainProductsServices(enterprise.mainProductsServices);
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1356", "🏢 解析后的主营产品数组:", productServices);
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1357", "🏢 主营产品数量:", productServices.length);
          this.form.productServices = productServices;
        }
        if (enterprise.additionalContent) {
          this.form.productDescription = enterprise.additionalContent;
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1365", "🏢 回显补充说明:", enterprise.additionalContent);
        }
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1368", "🏢 企业申请数据回显完成");
        this.clearAllErrors();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1374", "🏢 回显企业申请数据失败:", error);
      }
    },
    // 清除所有表单错误状态
    clearAllErrors() {
      this.form.companyNameError = false;
      this.form.regionError = false;
      this.form.founderSchoolError = false;
      this.form.industryError = false;
      this.form.annualRevenueError = false;
      this.form.companySizeError = false;
      this.form.companyTypeError = false;
      this.form.financingRoundError = false;
      this.companyNameErrorMsg = "";
    },
    loadIndustrySelectionFromIds(industryFieldStr) {
      if (!industryFieldStr || !this.industryData || this.industryData.length === 0)
        return;
      const ids = industryFieldStr.split(",").map((s) => parseInt(s.trim())).filter((n) => !isNaN(n));
      if (ids.length === 0)
        return;
      const selectedLevel1 = [], selectedLevel2 = [], selectedLevel3 = [];
      (this.industryData || []).forEach((l1) => {
        (l1.children || []).forEach((l2) => {
          (l2.children || []).forEach((l3) => {
            if (ids.includes(l3.id)) {
              selectedLevel3.push({ id: l3.id, name: l3.name, parentId: l3.parentId });
              if (!selectedLevel1.find((x) => x.id === l1.id))
                selectedLevel1.push({ id: l1.id, name: l1.name });
              if (!selectedLevel2.find((x) => x.id === l2.id))
                selectedLevel2.push({ id: l2.id, name: l2.name, parentId: l2.parentId });
            }
          });
        });
      });
      this.selectedIndustryData = { level1: selectedLevel1, level2: selectedLevel2, level3: selectedLevel3 };
    },
    validateIndustry() {
      this.form.industryError = !this.form.industry;
    },
    // 企业规模选择
    onCompanySizeChange(e) {
      const selectedIndex = e.detail.value;
      this.selectedCompanySize = this.companySizeList[selectedIndex];
      this.form.companySize = this.selectedCompanySize.name;
      this.form.companySizeError = false;
      this.checkFormCompletion();
    },
    // 企业类型选择
    onCompanyTypeChange(e) {
      const selectedIndex = e.detail.value;
      this.selectedCompanyType = this.companyTypeList[selectedIndex];
      this.form.companyType = this.selectedCompanyType.name;
      this.form.companyTypeError = false;
      this.checkFormCompletion();
    },
    // 融资轮次选择
    onFinancingRoundChange(e) {
      const selectedIndex = e.detail.value;
      this.selectedFinancingRound = this.financingRoundList[selectedIndex];
      this.form.financingRound = this.selectedFinancingRound.name;
      this.form.financingRoundError = false;
      this.checkFormCompletion();
    },
    // 切换企业资质选择
    toggleQualification(item) {
      const index = this.form.qualifications.indexOf(item.value);
      if (index > -1) {
        this.form.qualifications.splice(index, 1);
      } else {
        this.form.qualifications.push(item.value);
      }
      this.checkFormCompletion();
    },
    // 去掉“省/市”后缀
    cleanRegionName(name) {
      if (!name)
        return "";
      return name.replace(/(省|市)$/, "");
    },
    // 切换主营产品/服务选择
    toggleProductService(item) {
      const index = this.form.productServices.indexOf(item.value);
      if (index > -1) {
        this.form.productServices.splice(index, 1);
      } else {
        this.form.productServices.push(item.value);
      }
      this.checkFormCompletion();
    },
    // 解析复杂的主营产品字符串
    parseMainProductsServices(rawString) {
      if (!rawString || typeof rawString !== "string") {
        return [];
      }
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1477", "🔧 开始解析主营产品字符串:", rawString);
      try {
        let cleaned = rawString.trim();
        while (cleaned.startsWith('"') && cleaned.endsWith('"') || cleaned.startsWith("'") && cleaned.endsWith("'")) {
          cleaned = cleaned.slice(1, -1);
        }
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1489", "🔧 移除外层引号后:", cleaned);
        cleaned = cleaned.replace(/\\\\\\\\/g, "\\");
        cleaned = cleaned.replace(/\\\\/g, "\\");
        cleaned = cleaned.replace(/\\"/g, '"');
        cleaned = cleaned.replace(/\\'/g, "'");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1500", "🔧 处理转义字符后:", cleaned);
        let results = [];
        const simpleSplit = cleaned.split(",").map((s) => s.trim()).filter((s) => s && s !== "");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1507", "🔧 简单分割结果:", simpleSplit);
        const quotedPattern = /(?:[^",]|"[^"]*")+/g;
        const quotedSplit = cleaned.match(quotedPattern);
        if (quotedSplit) {
          const quotedCleaned = quotedSplit.map((s) => {
            let item = s.trim();
            if (item.startsWith('"') && item.endsWith('"') || item.startsWith("'") && item.endsWith("'")) {
              item = item.slice(1, -1);
            }
            return item;
          }).filter((s) => s && s !== "");
          common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1522", "🔧 引号分割结果:", quotedCleaned);
          if (quotedCleaned.length >= simpleSplit.length) {
            results = quotedCleaned;
          } else {
            results = simpleSplit;
          }
        } else {
          results = simpleSplit;
        }
        const uniqueResults = [...new Set(results)].filter((item) => item && item.trim() !== "");
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1537", "🔧 最终解析结果:", uniqueResults);
        return uniqueResults;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1541", "🔧 解析主营产品字符串失败:", error);
        return rawString.split(",").map((s) => s.trim()).filter((s) => s && s !== "");
      }
    },
    // 获取产品服务字符串（用于提交，逗号拼接）
    getProductServiceString() {
      return (this.form.productServices || []).join(",");
    },
    // 提交表单
    async submitForm() {
      var _a, _b;
      this.validateCompanyName();
      this.validateFounderSchool();
      this.validateAnnualRevenue();
      if (!this.form.region)
        this.form.regionError = true;
      if (!this.form.industry)
        this.form.industryError = true;
      if (!this.form.companySize)
        this.form.companySizeError = true;
      if (!this.form.companyType)
        this.form.companyTypeError = true;
      if (!this.form.financingRound)
        this.form.financingRoundError = true;
      const formComplete = !!(this.form.companyName && this.form.region && this.form.founderSchool && this.form.industry && this.form.annualRevenue && this.form.companySize && this.form.companyType && this.form.financingRound);
      if (!formComplete) {
        common_vendor.index.showToast({
          title: "请完善必填信息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1579", "🚀 当前企业状态:", this.enterpriseStatus, "允许提交");
      try {
        common_vendor.index.showLoading({
          title: "提交中..."
        });
        const industryIds = this.form.industry ? this.form.industry.split(",").map((s) => parseInt(s.trim())).filter((n) => !isNaN(n)) : [];
        const submitData = {
          enterpriseName: this.form.companyName,
          logoUrl: this.form.logo || "",
          businessDescription: this.form.companyDescription || "",
          location: this.selectedRegion ? this.cleanRegionName(this.selectedRegion.name) : "",
          founderUniversity: this.form.founderSchool,
          // industryTreeIds 传三级行业ID数组（与个人资料页一致的存储解析）
          industryTreeIds: industryIds,
          annualRevenue: parseFloat(this.form.annualRevenue) || 0,
          scale: this.selectedCompanySize ? this.selectedCompanySize.name : "",
          enterpriseType: this.selectedCompanyType ? this.selectedCompanyType.name : "",
          qualifications: this.form.qualifications.length > 0 ? this.form.qualifications.join(",") : "",
          financingRound: this.selectedFinancingRound ? this.selectedFinancingRound.name : "",
          // 主营产品/服务，中文并用逗号拼接
          mainProductsServices: this.getProductServiceString(),
          additionalContent: this.form.productDescription || ""
        };
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1608", "🚀 提交企业信息数据:", submitData);
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1609", "🚀 API地址: POST /miniapp/unified-enterprise/user/submit");
        const response = await utils_request.request.post("/miniapp/unified-enterprise/user/submit", submitData);
        common_vendor.index.__f__("log", "at pages/industry/company-profile.vue:1613", "🚀 企业信息提交API响应:", response);
        common_vendor.index.hideLoading();
        if (response && response.data && response.data.code === 200) {
          common_vendor.index.showModal({
            title: "提交成功",
            content: "您的企业信息已成功提交，我们会尽快审核处理",
            showCancel: false,
            confirmText: "确定",
            success: () => {
              common_vendor.index.navigateBack();
            }
          });
        } else {
          const errorMsg = ((_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg) || ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.message) || (response == null ? void 0 : response.message) || "提交失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/industry/company-profile.vue:1641", "🚀 提交企业信息异常:", error);
        common_vendor.index.showToast({
          title: "提交失败，请检查网络连接",
          icon: "none",
          duration: 2e3
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBar.show
  }, $data.statusBar.show ? common_vendor.e({
    b: common_vendor.t($data.statusBar.text),
    c: $data.statusBar.action
  }, $data.statusBar.action ? {
    d: common_vendor.t($data.statusBar.action),
    e: common_vendor.o((...args) => $options.handleStatusAction && $options.handleStatusAction(...args))
  } : {}, {
    f: common_vendor.n("status-" + $data.statusBar.type)
  }) : {}, {
    g: !$data.form.companyName || $data.form.companyName.trim() === "" || $data.form.companyNameError || $data.companyNameErrorMsg
  }, !$data.form.companyName || $data.form.companyName.trim() === "" || $data.form.companyNameError || $data.companyNameErrorMsg ? {} : {}, {
    h: $data.form.companyNameError ? 1 : "",
    i: common_vendor.o([($event) => $data.form.companyName = $event.detail.value, (...args) => $options.onCompanyNameInput && $options.onCompanyNameInput(...args)]),
    j: common_vendor.o((...args) => $options.validateCompanyName && $options.validateCompanyName(...args)),
    k: $data.form.companyName,
    l: $data.companyNameErrorMsg
  }, $data.companyNameErrorMsg ? {
    m: common_vendor.t($data.companyNameErrorMsg)
  } : {}, {
    n: $data.form.logo
  }, $data.form.logo ? {
    o: $data.form.logo
  } : {}, {
    p: common_vendor.o((...args) => $options.uploadLogo && $options.uploadLogo(...args)),
    q: common_vendor.o([($event) => $data.form.companyDescription = $event.detail.value, (...args) => $options.checkFormCompletion && $options.checkFormCompletion(...args)]),
    r: $data.form.companyDescription,
    s: common_vendor.t($data.form.companyDescription.length),
    t: !$data.form.region || $data.form.regionError
  }, !$data.form.region || $data.form.regionError ? {} : {}, {
    v: $data.selectedRegion
  }, $data.selectedRegion ? {
    w: common_vendor.t($options.cleanRegionName($data.selectedRegion.name))
  } : {}, {
    x: $data.form.regionError ? 1 : "",
    y: $data.regionList,
    z: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    A: !$data.form.founderSchool || $data.form.founderSchool.trim() === "" || $data.form.founderSchoolError
  }, !$data.form.founderSchool || $data.form.founderSchool.trim() === "" || $data.form.founderSchoolError ? {} : {}, {
    B: $data.form.founderSchoolError ? 1 : "",
    C: common_vendor.o([($event) => $data.form.founderSchool = $event.detail.value, (...args) => $options.checkFormCompletion && $options.checkFormCompletion(...args)]),
    D: common_vendor.o((...args) => $options.validateFounderSchool && $options.validateFounderSchool(...args)),
    E: $data.form.founderSchool,
    F: !$data.form.industry || $data.form.industryError
  }, !$data.form.industry || $data.form.industryError ? {} : {}, {
    G: $options.selectedIndustryText
  }, $options.selectedIndustryText ? {
    H: common_vendor.t($options.selectedIndustryText)
  } : {}, {
    I: $data.form.industryError ? 1 : "",
    J: common_vendor.o((...args) => $options.showIndustryPicker && $options.showIndustryPicker(...args)),
    K: $data.showIndustryModal
  }, $data.showIndustryModal ? {
    L: common_vendor.o((...args) => $options.hideIndustryPicker && $options.hideIndustryPicker(...args)),
    M: common_vendor.o((...args) => $options.confirmIndustrySelection && $options.confirmIndustrySelection(...args)),
    N: common_vendor.f($data.industryData, (level1, k0, i0) => {
      return common_vendor.e({
        a: $options.canSelectLevel1(level1)
      }, $options.canSelectLevel1(level1) ? {
        b: common_vendor.t(level1.expanded ? "▼" : "▶"),
        c: level1.expanded ? 1 : ""
      } : {}, {
        d: common_vendor.t(level1.name),
        e: !$options.canSelectLevel1(level1)
      }, !$options.canSelectLevel1(level1) ? {} : {}, {
        f: !$options.canSelectLevel1(level1) ? 1 : "",
        g: common_vendor.o(($event) => $options.toggleLevel1(level1), level1.id),
        h: level1.expanded
      }, level1.expanded ? common_vendor.e({
        i: level1.hasStreamType === "1"
      }, level1.hasStreamType === "1" ? {
        j: common_vendor.f(["上游", "中游", "下游"], (categoryName, k1, i1) => {
          return common_vendor.e({
            a: $options.getLevel2ByStream(level1, categoryName).length > 0
          }, $options.getLevel2ByStream(level1, categoryName).length > 0 ? {
            b: common_vendor.t(categoryName),
            c: common_vendor.f($options.getLevel2ByStream(level1, categoryName), (level2, k2, i2) => {
              return common_vendor.e({
                a: $options.canSelectLevel2(level2)
              }, $options.canSelectLevel2(level2) ? {
                b: common_vendor.t(level2.expanded ? "▼" : "▶"),
                c: level2.expanded ? 1 : ""
              } : {}, {
                d: common_vendor.t(level2.name),
                e: !$options.canSelectLevel2(level2)
              }, !$options.canSelectLevel2(level2) ? {} : {}, {
                f: !$options.canSelectLevel2(level2) ? 1 : "",
                g: common_vendor.o(($event) => $options.toggleLevel2(level2), level2.id),
                h: level2.expanded
              }, level2.expanded ? {
                i: common_vendor.f(level2.children, (level3, k3, i3) => {
                  return common_vendor.e({
                    a: common_vendor.t(level3.name),
                    b: $options.isLevel3Selected(level3)
                  }, $options.isLevel3Selected(level3) ? {} : {}, {
                    c: $options.isLevel3Selected(level3) ? 1 : "",
                    d: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                    e: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                    f: level3.id
                  });
                })
              } : {}, {
                j: level2.id
              });
            })
          } : {}, {
            d: categoryName
          });
        })
      } : {
        k: common_vendor.f(level1.children, (level2, k1, i1) => {
          return common_vendor.e({
            a: $options.canSelectLevel2(level2)
          }, $options.canSelectLevel2(level2) ? {
            b: common_vendor.t(level2.expanded ? "▼" : "▶"),
            c: level2.expanded ? 1 : ""
          } : {}, {
            d: common_vendor.t(level2.name),
            e: !$options.canSelectLevel2(level2)
          }, !$options.canSelectLevel2(level2) ? {} : {}, {
            f: !$options.canSelectLevel2(level2) ? 1 : "",
            g: common_vendor.o(($event) => $options.toggleLevel2(level2), level2.id),
            h: level2.expanded
          }, level2.expanded ? {
            i: common_vendor.f(level2.children, (level3, k2, i2) => {
              return common_vendor.e({
                a: common_vendor.t(level3.name),
                b: $options.isLevel3Selected(level3)
              }, $options.isLevel3Selected(level3) ? {} : {}, {
                c: $options.isLevel3Selected(level3) ? 1 : "",
                d: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                e: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                f: level3.id
              });
            })
          } : {}, {
            j: level2.id
          });
        })
      }) : {}, {
        l: level1.id
      });
    }),
    O: common_vendor.o(() => {
    }),
    P: common_vendor.o((...args) => $options.hideIndustryPicker && $options.hideIndustryPicker(...args))
  } : {}, {
    Q: !$data.form.annualRevenue || $data.form.annualRevenue.trim() === "" || $data.form.annualRevenueError
  }, !$data.form.annualRevenue || $data.form.annualRevenue.trim() === "" || $data.form.annualRevenueError ? {} : {}, {
    R: $data.form.annualRevenueError ? 1 : "",
    S: common_vendor.o([($event) => $data.form.annualRevenue = $event.detail.value, (...args) => $options.checkFormCompletion && $options.checkFormCompletion(...args)]),
    T: common_vendor.o((...args) => $options.validateAnnualRevenue && $options.validateAnnualRevenue(...args)),
    U: $data.form.annualRevenue,
    V: !$data.form.companySize || $data.form.companySizeError
  }, !$data.form.companySize || $data.form.companySizeError ? {} : {}, {
    W: $data.selectedCompanySize
  }, $data.selectedCompanySize ? {
    X: common_vendor.t($data.selectedCompanySize.name)
  } : {}, {
    Y: $data.form.companySizeError ? 1 : "",
    Z: $data.companySizeList,
    aa: common_vendor.o((...args) => $options.onCompanySizeChange && $options.onCompanySizeChange(...args)),
    ab: !$data.form.companyType || $data.form.companyTypeError
  }, !$data.form.companyType || $data.form.companyTypeError ? {} : {}, {
    ac: $data.selectedCompanyType
  }, $data.selectedCompanyType ? {
    ad: common_vendor.t($data.selectedCompanyType.name)
  } : {}, {
    ae: $data.form.companyTypeError ? 1 : "",
    af: $data.companyTypeList,
    ag: common_vendor.o((...args) => $options.onCompanyTypeChange && $options.onCompanyTypeChange(...args)),
    ah: common_vendor.f($data.qualificationOptions, (item, index, i0) => {
      return common_vendor.e({
        a: $data.form.qualifications.includes(item.value)
      }, $data.form.qualifications.includes(item.value) ? {} : {}, {
        b: $data.form.qualifications.includes(item.value) ? 1 : "",
        c: common_vendor.t(item.label),
        d: index,
        e: common_vendor.o(($event) => $options.toggleQualification(item), index)
      });
    }),
    ai: !$data.form.financingRound || $data.form.financingRoundError
  }, !$data.form.financingRound || $data.form.financingRoundError ? {} : {}, {
    aj: $data.selectedFinancingRound
  }, $data.selectedFinancingRound ? {
    ak: common_vendor.t($data.selectedFinancingRound.name)
  } : {}, {
    al: $data.form.financingRoundError ? 1 : "",
    am: $data.financingRoundList,
    an: common_vendor.o((...args) => $options.onFinancingRoundChange && $options.onFinancingRoundChange(...args)),
    ao: common_vendor.f($data.productServiceOptions, (item, index, i0) => {
      return common_vendor.e({
        a: $data.form.productServices.includes(item.value)
      }, $data.form.productServices.includes(item.value) ? {} : {}, {
        b: $data.form.productServices.includes(item.value) ? 1 : "",
        c: common_vendor.t(item.label),
        d: index,
        e: common_vendor.o(($event) => $options.toggleProductService(item), index)
      });
    }),
    ap: common_vendor.o([($event) => $data.form.productDescription = $event.detail.value, (...args) => $options.checkFormCompletion && $options.checkFormCompletion(...args)]),
    aq: $data.form.productDescription,
    ar: common_vendor.t($data.form.productDescription.length),
    as: common_vendor.t($options.submitButtonText),
    at: !$options.canSubmit ? 1 : "",
    av: !$options.canSubmit,
    aw: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/industry/company-profile.js.map
