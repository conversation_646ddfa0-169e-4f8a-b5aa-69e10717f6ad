"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      videoList: [],
      // 视频列表
      loading: true
      // 加载状态
    };
  },
  onLoad() {
    this.getVideoList();
  },
  methods: {
    // 获取视频展播列表 - 使用与海棠杯页面相同的API
    async getVideoList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/more-videos.vue:73", "📹 开始获取视频展播列表...");
        common_vendor.index.__f__("log", "at pages/index/more-videos.vue:74", "📹 API地址: GET /miniapp/video/list");
        const response = await utils_request.request.get("/miniapp/video/list", { status: 0 });
        common_vendor.index.__f__("log", "at pages/index/more-videos.vue:77", "📹 视频列表完整响应:", JSON.stringify(response, null, 2));
        if (response && response.success && response.data && response.data.code === 200) {
          const videoData = response.data.rows || [];
          common_vendor.index.__f__("log", "at pages/index/more-videos.vue:81", "📹 原始视频数据:", videoData);
          this.videoList = videoData.map((item) => ({
            id: item.id,
            title: item.title || "暂无标题",
            desc: item.title || "暂无描述",
            // 如果没有描述字段，使用标题
            poster: item.videoUrl || "",
            // 视频封面图，如果没有可以使用默认图
            url: item.videoUrl || "",
            sortOrder: item.sortOrder || 0,
            status: item.status,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));
          common_vendor.index.__f__("log", "at pages/index/more-videos.vue:96", "📹 视频列表获取成功，共", this.videoList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/more-videos.vue:97", "📹 处理后的视频数据:", this.videoList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/more-videos.vue:99", "📹 ❌ 视频数据获取失败！");
          common_vendor.index.__f__("log", "at pages/index/more-videos.vue:100", "📹 响应详情:", response);
          this.videoList = [];
          if (response && response.message) {
            common_vendor.index.showToast({
              title: response.message,
              icon: "none",
              duration: 2e3
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-videos.vue:113", "📹 获取视频列表失败:", error);
        this.videoList = [];
        common_vendor.index.showToast({
          title: "视频列表加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.loading = false;
      }
    },
    // 播放视频
    playVideo(video) {
      common_vendor.index.__f__("log", "at pages/index/more-videos.vue:128", "📹 播放视频:", video);
      if (video.url) {
        common_vendor.index.previewMedia({
          sources: [{
            url: video.url,
            type: "video",
            poster: video.poster
          }],
          current: 0,
          success: function(res) {
            common_vendor.index.__f__("log", "at pages/index/more-videos.vue:140", "📹 视频播放成功");
          },
          fail: function(err) {
            common_vendor.index.__f__("error", "at pages/index/more-videos.vue:143", "📹 视频播放失败:", err);
            common_vendor.index.showToast({
              title: "视频播放失败",
              icon: "none"
            });
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "暂无视频链接",
          icon: "none"
        });
      }
    },
    // 视频加载错误处理
    onVideoError(e) {
      common_vendor.index.__f__("log", "at pages/index/more-videos.vue:160", "📹 视频加载失败:", e);
    },
    // 格式化时间显示
    formatTime(timeStr) {
      if (!timeStr)
        return "暂无时间";
      try {
        const time = new Date(timeStr);
        const now = /* @__PURE__ */ new Date();
        const diff = now - time;
        const minutes = Math.floor(diff / (1e3 * 60));
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
        const months = Math.floor(diff / (1e3 * 60 * 60 * 24 * 30));
        if (minutes < 60) {
          return minutes <= 0 ? "刚刚" : `${minutes}分钟前`;
        } else if (hours < 24) {
          return `${hours}小时前`;
        } else if (days < 30) {
          return `${days}天前`;
        } else {
          return `${months}个月前`;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-videos.vue:188", "时间格式化失败:", error);
        return "时间格式错误";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.videoList && $data.videoList.length > 0
  }, $data.videoList && $data.videoList.length > 0 ? {
    b: common_vendor.f($data.videoList, (video, index, i0) => {
      return {
        a: video.url,
        b: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args), video.id || index),
        c: common_vendor.t(video.title),
        d: common_vendor.t(video.desc),
        e: common_vendor.t($options.formatTime(video.createdAt)),
        f: video.id || index,
        g: common_vendor.o(($event) => $options.playVideo(video), video.id || index)
      };
    })
  } : !$data.loading ? {} : {}, {
    c: !$data.loading
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/more-videos.js.map
