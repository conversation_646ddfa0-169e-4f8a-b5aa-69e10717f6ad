.contacts-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  /* 底部导航栏的高度 */
}
/* 顶部区域 */
.header {
  background: linear-gradient(270deg, #013fb0 0%, #002566 100%);
  padding: 40rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
}
.header image {
  position: absolute;
  width: 100%;
  top: 120rpx;
}
.search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 40rpx;
  position: relative;
  z-index: 10;
}
.search-input {
  flex: 1;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.search-input::-webkit-input-placeholder {
  color: #fff;
}
.search-input::placeholder {
  color: #fff;
}
.search-btn {
  width: 120rpx;
  height: 80rpx;
  background-color: #72a5ff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
}
/* 筛选条件区域 */
.filter-section {
  background: white;
  display: flex;
  padding: 10rpx 0rpx;
  margin: -30rpx 0rpx 10rpx;
  position: relative;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx #c7d6f2;
}
.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid #f0f0f0;
}
.filter-item:last-child {
  border-right: none;
}
.filter-picker {
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 40rpx;
  font-weight: 900;
}
.filter-arrow {
  font-size: 20rpx;
  margin-top: -2rpx;
}
/* 联系人列表 */
.contacts-list {
  padding: 20rpx;
}
/* 加载和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.contact-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
}
.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #003399;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}
.contact-info {
  flex: 1;
}
.contact-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}
.contact-details {
  display: flex;
  gap: 20rpx;
}
.detail-item {
  font-size: 26rpx;
  color: #666;
}
/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -6rpx 16rpx #c8d5f2;
  z-index: 100;
  padding-bottom: 20rpx;
}
.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  position: relative;
}
.tab-icon {
  width: 44rpx;
  height: 44rpx;
}
.tab-text {
  font-size: 22rpx;
  color: #444;
}
.tab-item.active .tab-text {
  color: #003399;
}
.tab-center-icon {
  width: 88rpx;
  height: 88rpx;
  background-color: #003399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  position: relative;
  top: -48rpx;
}
.tab-center-image {
  width: 60rpx;
  height: 60rpx;
}
.tab-center-text {
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.tab-center-text {
  font-size: 22rpx;
  color: #444;
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
