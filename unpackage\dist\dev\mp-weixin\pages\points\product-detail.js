"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      productId: "",
      productType: "",
      productImage: "",
      richTextContent: "",
      // 原始富文本内容
      processedRichTextContent: "",
      // 处理后的富文本内容
      loading: false,
      goodsDetail: null
      // 商品详情数据
    };
  },
  onLoad(options) {
    this.productId = options.id || "";
    this.productType = options.type || "";
    this.initProductData();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    // 处理服务器图片URL
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 初始化产品数据
    initProductData() {
      this.productImage = utils_imageUtils.getImagePath("product_default.jpg");
      this.getProductDetail();
    },
    // 获取产品详情
    async getProductDetail() {
      var _a, _b;
      try {
        this.loading = true;
        common_vendor.index.__f__("log", "at pages/points/product-detail.vue:87", "🎯 获取商品详情...", this.productId);
        const response = await utils_request.request.get(`/miniapp/points/goods/app/${this.productId}`);
        common_vendor.index.__f__("log", "at pages/points/product-detail.vue:90", "🎯 商品详情API响应:", response);
        if (response && response.data && response.data.code === 200) {
          this.goodsDetail = response.data.data;
          if (this.goodsDetail.productImageUrl) {
            this.productImage = utils_imageUtils.processServerImageUrl(this.goodsDetail.productImageUrl);
          }
          if (this.goodsDetail.goodsContent) {
            this.richTextContent = this.goodsDetail.goodsContent;
            this.processedRichTextContent = this.processRichTextContent(this.goodsDetail.goodsContent);
          }
        } else {
          common_vendor.index.__f__("log", "at pages/points/product-detail.vue:107", "🎯 获取商品详情失败:", (_a = response == null ? void 0 : response.data) == null ? void 0 : _a.msg);
          common_vendor.index.showToast({
            title: ((_b = response == null ? void 0 : response.data) == null ? void 0 : _b.msg) || "获取商品详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/product-detail.vue:115", "🎯 获取商品详情失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 处理富文本内容，确保图片样式正确
    processRichTextContent(htmlContent) {
      if (!htmlContent) {
        return "";
      }
      common_vendor.index.__f__("log", "at pages/points/product-detail.vue:131", "🛍️ 原始HTML内容:", htmlContent);
      let processedContent = htmlContent.replace(
        /<img([^>]*?)>/gi,
        '<img$1 style="max-width: 100% !important; width: auto !important; height: auto !important; display: block !important; margin: 10rpx 0 !important;">'
      );
      processedContent = processedContent.replace(
        /<img([^>]*?)style\s*=\s*["']([^"']*?)["']([^>]*?)>/gi,
        '<img$1style="$2; max-width: 100% !important; width: auto !important; height: auto !important; display: block !important; margin: 10rpx 0 !important;"$3>'
      );
      processedContent = utils_imageUtils.processHtmlImageUrls(processedContent);
      common_vendor.index.__f__("log", "at pages/points/product-detail.vue:148", "🛍️ 处理后的HTML内容:", processedContent);
      return processedContent;
    },
    // 客服咨询
    consultService() {
      common_vendor.index.showToast({
        title: "正在为您转接客服...",
        icon: "none"
      });
    },
    // 兑换产品
    exchangeProduct() {
      if (!this.goodsDetail) {
        common_vendor.index.showToast({
          title: "商品信息加载中，请稍后",
          icon: "none"
        });
        return;
      }
      if (this.goodsDetail.remainingStock <= 0) {
        common_vendor.index.showToast({
          title: "商品库存不足",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认兑换",
        content: `确定要使用${this.goodsDetail.pointsRequired}积分兑换"${this.goodsDetail.goodsName}"吗？`,
        success: (res) => {
          if (res.confirm) {
            this.doExchange();
          }
        }
      });
    },
    // 执行兑换
    async doExchange() {
      try {
        common_vendor.index.showToast({
          title: "兑换成功！",
          icon: "success"
        });
        setTimeout(() => {
          this.getProductDetail();
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/product-detail.vue:209", "🎯 兑换失败:", error);
        common_vendor.index.showToast({
          title: "兑换失败，请重试",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.productImage,
    b: $data.processedRichTextContent
  }, $data.processedRichTextContent ? {
    c: $data.processedRichTextContent
  } : $data.loading ? {} : {}, {
    d: $data.loading,
    e: $options.getImagePath("listen_icon.png"),
    f: common_vendor.o((...args) => $options.consultService && $options.consultService(...args)),
    g: $options.getImagePath("score_icon.png"),
    h: common_vendor.t($data.goodsDetail ? $data.goodsDetail.pointsRequired : "0"),
    i: common_vendor.o((...args) => $options.exchangeProduct && $options.exchangeProduct(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/product-detail.js.map
