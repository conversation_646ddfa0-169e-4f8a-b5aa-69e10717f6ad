<view class="competition-container"><view class="header-section" style="{{g}}"><view wx:if="{{a}}" class="header-image-container"><image src="{{b}}" mode="widthFix" class="header-image" binderror="{{c}}"></image></view><view wx:else class="header-content"><text class="main-title">{{d}}</text><text class="sub-title">{{e}}</text><text class="sub-title2">{{f}}</text></view></view><view class="cards-section"><view wx:for="{{h}}" wx:for-item="roadshow" wx:key="c" class="{{['competition-card', roadshow.d]}}" bindtap="{{roadshow.e}}"><image src="{{roadshow.a}}" mode="widthFix" style="width:100%;display:block" binderror="{{roadshow.b}}"></image></view><view wx:if="{{i}}" class="loading-container"><text class="loading-text">正在加载专区信息...</text></view></view></view>