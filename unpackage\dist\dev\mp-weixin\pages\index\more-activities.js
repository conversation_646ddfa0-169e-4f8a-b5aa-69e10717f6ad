"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      activityList: []
      // 活动列表
    };
  },
  onLoad() {
    this.getActivityList();
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 获取活动列表 - 使用与首页相同的API
    async getActivityList() {
      try {
        common_vendor.index.__f__("log", "at pages/index/more-activities.vue:52", "🎉 开始获取精彩活动列表...");
        common_vendor.index.__f__("log", "at pages/index/more-activities.vue:53", "🎉 API地址: POST /miniapp/activity/app/getEnabledList");
        const response = await utils_request.request.post("/miniapp/activity/app/getEnabledList");
        if (response && response.success && response.data && response.data.code === 200) {
          let activityData = response.data.data || [];
          common_vendor.index.__f__("log", "at pages/index/more-activities.vue:58", "🎉 原始精彩活动数据:", activityData);
          this.activityList = activityData.map((item) => ({
            id: item.id || item.activityId,
            title: item.title || item.name || "暂无标题",
            desc: item.description || item.content || item.summary || "暂无描述",
            time: item.createTime || item.publishTime || item.activityTime || "暂无时间",
            image: utils_imageUtils.processServerImageUrl(item.coverImage, utils_imageUtils.getImagePath("default-activity.png")),
            articleUrl: item.articleUrl
          }));
          common_vendor.index.__f__("log", "at pages/index/more-activities.vue:70", "🎉 精彩活动列表获取成功，共", this.activityList.length, "条数据");
          common_vendor.index.__f__("log", "at pages/index/more-activities.vue:71", "🎉 处理后的精彩活动数据:", this.activityList);
        } else {
          common_vendor.index.__f__("log", "at pages/index/more-activities.vue:73", "🎉 ❌ 精彩活动数据获取失败！");
          this.activityList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/more-activities.vue:77", "🎉 获取精彩活动列表失败:", error);
        this.activityList = [];
      }
    },
    // 查看活动详情
    viewActivityDetail(activity) {
      common_vendor.index.__f__("log", "at pages/index/more-activities.vue:84", "查看活动详情:", activity);
      if (activity.articleUrl) {
        if (activity.articleUrl.startsWith("http")) {
          this.openInWebview(activity.articleUrl, activity.title || "活动详情");
        } else if (activity.articleUrl.startsWith("/pages/")) {
          common_vendor.index.navigateTo({
            url: activity.articleUrl
          });
        }
      } else {
        common_vendor.index.showToast({
          title: `查看${activity.title}详情`,
          icon: "none"
        });
      }
    },
    // 在webview中打开链接
    openInWebview(url, title) {
      const encodedUrl = encodeURIComponent(url);
      const encodedTitle = encodeURIComponent(title);
      common_vendor.index.navigateTo({
        url: `/pages/webview/webview?url=${encodedUrl}&title=${encodedTitle}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/more-activities.vue:116", "跳转webview失败:", err);
          common_vendor.index.showToast({
            title: "打开链接失败",
            icon: "none"
          });
        }
      });
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/index/more-activities.vue:127", "图片加载失败:", e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.activityList && $data.activityList.length > 0
  }, $data.activityList && $data.activityList.length > 0 ? {
    b: common_vendor.f($data.activityList, (activity, index, i0) => {
      return {
        a: activity.image,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), activity.id || index),
        c: common_vendor.t(activity.title),
        d: common_vendor.t(activity.desc),
        e: activity.id || index,
        f: common_vendor.o(($event) => $options.viewActivityDetail(activity), activity.id || index)
      };
    })
  } : {
    c: $options.getImagePath("icon8.png")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/more-activities.js.map
