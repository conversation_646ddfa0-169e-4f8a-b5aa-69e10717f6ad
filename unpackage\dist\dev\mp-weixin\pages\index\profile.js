"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_profileCheck = require("../../utils/profileCheck.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const utils_shareMixin = require("../../utils/shareMixin.js");
const ImageCropper = () => "../../components/image-cropper/image-cropper.js";
const _sfc_main = {
  mixins: [utils_shareMixin.shareMixin],
  components: {
    ImageCropper
  },
  data() {
    return {
      statusBarHeight: 0,
      progress: 0,
      isFromMine: false,
      // 标识是否从mine页面进入
      isFromCompany: false,
      // 标识是否从企业信息页面进入
      isDataLoaded: false,
      // 标识数据是否已加载
      form: {
        name: "",
        gender: "",
        birthDate: "",
        nativePlace: "",
        phone: "",
        avatar: "",
        selfIntroduction: "",
        school: "",
        college: "",
        graduationYear: "",
        major: "",
        company: "",
        positionType: "",
        positionName: "",
        industry: "",
        // 错误状态字段
        nameError: false,
        birthDateError: false,
        nativePlaceError: false,
        phoneError: false,
        schoolError: false,
        majorError: false,
        companyError: false,
        industryError: false
      },
      // 模块完成状态
      moduleStatus: {
        basic: false,
        // 基础信息 50积分
        education: false,
        // 教育背景 50积分
        career: false
        // 职业信息 100积分
      },
      // 模块积分获取状态（是否已经获取过积分）
      modulePointsEarned: {
        basic: false,
        // 基础信息是否已获取积分
        education: false,
        // 教育背景是否已获取积分
        career: false
        // 职业信息是否已获取积分
      },
      // 积分配置
      modulePoints: {
        basic: 50,
        education: 50,
        career: 100
      },
      totalPoints: 200,
      // 省市数据
      selectedProvince: null,
      selectedCity: null,
      provinceList: [],
      cityList: [],
      phoneError: "",
      // 手机号验证错误信息
      companyError: "",
      // 企业名称验证错误信息
      graduationYears: [],
      positionTypeOptions: ["技术类", "产品类", "设计类", "市场类", "其他"],
      // 行业选择器相关
      showIndustryModal: false,
      currentStep: 1,
      // 当前选择步骤：1-一级分类，2-二级分类，3-细分领域
      tempSelectedIndustry: {
        level1: [],
        // 一级分类改为数组，支持多选
        level2: [],
        // 二级分类改为数组，支持多选
        level3: []
        // 三级分类改为数组，支持多选
      },
      selectedIndustryData: {
        level1: [],
        // 一级分类改为数组，支持多选
        level2: [],
        // 二级分类改为数组，支持多选
        level3: []
        // 三级分类改为数组，支持多选
      },
      // 三级行业数据
      industryData: [],
      // 图片裁剪相关
      showImageCropper: false,
      selectedImagePath: ""
    };
  },
  computed: {
    // 计算可获得积分（只计算未获取过积分的模块）
    earnedPoints() {
      let points = 0;
      Object.keys(this.moduleStatus).forEach((key) => {
        if (this.moduleStatus[key] && !this.modulePointsEarned[key]) {
          points += this.modulePoints[key];
        }
      });
      return points;
    },
    // 计算选中的行业文本
    selectedIndustryText() {
      if (this.selectedIndustryData.level3 && this.selectedIndustryData.level3.length > 0) {
        const level1Count = this.selectedIndustryData.level1.length;
        const level2Count = this.selectedIndustryData.level2.length;
        const level3Count = this.selectedIndustryData.level3.length;
        if (level1Count <= 2 && level2Count <= 2 && level3Count <= 3) {
          const level1Names = this.selectedIndustryData.level1.map((item) => item.name || item).join("、");
          const level2Names = this.selectedIndustryData.level2.map((item) => item.name || item).join("、");
          const level3Names = this.selectedIndustryData.level3.map((item) => item.name || item).join("、");
          const fullText = `${level1Names} > ${level2Names} > ${level3Names}`;
          if (fullText.length > 30) {
            return `已选择${level1Count}个一级分类、${level2Count}个二级分类、${level3Count}个细分领域`;
          }
          return fullText;
        } else {
          return `已选择${level1Count}个一级分类、${level2Count}个二级分类、${level3Count}个细分领域`;
        }
      }
      return "";
    },
    // 计算一级分类标签显示文本
    level1DisplayText() {
      if (!this.tempSelectedIndustry.level1 || this.tempSelectedIndustry.level1.length === 0) {
        return "一级分类";
      }
      const firstName = this.tempSelectedIndustry.level1[0].name;
      if (this.tempSelectedIndustry.level1.length === 1) {
        return firstName;
      } else {
        return `${firstName}等${this.tempSelectedIndustry.level1.length}项`;
      }
    },
    // 计算二级分类标签显示文本
    level2DisplayText() {
      if (!this.tempSelectedIndustry.level2 || this.tempSelectedIndustry.level2.length === 0) {
        return "二级分类";
      }
      const firstName = this.tempSelectedIndustry.level2[0].name;
      if (this.tempSelectedIndustry.level2.length === 1) {
        return firstName;
      } else {
        return `${firstName}等${this.tempSelectedIndustry.level2.length}项`;
      }
    },
    // 计算细分领域标签显示文本
    level3DisplayText() {
      if (!this.tempSelectedIndustry.level3 || this.tempSelectedIndustry.level3.length === 0) {
        return "细分领域";
      }
      const firstName = this.tempSelectedIndustry.level3[0].name;
      if (this.tempSelectedIndustry.level3.length === 1) {
        return firstName;
      } else {
        return `${firstName}等${this.tempSelectedIndustry.level3.length}项`;
      }
    },
    // 判断是否可以提交（所有必填项都填写完成）
    canSubmit() {
      const validation = this.checkRequiredFields();
      return validation.canSubmit;
    },
    // 当前一级分类是否有上中下游分类
    hasStreamType() {
      if (!this.tempSelectedIndustry.level1 || this.tempSelectedIndustry.level1.length === 0)
        return false;
      return this.tempSelectedIndustry.level1.some((selectedLevel1) => {
        const level1 = this.industryData.find((item) => item.id === selectedLevel1.id);
        return level1 && level1.hasStreamType === "1";
      });
    },
    // 当前二级选项
    currentLevel2Options() {
      if (!this.tempSelectedIndustry.level1 || this.tempSelectedIndustry.level1.length === 0)
        return [];
      const allLevel2Options = [];
      this.tempSelectedIndustry.level1.forEach((selectedLevel1) => {
        const level1 = this.industryData.find((item) => item.id === selectedLevel1.id);
        if (level1 && level1.children) {
          allLevel2Options.push(...level1.children);
        }
      });
      const uniqueLevel2Options = allLevel2Options.filter(
        (item, index, self) => self.findIndex((i) => i.id === item.id) === index
      );
      return uniqueLevel2Options;
    },
    // 当前三级选项
    currentLevel3Options() {
      if (!this.tempSelectedIndustry.level2 || this.tempSelectedIndustry.level2.length === 0)
        return [];
      const allLevel3Options = [];
      this.tempSelectedIndustry.level2.forEach((selectedLevel2) => {
        const level2 = this.currentLevel2Options.find((item) => item.id === selectedLevel2.id);
        if (level2 && level2.children) {
          allLevel3Options.push(...level2.children);
        }
      });
      const uniqueLevel3Options = allLevel3Options.filter(
        (item, index, self) => self.findIndex((i) => i.id === item.id) === index
      );
      return uniqueLevel3Options;
    },
    // 按上中下游分组的二级分类
    groupedLevel2Options() {
      const groups = {
        "上游": [],
        "中游": [],
        "下游": []
      };
      this.currentLevel2Options.forEach((item) => {
        if (item.category && groups[item.category]) {
          groups[item.category].push(item);
        }
      });
      return groups;
    }
  },
  watch: {
    // 监听表单数据变化，更新进度条
    form: {
      handler() {
        this.checkModuleCompletion();
      },
      deep: true
    }
  },
  onLoad(options) {
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
    this.isFromMine = options && options.from === "mine";
    this.isFromCompany = options && options.from === "company";
    common_vendor.index.__f__("log", "at pages/index/profile.vue:626", "个人资料页面 - 是否从mine页面进入:", this.isFromMine);
    common_vendor.index.__f__("log", "at pages/index/profile.vue:627", "个人资料页面 - 是否从企业信息页面进入:", this.isFromCompany);
    const token = common_vendor.index.getStorageSync("token");
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    common_vendor.index.__f__("log", "at pages/index/profile.vue:632", "个人资料页面 - 获取到的token:", token);
    common_vendor.index.__f__("log", "at pages/index/profile.vue:633", "个人资料页面 - 获取到的userInfo:", userInfo);
    common_vendor.index.__f__("log", "at pages/index/profile.vue:634", "个人资料页面 - token类型:", typeof token);
    this.generateYearOptions();
    this.initProvinceData();
    this.loadJobTypes();
    this.loadIndustryData().then(() => {
      if (this.isFromMine || this.isFromCompany) {
        this.loadUserProfileFromServer();
      } else {
        this.loadPhoneNumber();
        this.checkModuleCompletion();
      }
    }).catch(() => {
      if (this.isFromMine || this.isFromCompany) {
        this.loadUserProfileFromServer();
      } else {
        this.loadPhoneNumber();
        this.checkModuleCompletion();
      }
    });
  },
  methods: {
    // 获取图片路径
    getImagePath: utils_imageUtils.getImagePath,
    processServerImageUrl: utils_imageUtils.processServerImageUrl,
    // 自定义分享标题
    getShareTitle() {
      return "天大海棠 - 个人资料";
    },
    // 自定义分享内容
    getShareContent() {
      return "个人资料分享";
    },
    // 表单验证方法
    validateName() {
      this.form.nameError = !this.form.name || this.form.name.trim() === "";
    },
    validateBirthDate() {
      this.form.birthDateError = !this.form.birthDate;
    },
    validateNativePlace() {
      this.form.nativePlaceError = !this.form.nativePlace;
    },
    validatePhoneRequired() {
      this.form.phoneError = !this.form.phone || this.form.phone.trim() === "";
    },
    validateSchool() {
      this.form.schoolError = !this.form.school || this.form.school.trim() === "";
    },
    validateMajor() {
      this.form.majorError = !this.form.major || this.form.major.trim() === "";
    },
    validateCompany() {
      const company = this.form.company ? this.form.company.trim() : "";
      if (!company) {
        this.form.companyError = true;
        this.companyError = "请输入企业名称";
        return;
      }
      const companyValidation = this.validateCompanyName(company);
      if (companyValidation) {
        this.form.companyError = true;
        this.companyError = companyValidation;
        return;
      }
      this.form.companyError = false;
      this.companyError = "";
    },
    validateIndustry() {
      this.form.industryError = !this.form.industry;
    },
    // 加载职位类型数据
    loadJobTypes() {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:728", "开始加载职位类型数据");
      utils_request.request.post("/miniapp/jobType/app/getEnabledList", {}).then((result) => {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:730", "职位类型数据响应:", result);
        if (result && result.data && result.data.code === 200) {
          const jobTypeList = result.data.data || [];
          this.positionTypeOptions = jobTypeList.map((item) => item.typeName || item);
          common_vendor.index.__f__("log", "at pages/index/profile.vue:734", "职位类型数据加载成功:", this.positionTypeOptions);
        } else {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:736", "职位类型数据加载失败:", result);
          this.positionTypeOptions = ["技术类", "产品类", "设计类", "市场类", "其他"];
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:741", "加载职位类型数据失败:", error);
        this.positionTypeOptions = ["技术类", "产品类", "设计类", "市场类", "其他"];
      });
    },
    // 加载行业数据
    loadIndustryData() {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:749", "开始加载行业数据");
      return utils_request.request.get("/miniapp/industry/tree").then((result) => {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:751", "行业数据响应:", result);
        if (result && result.data && result.data.code === 200) {
          const industryTree = result.data.data || [];
          this.industryData = this.formatIndustryData(industryTree);
          common_vendor.index.__f__("log", "at pages/index/profile.vue:755", "行业数据加载成功:", this.industryData);
          return true;
        } else {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:758", "行业数据加载失败:", result);
          return false;
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:763", "加载行业数据失败:", error);
        return false;
      });
    },
    // 格式化行业数据
    formatIndustryData(treeData) {
      return treeData.map((level1) => {
        const level1Item = {
          id: level1.id,
          name: level1.nodeName,
          hasStreamType: level1.hasStreamType,
          parentId: level1.parentId,
          children: []
        };
        if (level1.children && level1.children.length > 0) {
          level1Item.children = level1.children.map((level2) => {
            const level2Item = {
              id: level2.id,
              name: level2.nodeName,
              description: level2.nodeDescription || "",
              hasStreamType: level2.hasStreamType,
              streamType: level2.streamType,
              parentId: level2.parentId,
              children: []
            };
            if (level2.children && level2.children.length > 0) {
              level2Item.children = level2.children.map((level3) => ({
                id: level3.id,
                name: level3.nodeName,
                description: level3.nodeDescription || "",
                parentId: level3.parentId
              }));
            }
            return level2Item;
          });
        }
        return level1Item;
      });
    },
    // 根据streamType获取分类
    getStreamTypeCategory(streamType) {
      const categoryMap = {
        "upstream": "上游",
        "midstream": "中游",
        "downstream": "下游"
      };
      return categoryMap[streamType] || "中游";
    },
    // 从服务器加载用户资料详情（从mine页面进入时使用）
    loadUserProfileFromServer() {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:821", "从服务器加载用户资料详情");
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_request.request.get("/miniapp/user/getProfileDetail").then((result) => {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:831", "获取用户资料详情响应:", result);
        if (result && result.data && result.data.code === 200) {
          const profileData = result.data.data;
          common_vendor.index.__f__("log", "at pages/index/profile.vue:835", "用户资料详情数据:", profileData);
          this.ensureIndustryDataLoadedThenFillForm(profileData);
          common_vendor.index.hideLoading();
        } else {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:842", "获取用户资料详情失败:", result);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "加载用户资料失败",
            icon: "none"
          });
          this.loadPhoneNumber();
          this.checkModuleCompletion();
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:855", "获取用户资料详情失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "加载用户资料失败",
          icon: "none"
        });
        this.loadPhoneNumber();
        this.checkModuleCompletion();
      });
    },
    // 确保行业数据加载完成后再填充表单数据
    ensureIndustryDataLoadedThenFillForm(profileData) {
      if (this.industryData && this.industryData.length > 0) {
        this.fillFormWithProfileData(profileData);
        this.isDataLoaded = true;
        this.checkModuleCompletion();
      } else {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:878", "等待行业数据加载完成...");
        const checkInterval = setInterval(() => {
          if (this.industryData && this.industryData.length > 0) {
            clearInterval(checkInterval);
            common_vendor.index.__f__("log", "at pages/index/profile.vue:882", "行业数据加载完成，开始填充表单");
            this.fillFormWithProfileData(profileData);
            this.isDataLoaded = true;
            this.checkModuleCompletion();
          }
        }, 100);
        setTimeout(() => {
          clearInterval(checkInterval);
          if (!this.isDataLoaded) {
            common_vendor.index.__f__("log", "at pages/index/profile.vue:893", "行业数据加载超时，使用默认数据填充表单");
            this.fillFormWithProfileData(profileData);
            this.isDataLoaded = true;
            this.checkModuleCompletion();
          }
        }, 5e3);
      }
    },
    // 将服务器返回的资料数据填充到表单中
    fillFormWithProfileData(profileData) {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:904", "开始填充表单数据:", profileData);
      const basicInfo = profileData.basicInfo || {};
      this.form.name = basicInfo.realName || "";
      this.form.gender = this.convertGenderFromServer(basicInfo.sex);
      this.form.birthDate = basicInfo.birthDate || "";
      this.form.nativePlace = basicInfo.region || "";
      this.form.phone = basicInfo.phonenumber || "";
      this.form.avatar = utils_imageUtils.processServerImageUrl(basicInfo.portraitUrl || "");
      this.parseNativePlaceData(basicInfo.region || "");
      this.form.selfIntroduction = profileData.personalIntroduction || "";
      const educationInfo = profileData.educationInfo || {};
      this.form.school = educationInfo.graduateSchool || "";
      this.form.college = educationInfo.college || "";
      this.form.graduationYear = educationInfo.graduationYear || "";
      this.form.major = educationInfo.major || "";
      const careerInfo = profileData.careerInfo || {};
      this.form.company = careerInfo.currentCompany || "";
      if (careerInfo.positionTitle) {
        const positionParts = careerInfo.positionTitle.split(" - ");
        this.form.positionType = positionParts[0] || "";
        this.form.positionName = positionParts[1] || "";
      } else {
        this.form.positionType = "";
        this.form.positionName = "";
      }
      this.form.industry = careerInfo.industryField || "";
      if (careerInfo.industryField) {
        this.loadIndustrySelectionFromIds(careerInfo.industryField);
      }
      this.loadPointsEarnedStatus(profileData);
      common_vendor.index.__f__("log", "at pages/index/profile.vue:952", "表单数据填充完成:", this.form);
    },
    // 转换服务器返回的性别数据格式
    convertGenderFromServer(serverGender) {
      if (serverGender === "0" || serverGender === 0 || serverGender === "男") {
        return "男";
      } else if (serverGender === "1" || serverGender === 1 || serverGender === "女") {
        return "女";
      }
      return "";
    },
    // 根据ID字符串加载行业选择状态
    loadIndustrySelectionFromIds(industryFieldStr) {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:969", "开始加载行业选择状态，ID字符串:", industryFieldStr);
      if (!industryFieldStr || !this.industryData || this.industryData.length === 0) {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:972", "行业数据或ID字符串为空，跳过加载");
        return;
      }
      const selectedIds = industryFieldStr.split(",").map((id) => parseInt(id.trim())).filter((id) => !isNaN(id));
      common_vendor.index.__f__("log", "at pages/index/profile.vue:978", "解析的ID数组:", selectedIds);
      if (selectedIds.length === 0) {
        return;
      }
      const selectedLevel1 = [];
      const selectedLevel2 = [];
      const selectedLevel3 = [];
      this.industryData.forEach((level1Item) => {
        let level1Selected = false;
        if (level1Item.children && level1Item.children.length > 0) {
          level1Item.children.forEach((level2Item) => {
            let level2Selected = false;
            if (level2Item.children && level2Item.children.length > 0) {
              level2Item.children.forEach((level3Item) => {
                if (selectedIds.includes(level3Item.id)) {
                  selectedLevel3.push({
                    id: level3Item.id,
                    name: level3Item.name,
                    description: level3Item.description || ""
                  });
                  level2Selected = true;
                  level1Selected = true;
                }
              });
            }
            if (level2Selected) {
              const existingLevel2 = selectedLevel2.find((item) => item.id === level2Item.id);
              if (!existingLevel2) {
                selectedLevel2.push({
                  id: level2Item.id,
                  name: level2Item.name
                });
              }
            }
          });
        }
        if (level1Selected) {
          const existingLevel1 = selectedLevel1.find((item) => item.id === level1Item.id);
          if (!existingLevel1) {
            selectedLevel1.push({
              id: level1Item.id,
              name: level1Item.name
            });
          }
        }
      });
      this.selectedIndustryData = {
        level1: selectedLevel1,
        level2: selectedLevel2,
        level3: selectedLevel3
      };
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1044", "行业选择状态加载完成:", this.selectedIndustryData);
    },
    // 加载积分获取状态
    loadPointsEarnedStatus(profileData) {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1049", "开始加载积分获取状态:", profileData);
      const basicInfo = profileData.basicInfo || {};
      const educationInfo = profileData.educationInfo || {};
      const careerInfo = profileData.careerInfo || {};
      this.modulePointsEarned.basic = basicInfo.isComplete === true;
      this.modulePointsEarned.education = educationInfo.isComplete === true;
      this.modulePointsEarned.career = careerInfo.isComplete === true;
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1061", "积分获取状态:", this.modulePointsEarned);
    },
    // 加载保存的手机号（首次注册时使用）
    loadPhoneNumber() {
      try {
        const savedPhone = common_vendor.index.getStorageSync("phone");
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        common_vendor.index.__f__("log", "at pages/index/profile.vue:1071", "尝试获取手机号 - savedPhone:", savedPhone);
        common_vendor.index.__f__("log", "at pages/index/profile.vue:1072", "尝试获取手机号 - userInfo:", userInfo);
        common_vendor.index.__f__("log", "at pages/index/profile.vue:1073", "尝试获取手机号 - userInfo.phonenumber:", userInfo == null ? void 0 : userInfo.phonenumber);
        let phoneNumber = savedPhone || (userInfo == null ? void 0 : userInfo.phonenumber) || "";
        if (phoneNumber) {
          this.form.phone = phoneNumber;
          common_vendor.index.__f__("log", "at pages/index/profile.vue:1079", "自动填写手机号:", phoneNumber);
          this.phoneError = this.validatePhone(phoneNumber);
        } else {
          common_vendor.index.__f__("log", "at pages/index/profile.vue:1083", "没有找到手机号");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:1086", "获取手机号失败:", error);
      }
    },
    // 检查模块完成状态
    checkModuleCompletion() {
      let completedFields = 0;
      const totalFields = 14;
      if (this.form.name && this.form.name.trim())
        completedFields++;
      if (this.form.gender)
        completedFields++;
      if (this.form.birthDate)
        completedFields++;
      if (this.form.nativePlace)
        completedFields++;
      if (this.form.phone && this.validatePhone(this.form.phone) === "")
        completedFields++;
      if (this.form.avatar)
        completedFields++;
      if (this.form.selfIntroduction && this.form.selfIntroduction.trim())
        completedFields++;
      if (this.form.school && this.form.school.trim())
        completedFields++;
      if (this.form.college && this.form.college.trim())
        completedFields++;
      if (this.form.graduationYear)
        completedFields++;
      if (this.form.major && this.form.major.trim())
        completedFields++;
      if (this.form.company && this.form.company.trim())
        completedFields++;
      if (this.form.positionType && this.form.positionName && this.form.positionName.trim())
        completedFields++;
      if (this.form.industry)
        completedFields++;
      this.progress = Math.round(completedFields / totalFields * 100);
      this.moduleStatus.basic = !!(this.form.name && this.form.gender && this.form.birthDate && this.form.nativePlace && this.form.phone && this.form.avatar);
      this.moduleStatus.education = !!(this.form.school && this.form.college && this.form.graduationYear && this.form.major);
      this.moduleStatus.career = !!(this.form.company && this.form.positionType && this.form.positionName && this.form.industry);
    },
    // 检查必填项是否完成（用于提交验证）
    checkRequiredFields() {
      const phoneValid = this.form.phone && this.validatePhone(this.form.phone) === "";
      const basicRequired = !!(this.form.name && this.form.birthDate && this.form.nativePlace && phoneValid);
      const educationRequired = !!(this.form.school && this.form.major);
      const careerRequired = !!(this.form.company && this.form.industry);
      return {
        basicRequired,
        educationRequired,
        careerRequired,
        phoneValid,
        canSubmit: basicRequired && educationRequired && careerRequired
      };
    },
    // 生成年份选项
    generateYearOptions() {
      const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
      for (let year = 1980; year <= currentYear; year++) {
        this.graduationYears.push(year.toString());
      }
    },
    // 初始化省市数据
    initProvinceData() {
      this.provinceList = [
        {
          name: "北京",
          cities: [
            { name: "东城区" },
            { name: "西城区" },
            { name: "朝阳区" },
            { name: "丰台区" },
            { name: "石景山区" },
            { name: "海淀区" },
            { name: "门头沟区" },
            { name: "房山区" },
            { name: "通州区" },
            { name: "顺义区" },
            { name: "昌平区" },
            { name: "大兴区" },
            { name: "怀柔区" },
            { name: "平谷区" },
            { name: "密云区" },
            { name: "延庆区" }
          ]
        },
        {
          name: "上海",
          cities: [
            { name: "黄浦区" },
            { name: "徐汇区" },
            { name: "长宁区" },
            { name: "静安区" },
            { name: "普陀区" },
            { name: "虹口区" },
            { name: "杨浦区" },
            { name: "闵行区" },
            { name: "宝山区" },
            { name: "嘉定区" },
            { name: "浦东新区" },
            { name: "金山区" },
            { name: "松江区" },
            { name: "青浦区" },
            { name: "奉贤区" },
            { name: "崇明区" }
          ]
        },
        {
          name: "天津",
          cities: [
            { name: "和平区" },
            { name: "河东区" },
            { name: "河西区" },
            { name: "南开区" },
            { name: "河北区" },
            { name: "红桥区" },
            { name: "东丽区" },
            { name: "西青区" },
            { name: "津南区" },
            { name: "北辰区" },
            { name: "武清区" },
            { name: "宝坻区" },
            { name: "滨海新区" },
            { name: "宁河区" },
            { name: "静海区" },
            { name: "蓟州区" }
          ]
        },
        {
          name: "重庆",
          cities: [
            { name: "万州区" },
            { name: "涪陵区" },
            { name: "渝中区" },
            { name: "大渡口区" },
            { name: "江北区" },
            { name: "沙坪坝区" },
            { name: "九龙坡区" },
            { name: "南岸区" },
            { name: "北碚区" },
            { name: "綦江区" },
            { name: "大足区" },
            { name: "渝北区" },
            { name: "巴南区" },
            { name: "黔江区" },
            { name: "长寿区" },
            { name: "江津区" },
            { name: "合川区" },
            { name: "永川区" },
            { name: "南川区" },
            { name: "璧山区" },
            { name: "铜梁区" },
            { name: "潼南区" },
            { name: "荣昌区" },
            { name: "开州区" }
          ]
        },
        {
          name: "河北",
          cities: [
            { name: "石家庄市" },
            { name: "唐山市" },
            { name: "秦皇岛市" },
            { name: "邯郸市" },
            { name: "邢台市" },
            { name: "保定市" },
            { name: "张家口市" },
            { name: "承德市" },
            { name: "沧州市" },
            { name: "廊坊市" },
            { name: "衡水市" }
          ]
        },
        {
          name: "山西",
          cities: [
            { name: "太原市" },
            { name: "大同市" },
            { name: "阳泉市" },
            { name: "长治市" },
            { name: "晋城市" },
            { name: "朔州市" },
            { name: "晋中市" },
            { name: "运城市" },
            { name: "忻州市" },
            { name: "临汾市" },
            { name: "吕梁市" }
          ]
        },
        {
          name: "内蒙古",
          cities: [
            { name: "呼和浩特市" },
            { name: "包头市" },
            { name: "乌海市" },
            { name: "赤峰市" },
            { name: "通辽市" },
            { name: "鄂尔多斯市" },
            { name: "呼伦贝尔市" },
            { name: "巴彦淖尔市" },
            { name: "乌兰察布市" },
            { name: "兴安盟" },
            { name: "锡林郭勒盟" },
            { name: "阿拉善盟" }
          ]
        },
        {
          name: "辽宁",
          cities: [
            { name: "沈阳市" },
            { name: "大连市" },
            { name: "鞍山市" },
            { name: "抚顺市" },
            { name: "本溪市" },
            { name: "丹东市" },
            { name: "锦州市" },
            { name: "营口市" },
            { name: "阜新市" },
            { name: "辽阳市" },
            { name: "盘锦市" },
            { name: "铁岭市" },
            { name: "朝阳市" },
            { name: "葫芦岛市" }
          ]
        },
        {
          name: "吉林",
          cities: [
            { name: "长春市" },
            { name: "吉林市" },
            { name: "四平市" },
            { name: "辽源市" },
            { name: "通化市" },
            { name: "白山市" },
            { name: "松原市" },
            { name: "白城市" },
            { name: "延边朝鲜族自治州" }
          ]
        },
        {
          name: "黑龙江",
          cities: [
            { name: "哈尔滨市" },
            { name: "齐齐哈尔市" },
            { name: "鸡西市" },
            { name: "鹤岗市" },
            { name: "双鸭山市" },
            { name: "大庆市" },
            { name: "伊春市" },
            { name: "佳木斯市" },
            { name: "七台河市" },
            { name: "牡丹江市" },
            { name: "黑河市" },
            { name: "绥化市" },
            { name: "大兴安岭地区" }
          ]
        },
        {
          name: "江苏",
          cities: [
            { name: "南京市" },
            { name: "无锡市" },
            { name: "徐州市" },
            { name: "常州市" },
            { name: "苏州市" },
            { name: "南通市" },
            { name: "连云港市" },
            { name: "淮安市" },
            { name: "盐城市" },
            { name: "扬州市" },
            { name: "镇江市" },
            { name: "泰州市" },
            { name: "宿迁市" }
          ]
        },
        {
          name: "浙江",
          cities: [
            { name: "杭州市" },
            { name: "宁波市" },
            { name: "温州市" },
            { name: "嘉兴市" },
            { name: "湖州市" },
            { name: "绍兴市" },
            { name: "金华市" },
            { name: "衢州市" },
            { name: "舟山市" },
            { name: "台州市" },
            { name: "丽水市" }
          ]
        },
        {
          name: "安徽",
          cities: [
            { name: "合肥市" },
            { name: "芜湖市" },
            { name: "蚌埠市" },
            { name: "淮南市" },
            { name: "马鞍山市" },
            { name: "淮北市" },
            { name: "铜陵市" },
            { name: "安庆市" },
            { name: "黄山市" },
            { name: "滁州市" },
            { name: "阜阳市" },
            { name: "宿州市" },
            { name: "六安市" },
            { name: "亳州市" },
            { name: "池州市" },
            { name: "宣城市" }
          ]
        },
        {
          name: "福建",
          cities: [
            { name: "福州市" },
            { name: "厦门市" },
            { name: "莆田市" },
            { name: "三明市" },
            { name: "泉州市" },
            { name: "漳州市" },
            { name: "南平市" },
            { name: "龙岩市" },
            { name: "宁德市" }
          ]
        },
        {
          name: "江西",
          cities: [
            { name: "南昌市" },
            { name: "景德镇市" },
            { name: "萍乡市" },
            { name: "九江市" },
            { name: "新余市" },
            { name: "鹰潭市" },
            { name: "赣州市" },
            { name: "吉安市" },
            { name: "宜春市" },
            { name: "抚州市" },
            { name: "上饶市" }
          ]
        },
        {
          name: "山东",
          cities: [
            { name: "济南市" },
            { name: "青岛市" },
            { name: "淄博市" },
            { name: "枣庄市" },
            { name: "东营市" },
            { name: "烟台市" },
            { name: "潍坊市" },
            { name: "济宁市" },
            { name: "泰安市" },
            { name: "威海市" },
            { name: "日照市" },
            { name: "临沂市" },
            { name: "德州市" },
            { name: "聊城市" },
            { name: "滨州市" },
            { name: "菏泽市" }
          ]
        },
        {
          name: "河南",
          cities: [
            { name: "郑州市" },
            { name: "开封市" },
            { name: "洛阳市" },
            { name: "平顶山市" },
            { name: "安阳市" },
            { name: "鹤壁市" },
            { name: "新乡市" },
            { name: "焦作市" },
            { name: "濮阳市" },
            { name: "许昌市" },
            { name: "漯河市" },
            { name: "三门峡市" },
            { name: "南阳市" },
            { name: "商丘市" },
            { name: "信阳市" },
            { name: "周口市" },
            { name: "驻马店市" },
            { name: "济源市" }
          ]
        },
        {
          name: "湖北",
          cities: [
            { name: "武汉市" },
            { name: "黄石市" },
            { name: "十堰市" },
            { name: "宜昌市" },
            { name: "襄阳市" },
            { name: "鄂州市" },
            { name: "荆门市" },
            { name: "孝感市" },
            { name: "荆州市" },
            { name: "黄冈市" },
            { name: "咸宁市" },
            { name: "随州市" },
            { name: "恩施土家族苗族自治州" },
            { name: "仙桃市" },
            { name: "潜江市" },
            { name: "天门市" },
            { name: "神农架林区" }
          ]
        },
        {
          name: "湖南",
          cities: [
            { name: "长沙市" },
            { name: "株洲市" },
            { name: "湘潭市" },
            { name: "衡阳市" },
            { name: "邵阳市" },
            { name: "岳阳市" },
            { name: "常德市" },
            { name: "张家界市" },
            { name: "益阳市" },
            { name: "郴州市" },
            { name: "永州市" },
            { name: "怀化市" },
            { name: "娄底市" },
            { name: "湘西土家族苗族自治州" }
          ]
        },
        {
          name: "广东",
          cities: [
            { name: "广州市" },
            { name: "韶关市" },
            { name: "深圳市" },
            { name: "珠海市" },
            { name: "汕头市" },
            { name: "佛山市" },
            { name: "江门市" },
            { name: "湛江市" },
            { name: "茂名市" },
            { name: "肇庆市" },
            { name: "惠州市" },
            { name: "梅州市" },
            { name: "汕尾市" },
            { name: "河源市" },
            { name: "阳江市" },
            { name: "清远市" },
            { name: "东莞市" },
            { name: "中山市" },
            { name: "潮州市" },
            { name: "揭阳市" },
            { name: "云浮市" }
          ]
        },
        {
          name: "广西",
          cities: [
            { name: "南宁市" },
            { name: "柳州市" },
            { name: "桂林市" },
            { name: "梧州市" },
            { name: "北海市" },
            { name: "防城港市" },
            { name: "钦州市" },
            { name: "贵港市" },
            { name: "玉林市" },
            { name: "百色市" },
            { name: "贺州市" },
            { name: "河池市" },
            { name: "来宾市" },
            { name: "崇左市" }
          ]
        },
        {
          name: "海南",
          cities: [
            { name: "海口市" },
            { name: "三亚市" },
            { name: "三沙市" },
            { name: "儋州市" },
            { name: "五指山市" },
            { name: "琼海市" },
            { name: "文昌市" },
            { name: "万宁市" },
            { name: "东方市" },
            { name: "定安县" },
            { name: "屯昌县" },
            { name: "澄迈县" },
            { name: "临高县" },
            { name: "白沙黎族自治县" },
            { name: "昌江黎族自治县" },
            { name: "乐东黎族自治县" },
            { name: "陵水黎族自治县" },
            { name: "保亭黎族苗族自治县" },
            { name: "琼中黎族苗族自治县" }
          ]
        },
        {
          name: "四川",
          cities: [
            { name: "成都市" },
            { name: "自贡市" },
            { name: "攀枝花市" },
            { name: "泸州市" },
            { name: "德阳市" },
            { name: "绵阳市" },
            { name: "广元市" },
            { name: "遂宁市" },
            { name: "内江市" },
            { name: "乐山市" },
            { name: "南充市" },
            { name: "眉山市" },
            { name: "宜宾市" },
            { name: "广安市" },
            { name: "达州市" },
            { name: "雅安市" },
            { name: "巴中市" },
            { name: "资阳市" },
            { name: "阿坝藏族羌族自治州" },
            { name: "甘孜藏族自治州" },
            { name: "凉山彝族自治州" }
          ]
        },
        {
          name: "贵州",
          cities: [
            { name: "贵阳市" },
            { name: "六盘水市" },
            { name: "遵义市" },
            { name: "安顺市" },
            { name: "毕节市" },
            { name: "铜仁市" },
            { name: "黔西南布依族苗族自治州" },
            { name: "黔东南苗族侗族自治州" },
            { name: "黔南布依族苗族自治州" }
          ]
        },
        {
          name: "云南",
          cities: [
            { name: "昆明市" },
            { name: "曲靖市" },
            { name: "玉溪市" },
            { name: "保山市" },
            { name: "昭通市" },
            { name: "丽江市" },
            { name: "普洱市" },
            { name: "临沧市" },
            { name: "楚雄彝族自治州" },
            { name: "红河哈尼族彝族自治州" },
            { name: "文山壮族苗族自治州" },
            { name: "西双版纳傣族自治州" },
            { name: "大理白族自治州" },
            { name: "德宏傣族景颇族自治州" },
            { name: "怒江傈僳族自治州" },
            { name: "迪庆藏族自治州" }
          ]
        },
        {
          name: "西藏",
          cities: [
            { name: "拉萨市" },
            { name: "日喀则市" },
            { name: "昌都市" },
            { name: "林芝市" },
            { name: "山南市" },
            { name: "那曲市" },
            { name: "阿里地区" }
          ]
        },
        {
          name: "陕西",
          cities: [
            { name: "西安市" },
            { name: "铜川市" },
            { name: "宝鸡市" },
            { name: "咸阳市" },
            { name: "渭南市" },
            { name: "延安市" },
            { name: "汉中市" },
            { name: "榆林市" },
            { name: "安康市" },
            { name: "商洛市" }
          ]
        },
        {
          name: "甘肃",
          cities: [
            { name: "兰州市" },
            { name: "嘉峪关市" },
            { name: "金昌市" },
            { name: "白银市" },
            { name: "天水市" },
            { name: "武威市" },
            { name: "张掖市" },
            { name: "平凉市" },
            { name: "酒泉市" },
            { name: "庆阳市" },
            { name: "定西市" },
            { name: "陇南市" },
            { name: "临夏回族自治州" },
            { name: "甘南藏族自治州" }
          ]
        },
        {
          name: "青海",
          cities: [
            { name: "西宁市" },
            { name: "海东市" },
            { name: "海北藏族自治州" },
            { name: "黄南藏族自治州" },
            { name: "海南藏族自治州" },
            { name: "果洛藏族自治州" },
            { name: "玉树藏族自治州" },
            { name: "海西蒙古族藏族自治州" }
          ]
        },
        {
          name: "宁夏",
          cities: [
            { name: "银川市" },
            { name: "石嘴山市" },
            { name: "吴忠市" },
            { name: "固原市" },
            { name: "中卫市" }
          ]
        },
        {
          name: "新疆",
          cities: [
            { name: "乌鲁木齐市" },
            { name: "克拉玛依市" },
            { name: "吐鲁番市" },
            { name: "哈密市" },
            { name: "昌吉回族自治州" },
            { name: "博尔塔拉蒙古自治州" },
            { name: "巴音郭楞蒙古自治州" },
            { name: "阿克苏地区" },
            { name: "克孜勒苏柯尔克孜自治州" },
            { name: "喀什地区" },
            { name: "和田地区" },
            { name: "伊犁哈萨克自治州" },
            { name: "塔城地区" },
            { name: "阿勒泰地区" },
            { name: "石河子市" },
            { name: "阿拉尔市" },
            { name: "图木舒克市" },
            { name: "五家渠市" },
            { name: "北屯市" },
            { name: "铁门关市" },
            { name: "双河市" },
            { name: "可克达拉市" },
            { name: "昆玉市" },
            { name: "胡杨河市" }
          ]
        },
        {
          name: "香港",
          cities: [
            { name: "中西区" },
            { name: "湾仔区" },
            { name: "东区" },
            { name: "南区" },
            { name: "油尖旺区" },
            { name: "深水埗区" },
            { name: "九龙城区" },
            { name: "黄大仙区" },
            { name: "观塘区" },
            { name: "荃湾区" },
            { name: "屯门区" },
            { name: "元朗区" },
            { name: "北区" },
            { name: "大埔区" },
            { name: "沙田区" },
            { name: "西贡区" },
            { name: "葵青区" },
            { name: "离岛区" }
          ]
        },
        {
          name: "澳门",
          cities: [
            { name: "澳门半岛" },
            { name: "氹仔" },
            { name: "路环" }
          ]
        },
        {
          name: "台湾",
          cities: [
            { name: "台北市" },
            { name: "新北市" },
            { name: "桃园市" },
            { name: "台中市" },
            { name: "台南市" },
            { name: "高雄市" },
            { name: "基隆市" },
            { name: "新竹市" },
            { name: "嘉义市" },
            { name: "新竹县" },
            { name: "苗栗县" },
            { name: "彰化县" },
            { name: "南投县" },
            { name: "云林县" },
            { name: "嘉义县" },
            { name: "屏东县" },
            { name: "宜兰县" },
            { name: "花莲县" },
            { name: "台东县" },
            { name: "澎湖县" },
            { name: "金门县" },
            { name: "连江县" }
          ]
        }
      ];
    },
    // 验证手机号格式
    validatePhone(phone) {
      if (!phone) {
        return "";
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        if (phone.length < 11) {
          return "手机号码长度不足11位";
        } else if (phone.length > 11) {
          return "手机号码长度超过11位";
        } else if (!phone.startsWith("1")) {
          return "手机号码必须以1开头";
        } else if (!/^1[3-9]/.test(phone)) {
          return "手机号码第二位必须是3-9之间的数字";
        } else {
          return "请输入正确的手机号码格式";
        }
      }
      return "";
    },
    // 验证企业名称格式（按照中国企业全称规范）
    validateCompanyName(company) {
      if (!company) {
        return "";
      }
      company = company.trim();
      if (company.length < 5) {
        return "企业全称至少需要5个字符";
      }
      if (company.length > 80) {
        return "企业名称不得超过80个字符";
      }
      const companyRegex = /^[\u4e00-\u9fa5a-zA-Z0-9\(\)（）]+$/;
      if (!companyRegex.test(company)) {
        return "企业名称只能包含中文、英文、数字和括号";
      }
      const validOrganizationForms = [
        // 有限责任公司
        "有限责任公司",
        "有限公司",
        // 股份有限公司
        "股份有限公司",
        "股份公司",
        // 个人独资企业
        "个人独资企业",
        // 合伙企业
        "合伙企业",
        "普通合伙企业",
        "有限合伙企业",
        // 个体工商户
        "个体工商户",
        "个体户",
        // 外商投资企业
        "外商独资企业",
        "中外合资企业",
        "中外合作企业",
        // 集团公司
        "集团有限公司",
        "集团股份有限公司",
        "集团公司",
        // 其他特殊形式
        "事务所",
        "工作室",
        "中心",
        "研究院",
        "研究所"
      ];
      const hasValidOrganizationForm = validOrganizationForms.some(
        (form) => company.endsWith(form)
      );
      if (!hasValidOrganizationForm) {
        return "企业名称必须包含正确的组织形式，如：有限公司、股份有限公司等";
      }
      const administrativeDivisions = [
        "北京",
        "天津",
        "上海",
        "重庆",
        // 直辖市
        "河北",
        "山西",
        "辽宁",
        "吉林",
        "黑龙江",
        "江苏",
        "浙江",
        "安徽",
        "福建",
        "江西",
        "山东",
        "河南",
        "湖北",
        "湖南",
        "广东",
        "海南",
        "四川",
        "贵州",
        "云南",
        "陕西",
        "甘肃",
        "青海",
        "台湾",
        // 省
        "内蒙古",
        "广西",
        "西藏",
        "宁夏",
        "新疆",
        // 自治区
        "香港",
        "澳门",
        // 特别行政区
        // 常见地级市
        "深圳",
        "广州",
        "杭州",
        "南京",
        "苏州",
        "成都",
        "武汉",
        "西安",
        "郑州",
        "青岛",
        "大连",
        "宁波",
        "厦门",
        "济南",
        "沈阳",
        "长沙",
        "哈尔滨",
        "昆明",
        "福州",
        "无锡",
        "合肥",
        "东莞",
        "佛山",
        "长春",
        "石家庄",
        "南昌",
        "贵阳",
        "南宁",
        "金华",
        "常州",
        "嘉兴",
        "太原",
        "徐州",
        "惠州",
        "珠海",
        "中山",
        "台州",
        "烟台",
        "兰州"
      ];
      administrativeDivisions.some(
        (division) => company.startsWith(division)
      );
      let remainingName = company;
      for (const form of validOrganizationForms) {
        if (company.endsWith(form)) {
          remainingName = company.substring(0, company.length - form.length);
          break;
        }
      }
      if (remainingName.length < 2) {
        return "企业字号部分过短，请输入完整的企业名称";
      }
      return "";
    },
    // 手机号输入处理
    onPhoneInput(e) {
      let phone = e.detail.value;
      phone = phone.replace(/[^\d]/g, "");
      if (phone.length > 11) {
        phone = phone.substring(0, 11);
      }
      this.form.phone = phone;
      this.phoneError = this.validatePhone(phone);
      this.checkModuleCompletion();
    },
    // 企业名称输入处理
    onCompanyInput(e) {
      const company = e.detail.value;
      this.form.company = company;
      if (company.trim()) {
        this.companyError = this.validateCompanyName(company.trim());
      } else {
        this.companyError = "";
      }
      this.checkModuleCompletion();
    },
    // 选择性别
    selectGender(gender) {
      this.form.gender = gender;
      this.checkModuleCompletion();
    },
    // 日期选择
    onDateChange(e) {
      this.form.birthDate = e.detail.value;
      this.form.birthDateError = false;
      this.checkModuleCompletion();
    },
    // 省份选择 - 只需要选择省份即可
    onProvinceChange(e) {
      const selectedIndex = e.detail.value;
      this.selectedProvince = this.provinceList[selectedIndex];
      this.selectedCity = null;
      this.updateNativePlace();
      this.form.nativePlaceError = false;
      this.checkModuleCompletion();
    },
    // 更新籍贯字段 - 只使用省份信息
    updateNativePlace() {
      if (this.selectedProvince) {
        this.form.nativePlace = this.selectedProvince.name;
      } else {
        this.form.nativePlace = "";
      }
    },
    // 解析籍贯数据并设置省份选择
    parseNativePlaceData(nativePlaceStr) {
      if (!nativePlaceStr) {
        this.selectedProvince = null;
        this.selectedCity = null;
        return;
      }
      let provinceName = nativePlaceStr;
      if (nativePlaceStr.includes("-")) {
        provinceName = nativePlaceStr.split("-")[0];
      }
      const province = this.provinceList.find((p) => p.name === provinceName);
      if (province) {
        this.selectedProvince = province;
      } else {
        this.selectedProvince = null;
      }
      this.selectedCity = null;
    },
    // 毕业年份选择
    onGraduationYearChange(e) {
      this.form.graduationYear = this.graduationYears[e.detail.value];
      this.checkModuleCompletion();
    },
    // 职位类型选择
    onPositionTypeChange(e) {
      this.form.positionType = this.positionTypeOptions[e.detail.value];
      this.checkModuleCompletion();
    },
    // 显示行业选择器
    showIndustryPicker() {
      this.tempSelectedIndustry = {
        level1: [...this.selectedIndustryData.level1],
        level2: [...this.selectedIndustryData.level2],
        level3: [...this.selectedIndustryData.level3]
      };
      this.initTreeExpandState();
      this.expandSelectedParents();
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1721", "🏭 行业数据结构:", this.industryData);
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1722", "🏭 当前选中数据:", this.tempSelectedIndustry);
      this.showIndustryModal = true;
    },
    // 隐藏行业选择器
    hideIndustryPicker() {
      this.showIndustryModal = false;
      if (!this.form.industry) {
        this.validateIndustry();
      }
    },
    // 初始化树形展开状态
    initTreeExpandState() {
      this.industryData.forEach((level1) => {
        this.$set(level1, "expanded", false);
        if (level1.children) {
          level1.children.forEach((level2) => {
            this.$set(level2, "expanded", false);
          });
        }
      });
    },
    // 展开已选择项目的父级分类
    expandSelectedParents() {
      if (this.tempSelectedIndustry.level3.length === 0) {
        return;
      }
      const selectedLevel3Ids = this.tempSelectedIndustry.level3.map((item) => item.id);
      this.industryData.forEach((level1) => {
        let shouldExpandLevel1 = false;
        if (level1.children && level1.children.length > 0) {
          level1.children.forEach((level2) => {
            let shouldExpandLevel2 = false;
            if (level2.children && level2.children.length > 0) {
              const hasSelectedLevel3 = level2.children.some(
                (level3) => selectedLevel3Ids.includes(level3.id)
              );
              if (hasSelectedLevel3) {
                shouldExpandLevel2 = true;
                shouldExpandLevel1 = true;
              }
            }
            if (shouldExpandLevel2) {
              this.$set(level2, "expanded", true);
            }
          });
        }
        if (shouldExpandLevel1) {
          this.$set(level1, "expanded", true);
        }
      });
    },
    // 切换一级分类展开状态
    toggleLevel1(level1) {
      if (this.canSelectLevel1(level1)) {
        this.$set(level1, "expanded", !level1.expanded);
      }
    },
    // 切换二级分类展开状态
    toggleLevel2(level2) {
      if (this.canSelectLevel2(level2)) {
        this.$set(level2, "expanded", !level2.expanded);
      }
    },
    // 获取指定流向的二级分类
    getLevel2ByStream(level1, streamType) {
      if (!level1.children)
        return [];
      const streamTypeMap = {
        "上游": "upstream",
        "中游": "midstream",
        "下游": "downstream"
      };
      const targetStreamType = streamTypeMap[streamType];
      common_vendor.index.__f__("log", "at pages/index/profile.vue:1819", `🔍 查找${streamType}(${targetStreamType})的二级分类:`, level1.children.map((l2) => ({
        name: l2.name,
        streamType: l2.streamType,
        hasStreamType: l2.hasStreamType
      })));
      return level1.children.filter((level2) => {
        if (!level2.streamType || level2.streamType === "null") {
          return false;
        }
        return level2.streamType === targetStreamType;
      });
    },
    // 判断一级分类是否被选中（基于其子级的选中情况）
    isLevel1Selected(level1) {
      if (!level1.children || level1.children.length === 0) {
        return false;
      }
      return level1.children.some((level2) => this.isLevel2Selected(level2));
    },
    // 判断二级分类是否被选中（基于其子级的选中情况）
    isLevel2Selected(level2) {
      if (!level2.children || level2.children.length === 0) {
        return false;
      }
      return level2.children.some((level3) => this.isLevel3Selected(level3));
    },
    // 判断三级分类是否被选中
    isLevel3Selected(level3) {
      return this.tempSelectedIndustry.level3.some((selected) => selected.id === level3.id);
    },
    // 判断一级分类是否可以被选择（是否有子级）
    canSelectLevel1(level1) {
      return level1.children && level1.children.length > 0;
    },
    // 判断二级分类是否可以被选择（是否有子级）
    canSelectLevel2(level2) {
      return level2.children && level2.children.length > 0;
    },
    // 设置当前步骤
    setCurrentStep(step) {
      if (step === 1) {
        this.currentStep = 1;
      } else if (step === 2 && this.tempSelectedIndustry.level1.length > 0) {
        this.currentStep = 2;
      } else if (step === 3 && this.tempSelectedIndustry.level2.length > 0) {
        this.currentStep = 3;
      }
    },
    // 选择一级分类（只有有子级的才能操作）
    selectLevel1(item) {
      if (!this.canSelectLevel1(item)) {
        common_vendor.index.showToast({
          title: "请选择具体的行业分类",
          icon: "none"
        });
        return;
      }
      const isCurrentlySelected = this.isLevel1Selected(item);
      if (isCurrentlySelected) {
        this.removeChildSelections(item.id, "level1");
      } else {
        this.selectAllChildren(item, "level1");
        this.expandAllChildren(item, "level1");
      }
      this.updateParentSelections();
    },
    // 选择二级分类（只有有子级的才能操作）
    selectLevel2(item) {
      if (!this.canSelectLevel2(item)) {
        common_vendor.index.showToast({
          title: "请选择具体的行业分类",
          icon: "none"
        });
        return;
      }
      const isCurrentlySelected = this.isLevel2Selected(item);
      if (isCurrentlySelected) {
        this.removeChildSelections(item.id, "level2");
      } else {
        this.selectAllChildren(item, "level2");
        this.expandAllChildren(item, "level2");
      }
      this.updateParentSelections();
    },
    // 选择三级分类（只有三级分类可以被直接选择）
    selectLevel3(item) {
      const level3Array = this.tempSelectedIndustry.level3;
      const existingIndex = level3Array.findIndex((selected) => selected.id === item.id);
      if (existingIndex > -1) {
        level3Array.splice(existingIndex, 1);
      } else {
        level3Array.push({
          id: item.id,
          name: item.name,
          parentId: item.parentId
        });
      }
      this.updateParentSelections();
    },
    // 自动展开所有子级（让用户看到选择了什么）
    expandAllChildren(item, level) {
      if (level === "level1") {
        this.$set(item, "expanded", true);
        const fullLevel1 = this.industryData.find((l1) => l1.id === item.id);
        if (fullLevel1 && fullLevel1.children && fullLevel1.children.length > 0) {
          fullLevel1.children.forEach((level2) => {
            this.$set(level2, "expanded", true);
          });
        }
      } else if (level === "level2") {
        this.$set(item, "expanded", true);
      }
    },
    // 级联选择所有三级子分类（只选择三级分类）
    selectAllChildren(item, level) {
      if (level === "level1") {
        const fullLevel1 = this.industryData.find((l1) => l1.id === item.id);
        if (fullLevel1 && fullLevel1.children && fullLevel1.children.length > 0) {
          fullLevel1.children.forEach((level2) => {
            if (level2.children && level2.children.length > 0) {
              level2.children.forEach((level3) => {
                const existingIndex3 = this.tempSelectedIndustry.level3.findIndex((selected) => selected.id === level3.id);
                if (existingIndex3 === -1) {
                  this.tempSelectedIndustry.level3.push({
                    id: level3.id,
                    name: level3.name,
                    parentId: level3.parentId
                  });
                }
              });
            }
          });
        }
      } else if (level === "level2") {
        const fullLevel2 = this.findLevel2ById(item.id);
        if (fullLevel2 && fullLevel2.children && fullLevel2.children.length > 0) {
          fullLevel2.children.forEach((level3) => {
            const existingIndex = this.tempSelectedIndustry.level3.findIndex((selected) => selected.id === level3.id);
            if (existingIndex === -1) {
              this.tempSelectedIndustry.level3.push({
                id: level3.id,
                name: level3.name,
                parentId: level3.parentId
              });
            }
          });
        }
      }
    },
    // 更新父级选择状态（基于子级的选择情况自动计算）
    updateParentSelections() {
      this.tempSelectedIndustry.level1 = [];
      this.tempSelectedIndustry.level2 = [];
      const selectedLevel3Ids = this.tempSelectedIndustry.level3.map((item) => item.id);
      const selectedLevel2Ids = /* @__PURE__ */ new Set();
      const selectedLevel1Ids = /* @__PURE__ */ new Set();
      this.industryData.forEach((level1) => {
        let level1HasSelectedChildren = false;
        if (level1.children && level1.children.length > 0) {
          level1.children.forEach((level2) => {
            if (level2.children && level2.children.length > 0) {
              const hasSelectedLevel3 = level2.children.some(
                (level3) => selectedLevel3Ids.includes(level3.id)
              );
              if (hasSelectedLevel3) {
                level1HasSelectedChildren = true;
                selectedLevel2Ids.add(level2.id);
              }
            }
          });
        }
        if (level1HasSelectedChildren) {
          selectedLevel1Ids.add(level1.id);
        }
      });
      selectedLevel1Ids.forEach((level1Id) => {
        const level1Item = this.industryData.find((l1) => l1.id === level1Id);
        if (level1Item) {
          this.tempSelectedIndustry.level1.push({
            id: level1Item.id,
            name: level1Item.name
          });
        }
      });
      selectedLevel2Ids.forEach((level2Id) => {
        const level2Item = this.findLevel2ById(level2Id);
        if (level2Item) {
          this.tempSelectedIndustry.level2.push({
            id: level2Item.id,
            name: level2Item.name,
            parentId: level2Item.parentId
          });
        }
      });
    },
    // 移除子级选择（只移除三级选择，父级状态会自动更新）
    removeChildSelections(parentId, parentLevel) {
      if (parentLevel === "level1") {
        this.tempSelectedIndustry.level3 = this.tempSelectedIndustry.level3.filter((level3) => {
          const level1Item = this.industryData.find((l1) => l1.id === parentId);
          if (level1Item && level1Item.children) {
            return !level1Item.children.some(
              (l2) => l2.children && l2.children.some((l3) => l3.id === level3.id)
            );
          }
          return true;
        });
      } else if (parentLevel === "level2") {
        this.tempSelectedIndustry.level3 = this.tempSelectedIndustry.level3.filter((level3) => {
          const level2Item = this.findLevel2ById(parentId);
          if (level2Item && level2Item.children) {
            return !level2Item.children.some((l3) => l3.id === level3.id);
          }
          return true;
        });
      }
    },
    // 根据ID查找二级分类
    findLevel2ById(level2Id) {
      for (let level1 of this.industryData) {
        if (level1.children) {
          const level2 = level1.children.find((l2) => l2.id === level2Id);
          if (level2)
            return level2;
        }
      }
      return null;
    },
    // 确认行业选择
    confirmIndustrySelection() {
      if (this.tempSelectedIndustry.level3 && this.tempSelectedIndustry.level3.length > 0) {
        this.selectedIndustryData = {
          level1: [...this.tempSelectedIndustry.level1],
          level2: [...this.tempSelectedIndustry.level2],
          level3: [...this.tempSelectedIndustry.level3]
        };
        this.form.industry = this.tempSelectedIndustry.level3.map((item) => item.id).join(",");
        this.form.industryError = false;
        this.checkModuleCompletion();
        this.hideIndustryPicker();
      } else {
        common_vendor.index.showToast({
          title: "请至少选择一个行业",
          icon: "none"
        });
      }
    },
    // 上传图片
    uploadImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        // 选择原图以保证裁剪质量
        sourceType: ["album", "camera"],
        // 从相册选择或拍照
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2151", "选择图片成功:", tempFilePath);
          this.selectedImagePath = tempFilePath;
          this.showImageCropper = true;
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:2158", "选择图片失败:", error);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 裁剪确认回调
    onCropConfirm(croppedImagePath) {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:2169", "图片裁剪完成:", croppedImagePath);
      this.showImageCropper = false;
      common_vendor.index.showLoading({
        title: "上传中..."
      });
      utils_request.request.upload("/common/upload", croppedImagePath, "file").then((result) => {
        if (result.success) {
          this.form.avatar = result.url;
          this.checkModuleCompletion();
          common_vendor.index.showToast({
            title: "上传成功",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2189", "图片上传成功，URL:", result.url);
        } else {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:2191", "上传失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "上传失败",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:2199", "上传失败:", error);
        common_vendor.index.showToast({
          title: "上传失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 裁剪取消回调
    onCropCancel() {
      common_vendor.index.__f__("log", "at pages/index/profile.vue:2212", "取消图片裁剪");
      this.showImageCropper = false;
      this.selectedImagePath = "";
    },
    // 提交表单
    async submitForm() {
      this.validateName();
      this.validateBirthDate();
      this.validateNativePlace();
      this.validatePhoneRequired();
      this.validateSchool();
      this.validateMajor();
      this.validateCompany();
      this.validateIndustry();
      const validation = this.checkRequiredFields();
      if (this.form.phone && !validation.phoneValid) {
        common_vendor.index.showToast({
          title: this.validatePhone(this.form.phone),
          icon: "none"
        });
        return;
      }
      if (this.form.company && this.companyError) {
        common_vendor.index.showToast({
          title: this.companyError,
          icon: "none"
        });
        return;
      }
      if (!validation.basicRequired) {
        common_vendor.index.showToast({
          title: "请完善基础信息必填项",
          icon: "none"
        });
        return;
      }
      if (!validation.educationRequired) {
        common_vendor.index.showToast({
          title: "请完善教育背景必填项",
          icon: "none"
        });
        return;
      }
      if (!validation.careerRequired) {
        common_vendor.index.showToast({
          title: "请完善职业信息必填项",
          icon: "none"
        });
        return;
      }
      const profileData = this.buildProfileData();
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      utils_request.request.post("/miniapp/user/updateProfile", profileData).then((result) => {
        var _a, _b;
        common_vendor.index.__f__("log", "at pages/index/profile.vue:2287", "资料提交响应:", result);
        if (result && result.data && result.data.code === 200) {
          const pointsAwarded = ((_a = result.data.data) == null ? void 0 : _a.pointsAwarded) || 0;
          const successMsg = result.data.msg || "资料更新成功";
          let toastTitle;
          if (pointsAwarded > 0) {
            toastTitle = `${successMsg}，获得${pointsAwarded}积分！`;
          } else {
            toastTitle = successMsg;
          }
          common_vendor.index.showToast({
            title: toastTitle,
            icon: "success",
            duration: 2e3
          });
          utils_profileCheck.updateProfileCompleteStatus(true);
          this.updateUserInfoCache();
          this.checkUserStatusAndRedirect();
        } else {
          const errorMsg = ((_b = result == null ? void 0 : result.data) == null ? void 0 : _b.msg) || "提交失败，请重试";
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:2324", "资料提交失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请检查网络连接",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 更新用户信息缓存
    async updateUserInfoCache() {
      try {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:2338", "🔄 开始更新用户信息缓存...");
        const response = await utils_request.request.get("/miniapp/user/getCurrentUser");
        common_vendor.index.__f__("log", "at pages/index/profile.vue:2342", "🔄 获取最新用户信息响应:", response);
        if (response && response.data && response.data.code === 200) {
          const latestUserInfo = response.data.data;
          common_vendor.index.setStorageSync("userInfo", latestUserInfo);
          const isInfoComplete = latestUserInfo.isInfoComplete;
          common_vendor.index.setStorageSync("isInfoComplete", isInfoComplete);
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2354", "✅ 用户信息缓存更新成功");
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2355", "💾 最新用户信息:", latestUserInfo);
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2356", "💾 个人资料完善状态:", isInfoComplete);
        } else {
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2358", "❌ 获取最新用户信息失败，但不影响主流程");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:2361", "🔄 更新用户信息缓存失败:", error);
      }
    },
    // 检查用户状态并决定跳转行为
    async checkUserStatusAndRedirect() {
      try {
        common_vendor.index.__f__("log", "at pages/index/profile.vue:2369", "👤 个人资料提交成功，检查用户状态...");
        const response = await utils_request.request.get("/miniapp/unified-enterprise/user/status");
        common_vendor.index.__f__("log", "at pages/index/profile.vue:2373", "👤 用户状态API响应:", response);
        if (response && response.data && response.data.code === 200) {
          const statusData = response.data || {};
          const status = statusData.status;
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2378", "👤 用户当前状态:", status);
          setTimeout(() => {
            if (this.isFromMine) {
              common_vendor.index.__f__("log", "at pages/index/profile.vue:2384", "个人资料提交成功，设置mine页面刷新标记");
              common_vendor.index.setStorageSync("minePageNeedRefresh", true);
              common_vendor.index.navigateBack();
            } else if (this.isFromCompany) {
              common_vendor.index.__f__("log", "at pages/index/profile.vue:2389", "个人资料提交成功，返回企业信息页面");
              common_vendor.index.navigateBack();
            } else if (status === "info_incomplete" || status === "not_submitted" && statusData.needCompleteInfo) {
              common_vendor.index.__f__("log", "at pages/index/profile.vue:2393", "个人资料已完善，引导用户完善企业信息");
              common_vendor.index.reLaunch({
                url: "/pages/industry/company-profile"
              });
            } else {
              common_vendor.index.reLaunch({
                url: "/pages/index/home"
              });
            }
          }, 2e3);
        } else {
          common_vendor.index.__f__("error", "at pages/index/profile.vue:2406", "👤 获取用户状态失败，使用默认跳转逻辑");
          this.defaultRedirect();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/profile.vue:2410", "👤 检查用户状态异常:", error);
        this.defaultRedirect();
      }
    },
    // 默认跳转逻辑
    defaultRedirect() {
      setTimeout(() => {
        if (this.isFromMine) {
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2421", "个人资料提交成功，设置mine页面刷新标记");
          common_vendor.index.setStorageSync("minePageNeedRefresh", true);
          common_vendor.index.navigateBack();
        } else if (this.isFromCompany) {
          common_vendor.index.__f__("log", "at pages/index/profile.vue:2426", "个人资料提交成功，返回企业信息页面");
          common_vendor.index.navigateBack();
        } else {
          common_vendor.index.reLaunch({
            url: "/pages/index/home"
          });
        }
      }, 2e3);
    },
    // 构建提交数据
    buildProfileData() {
      const getSexCode = (gender) => {
        if (gender === "男")
          return "0";
        if (gender === "女")
          return "1";
        return "2";
      };
      const getPositionTitle = () => {
        const parts = [];
        if (this.form.positionType) {
          parts.push(this.form.positionType);
        }
        if (this.form.positionName) {
          parts.push(this.form.positionName);
        }
        return parts.join(" - ");
      };
      return {
        realName: this.form.name || "",
        sex: getSexCode(this.form.gender),
        birthDate: this.form.birthDate || "",
        region: this.form.nativePlace || "",
        phonenumber: this.form.phone || "",
        portraitUrl: this.form.avatar || "",
        personalIntroduction: this.form.selfIntroduction || "",
        // 个人介绍
        graduateSchool: this.form.school || "",
        college: this.form.college || "",
        graduationYear: this.form.graduationYear || "",
        major: this.form.major || "",
        currentCompany: this.form.company || "",
        positionTitle: getPositionTitle(),
        industryField: this.form.industry || "",
        // 已经是逗号分隔的ID
        profileCompletionRate: this.progress,
        // 资料完成度百分比
        // 以下字段接口支持但当前表单没有，设为空值
        avatar: "",
        // 微信头像，我们用的是portraitUrl
        nickName: ""
        // 微信昵称，当前表单没有收集
      };
    }
  }
};
if (!Array) {
  const _easycom_image_cropper2 = common_vendor.resolveComponent("image-cropper");
  _easycom_image_cropper2();
}
const _easycom_image_cropper = () => "../../components/image-cropper/image-cropper.js";
if (!Math) {
  _easycom_image_cropper();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.getImagePath("login_bg.png"),
    b: $data.progress + "%",
    c: common_vendor.t($data.progress),
    d: $options.getImagePath("personal_info1.png"),
    e: common_vendor.t($data.modulePointsEarned.basic ? "已获得50积分" : "全部完善+50积分"),
    f: $data.moduleStatus.basic ? 1 : "",
    g: $data.modulePointsEarned.basic ? 1 : "",
    h: !$data.form.name || $data.form.name.trim() === ""
  }, !$data.form.name || $data.form.name.trim() === "" ? {} : {}, {
    i: $data.form.nameError ? 1 : "",
    j: common_vendor.o([($event) => $data.form.name = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    k: common_vendor.o((...args) => $options.validateName && $options.validateName(...args)),
    l: $data.form.name,
    m: $data.form.gender === "男"
  }, $data.form.gender === "男" ? {} : {}, {
    n: $data.form.gender === "男" ? 1 : "",
    o: common_vendor.o(($event) => $options.selectGender("男")),
    p: $data.form.gender === "女"
  }, $data.form.gender === "女" ? {} : {}, {
    q: $data.form.gender === "女" ? 1 : "",
    r: common_vendor.o(($event) => $options.selectGender("女")),
    s: !$data.form.birthDate
  }, !$data.form.birthDate ? {} : {}, {
    t: $data.form.birthDate
  }, $data.form.birthDate ? {
    v: common_vendor.t($data.form.birthDate)
  } : {}, {
    w: $data.form.birthDateError ? 1 : "",
    x: $data.form.birthDate,
    y: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    z: !$data.form.nativePlace
  }, !$data.form.nativePlace ? {} : {}, {
    A: $data.selectedProvince
  }, $data.selectedProvince ? {
    B: common_vendor.t($data.selectedProvince.name)
  } : {}, {
    C: $data.form.nativePlaceError ? 1 : "",
    D: $data.provinceList,
    E: common_vendor.o((...args) => $options.onProvinceChange && $options.onProvinceChange(...args)),
    F: !$data.form.phone || $data.form.phone.trim() === ""
  }, !$data.form.phone || $data.form.phone.trim() === "" ? {} : {}, {
    G: $data.form.phoneError || $data.phoneError ? 1 : "",
    H: common_vendor.o([($event) => $data.form.phone = $event.detail.value, (...args) => $options.onPhoneInput && $options.onPhoneInput(...args)]),
    I: common_vendor.o((...args) => $options.validatePhoneRequired && $options.validatePhoneRequired(...args)),
    J: $data.form.phone,
    K: $data.phoneError
  }, $data.phoneError ? {
    L: common_vendor.t($data.phoneError)
  } : {}, {
    M: common_vendor.t($data.form.avatar ? "重新选择" : "选择文件"),
    N: common_vendor.t($data.form.avatar ? "已选择文件" : "未选择任何文件"),
    O: $data.form.avatar
  }, $data.form.avatar ? {} : {}, {
    P: common_vendor.o((...args) => $options.uploadImage && $options.uploadImage(...args)),
    Q: common_vendor.o([($event) => $data.form.selfIntroduction = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    R: $data.form.selfIntroduction,
    S: common_vendor.t(($data.form.selfIntroduction || "").length),
    T: $options.getImagePath("personal_info2.png"),
    U: common_vendor.t($data.modulePointsEarned.education ? "已获得50积分" : "全部完善+50积分"),
    V: $data.moduleStatus.education ? 1 : "",
    W: $data.modulePointsEarned.education ? 1 : "",
    X: !$data.form.school || $data.form.school.trim() === ""
  }, !$data.form.school || $data.form.school.trim() === "" ? {} : {}, {
    Y: $data.form.schoolError ? 1 : "",
    Z: common_vendor.o([($event) => $data.form.school = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    aa: common_vendor.o((...args) => $options.validateSchool && $options.validateSchool(...args)),
    ab: $data.form.school,
    ac: common_vendor.o([($event) => $data.form.college = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    ad: $data.form.college,
    ae: $data.form.graduationYear
  }, $data.form.graduationYear ? {
    af: common_vendor.t($data.form.graduationYear)
  } : {}, {
    ag: $data.graduationYears,
    ah: common_vendor.o((...args) => $options.onGraduationYearChange && $options.onGraduationYearChange(...args)),
    ai: !$data.form.major || $data.form.major.trim() === ""
  }, !$data.form.major || $data.form.major.trim() === "" ? {} : {}, {
    aj: $data.form.majorError ? 1 : "",
    ak: common_vendor.o([($event) => $data.form.major = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    al: common_vendor.o((...args) => $options.validateMajor && $options.validateMajor(...args)),
    am: $data.form.major,
    an: $options.getImagePath("personal_info3.png"),
    ao: common_vendor.t($data.modulePointsEarned.career ? "已获得100积分" : "全部完善+100积分"),
    ap: $data.moduleStatus.career ? 1 : "",
    aq: $data.modulePointsEarned.career ? 1 : "",
    ar: !$data.form.company || $data.form.company.trim() === ""
  }, !$data.form.company || $data.form.company.trim() === "" ? {} : {}, {
    as: $data.form.companyError || $data.companyError ? 1 : "",
    at: common_vendor.o([($event) => $data.form.company = $event.detail.value, (...args) => $options.onCompanyInput && $options.onCompanyInput(...args)]),
    av: common_vendor.o((...args) => $options.validateCompany && $options.validateCompany(...args)),
    aw: $data.form.company,
    ax: $data.companyError
  }, $data.companyError ? {
    ay: common_vendor.t($data.companyError)
  } : {}, {
    az: $data.form.positionType
  }, $data.form.positionType ? {
    aA: common_vendor.t($data.form.positionType)
  } : {}, {
    aB: $data.positionTypeOptions,
    aC: common_vendor.o((...args) => $options.onPositionTypeChange && $options.onPositionTypeChange(...args)),
    aD: common_vendor.o([($event) => $data.form.positionName = $event.detail.value, (...args) => $options.checkModuleCompletion && $options.checkModuleCompletion(...args)]),
    aE: $data.form.positionName,
    aF: !$data.form.industry
  }, !$data.form.industry ? {} : {}, {
    aG: $options.selectedIndustryText
  }, $options.selectedIndustryText ? {
    aH: common_vendor.t($options.selectedIndustryText)
  } : {}, {
    aI: $data.form.industryError ? 1 : "",
    aJ: common_vendor.o((...args) => $options.showIndustryPicker && $options.showIndustryPicker(...args)),
    aK: common_vendor.t($options.earnedPoints),
    aL: !$options.canSubmit ? 1 : "",
    aM: !$options.canSubmit,
    aN: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args)),
    aO: $data.showIndustryModal
  }, $data.showIndustryModal ? {
    aP: common_vendor.o((...args) => $options.hideIndustryPicker && $options.hideIndustryPicker(...args)),
    aQ: common_vendor.o((...args) => $options.confirmIndustrySelection && $options.confirmIndustrySelection(...args)),
    aR: common_vendor.f($data.industryData, (level1, k0, i0) => {
      return common_vendor.e({
        a: $options.canSelectLevel1(level1)
      }, $options.canSelectLevel1(level1) ? {
        b: common_vendor.t(level1.expanded ? "▼" : "▶"),
        c: level1.expanded ? 1 : ""
      } : {}, {
        d: common_vendor.t(level1.name),
        e: !$options.canSelectLevel1(level1)
      }, !$options.canSelectLevel1(level1) ? {} : {}, {
        f: !$options.canSelectLevel1(level1) ? 1 : "",
        g: common_vendor.o(($event) => $options.toggleLevel1(level1), level1.id),
        h: level1.expanded
      }, level1.expanded ? common_vendor.e({
        i: level1.hasStreamType === "1"
      }, level1.hasStreamType === "1" ? {
        j: common_vendor.f(["上游", "中游", "下游"], (categoryName, k1, i1) => {
          return common_vendor.e({
            a: $options.getLevel2ByStream(level1, categoryName).length > 0
          }, $options.getLevel2ByStream(level1, categoryName).length > 0 ? {
            b: common_vendor.t(categoryName),
            c: common_vendor.f($options.getLevel2ByStream(level1, categoryName), (level2, k2, i2) => {
              return common_vendor.e({
                a: $options.canSelectLevel2(level2)
              }, $options.canSelectLevel2(level2) ? {
                b: common_vendor.t(level2.expanded ? "▼" : "▶"),
                c: level2.expanded ? 1 : ""
              } : {}, {
                d: common_vendor.t(level2.name),
                e: !$options.canSelectLevel2(level2)
              }, !$options.canSelectLevel2(level2) ? {} : {}, {
                f: !$options.canSelectLevel2(level2) ? 1 : "",
                g: common_vendor.o(($event) => $options.toggleLevel2(level2), level2.id),
                h: level2.expanded
              }, level2.expanded ? {
                i: common_vendor.f(level2.children, (level3, k3, i3) => {
                  return common_vendor.e({
                    a: common_vendor.t(level3.name),
                    b: $options.isLevel3Selected(level3)
                  }, $options.isLevel3Selected(level3) ? {} : {}, {
                    c: $options.isLevel3Selected(level3) ? 1 : "",
                    d: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                    e: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                    f: level3.id
                  });
                })
              } : {}, {
                j: level2.id
              });
            })
          } : {}, {
            d: categoryName
          });
        })
      } : {
        k: common_vendor.f(level1.children, (level2, k1, i1) => {
          return common_vendor.e({
            a: $options.canSelectLevel2(level2)
          }, $options.canSelectLevel2(level2) ? {
            b: common_vendor.t(level2.expanded ? "▼" : "▶"),
            c: level2.expanded ? 1 : ""
          } : {}, {
            d: common_vendor.t(level2.name),
            e: !$options.canSelectLevel2(level2)
          }, !$options.canSelectLevel2(level2) ? {} : {}, {
            f: !$options.canSelectLevel2(level2) ? 1 : "",
            g: common_vendor.o(($event) => $options.toggleLevel2(level2), level2.id),
            h: level2.expanded
          }, level2.expanded ? {
            i: common_vendor.f(level2.children, (level3, k2, i2) => {
              return common_vendor.e({
                a: common_vendor.t(level3.name),
                b: $options.isLevel3Selected(level3)
              }, $options.isLevel3Selected(level3) ? {} : {}, {
                c: $options.isLevel3Selected(level3) ? 1 : "",
                d: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                e: common_vendor.o(($event) => $options.selectLevel3(level3), level3.id),
                f: level3.id
              });
            })
          } : {}, {
            j: level2.id
          });
        })
      }) : {}, {
        l: level1.id
      });
    }),
    aS: common_vendor.o(() => {
    }),
    aT: common_vendor.o((...args) => $options.hideIndustryPicker && $options.hideIndustryPicker(...args))
  } : {}, {
    aU: common_vendor.o($options.onCropConfirm),
    aV: common_vendor.o($options.onCropCancel),
    aW: common_vendor.p({
      show: $data.showImageCropper,
      imageSrc: $data.selectedImagePath,
      cropSize: 400
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/profile.js.map
